#!/bin/bash

# Script cập nhật tất cả services khi đổi vị trí CAPEv2
# Sử dụng: sudo ./update_cape_services.sh /mnt/CAPEv2

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

function print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

function print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

function print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

function print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Cấu hình
NEW_CAPE_PATH="${1:-/mnt/CAPEv2}"
OLD_CAPE_PATH="/opt/CAPEv2"
BACKUP_DIR="/tmp/cape_services_backup_$(date +%Y%m%d_%H%M%S)"

print_info "=== Cập nhật CAPEv2 Services ==="
print_info "Từ: $OLD_CAPE_PATH"
print_info "Sang: $NEW_CAPE_PATH"

# Kiểm tra quyền root
if [ "$EUID" -ne 0 ]; then
    print_error "Script này cần chạy với quyền root"
    exit 1
fi

# Kiểm tra đường dẫn mới
if [ ! -d "$NEW_CAPE_PATH" ]; then
    print_error "Thư mục $NEW_CAPE_PATH không tồn tại"
    exit 1
fi

# Tạo backup directory
mkdir -p "$BACKUP_DIR"
print_info "Backup directory: $BACKUP_DIR"

print_step "1. Dừng tất cả CAPE services"
CAPE_SERVICES=(
    "cape"
    "cape-processor" 
    "cape-web"
    "cape-rooter"
    "cape-dist"
    "suricata"
    "guacd"
    "guac-web"
)

for service in "${CAPE_SERVICES[@]}"; do
    if systemctl is-active --quiet "$service" 2>/dev/null; then
        print_info "Dừng service: $service"
        systemctl stop "$service"
    fi
done

print_step "2. Backup systemd service files"
SYSTEMD_FILES=(
    "/lib/systemd/system/cape.service"
    "/lib/systemd/system/cape-processor.service"
    "/lib/systemd/system/cape-web.service"
    "/lib/systemd/system/cape-rooter.service"
    "/lib/systemd/system/cape-dist.service"
    "/lib/systemd/system/suricata.service"
    "/lib/systemd/system/guacd.service"
    "/lib/systemd/system/guac-web.service"
    "/lib/systemd/system/cape-fstab.service"
)

for file in "${SYSTEMD_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_info "Backup: $file"
        cp "$file" "$BACKUP_DIR/"
    fi
done

print_step "3. Cập nhật systemd service files"

# Cập nhật cape.service
if [ -f "/lib/systemd/system/cape.service" ]; then
    print_info "Cập nhật cape.service"
    sed -i "s|WorkingDirectory=$OLD_CAPE_PATH/|WorkingDirectory=$NEW_CAPE_PATH/|g" /lib/systemd/system/cape.service
fi

# Cập nhật cape-processor.service
if [ -f "/lib/systemd/system/cape-processor.service" ]; then
    print_info "Cập nhật cape-processor.service"
    sed -i "s|WorkingDirectory=$OLD_CAPE_PATH/utils/|WorkingDirectory=$NEW_CAPE_PATH/utils/|g" /lib/systemd/system/cape-processor.service
fi

# Cập nhật cape-web.service
if [ -f "/lib/systemd/system/cape-web.service" ]; then
    print_info "Cập nhật cape-web.service"
    sed -i "s|WorkingDirectory=$OLD_CAPE_PATH/web|WorkingDirectory=$NEW_CAPE_PATH/web|g" /lib/systemd/system/cape-web.service
fi

# Cập nhật cape-rooter.service
if [ -f "/lib/systemd/system/cape-rooter.service" ]; then
    print_info "Cập nhật cape-rooter.service"
    sed -i "s|WorkingDirectory=$OLD_CAPE_PATH/utils/|WorkingDirectory=$NEW_CAPE_PATH/utils/|g" /lib/systemd/system/cape-rooter.service
    sed -i "s|ExecStartPre=/etc/poetry/bin/poetry config cache-dir $OLD_CAPE_PATH/.cache/pypoetry|ExecStartPre=/etc/poetry/bin/poetry config cache-dir $NEW_CAPE_PATH/.cache/pypoetry|g" /lib/systemd/system/cape-rooter.service
fi

# Cập nhật cape-dist.service (nếu có)
if [ -f "/lib/systemd/system/cape-dist.service" ]; then
    print_info "Cập nhật cape-dist.service"
    sed -i "s|$OLD_CAPE_PATH|$NEW_CAPE_PATH|g" /lib/systemd/system/cape-dist.service
fi

# Cập nhật guac services
for service in "guacd.service" "guac-web.service"; do
    if [ -f "/lib/systemd/system/$service" ]; then
        print_info "Cập nhật $service"
        sed -i "s|$OLD_CAPE_PATH|$NEW_CAPE_PATH|g" "/lib/systemd/system/$service"
    fi
done

print_step "4. Cập nhật crontab"
print_info "Backup crontab hiện tại"
crontab -l > "$BACKUP_DIR/crontab.backup" 2>/dev/null || echo "No crontab found"

print_info "Cập nhật crontab paths"
crontab -l 2>/dev/null | sed "s|$OLD_CAPE_PATH|$NEW_CAPE_PATH|g" | crontab - || true

print_step "5. Cập nhật UWSGI config"
UWSGI_FILES=(
    "/etc/uwsgi/apps-available/cape_dist.ini"
    "/etc/uwsgi/apps-enabled/cape_dist.ini"
)

for file in "${UWSGI_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_info "Backup và cập nhật: $file"
        cp "$file" "$BACKUP_DIR/"
        sed -i "s|$OLD_CAPE_PATH|$NEW_CAPE_PATH|g" "$file"
    fi
done

print_step "6. Cập nhật Nginx config"
NGINX_DIRS=(
    "/etc/nginx/sites-available"
    "/etc/nginx/sites-enabled"
    "/etc/nginx/conf.d"
)

for dir in "${NGINX_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        find "$dir" -name "*.conf" -o -name "*cape*" | while read -r file; do
            if grep -q "$OLD_CAPE_PATH" "$file" 2>/dev/null; then
                print_info "Cập nhật Nginx config: $file"
                cp "$file" "$BACKUP_DIR/"
                sed -i "s|$OLD_CAPE_PATH|$NEW_CAPE_PATH|g" "$file"
            fi
        done
    fi
done

print_step "7. Cập nhật AppArmor profiles"
APPARMOR_FILES=(
    "/etc/apparmor.d/local/usr.sbin.clamd"
    "/etc/apparmor.d/usr.sbin.clamd"
)

for file in "${APPARMOR_FILES[@]}"; do
    if [ -f "$file" ] && grep -q "$OLD_CAPE_PATH" "$file" 2>/dev/null; then
        print_info "Cập nhật AppArmor: $file"
        cp "$file" "$BACKUP_DIR/"
        sed -i "s|$OLD_CAPE_PATH|$NEW_CAPE_PATH|g" "$file"
    fi
done

# Reload AppArmor
if command -v apparmor_parser >/dev/null 2>&1; then
    apparmor_parser -r /etc/apparmor.d/usr.sbin.clamd 2>/dev/null || true
fi

print_step "8. Cập nhật logrotate config"
LOGROTATE_FILES=(
    "/etc/logrotate.d/cape"
    "/etc/logrotate.d/cape-mnt"
)

for file in "${LOGROTATE_FILES[@]}"; do
    if [ -f "$file" ] && grep -q "$OLD_CAPE_PATH" "$file" 2>/dev/null; then
        print_info "Cập nhật logrotate: $file"
        cp "$file" "$BACKUP_DIR/"
        sed -i "s|$OLD_CAPE_PATH|$NEW_CAPE_PATH|g" "$file"
    fi
done

print_step "9. Cập nhật sudoers"
SUDOERS_FILES=(
    "/etc/sudoers.d/cape"
    "/etc/sudoers.d/ip_netns"
)

for file in "${SUDOERS_FILES[@]}"; do
    if [ -f "$file" ] && grep -q "$OLD_CAPE_PATH" "$file" 2>/dev/null; then
        print_info "Cập nhật sudoers: $file"
        cp "$file" "$BACKUP_DIR/"
        sed -i "s|$OLD_CAPE_PATH|$NEW_CAPE_PATH|g" "$file"
    fi
done

print_step "10. Reload systemd và restart services"
print_info "Reload systemd daemon"
systemctl daemon-reload

print_info "Restart services..."
for service in "${CAPE_SERVICES[@]}"; do
    if [ -f "/lib/systemd/system/$service.service" ]; then
        print_info "Khởi động service: $service"
        systemctl enable "$service" 2>/dev/null || true
        systemctl start "$service" 2>/dev/null || print_warning "Không thể khởi động $service"
    fi
done

print_step "11. Kiểm tra status"
sleep 5
print_info "Kiểm tra status các services:"
for service in "${CAPE_SERVICES[@]}"; do
    if systemctl is-active --quiet "$service" 2>/dev/null; then
        print_info "✅ $service: RUNNING"
    else
        print_warning "❌ $service: STOPPED"
    fi
done

print_step "12. Tạo symbolic link"
if [ ! -L "$OLD_CAPE_PATH" ]; then
    print_info "Tạo symbolic link: $OLD_CAPE_PATH -> $NEW_CAPE_PATH"
    ln -sf "$NEW_CAPE_PATH" "$OLD_CAPE_PATH"
fi

print_info "=== Cập nhật hoàn tất! ==="
print_info ""
print_info "Backup files tại: $BACKUP_DIR"
print_info "New CAPE path: $NEW_CAPE_PATH"
print_info "Symbolic link: $OLD_CAPE_PATH -> $NEW_CAPE_PATH"
print_info ""
print_info "Kiểm tra logs:"
print_info "  journalctl -u cape -f"
print_info "  journalctl -u cape-processor -f"
print_info "  journalctl -u cape-web -f"
print_info ""
print_warning "Nếu có lỗi, restore từ: $BACKUP_DIR"
