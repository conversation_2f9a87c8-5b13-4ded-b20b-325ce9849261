#!/bin/bash

# Script để thay đổi vị trí cài đặt CAPEv2
# Sử dụng: ./change_cape_location.sh /path/to/new/location

set -e

# Màu sắc cho output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

function print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

function print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

function print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Kiểm tra tham số
if [ $# -ne 1 ]; then
    print_error "Sử dụng: $0 <đường_dẫn_mới>"
    echo "Ví dụ:"
    echo "  $0 /home/<USER>/CAPEv2"
    echo "  $0 /data/CAPEv2"
    echo "  $0 /srv/CAPEv2"
    exit 1
fi

NEW_CAPE_PATH="$1"
OLD_CAPE_PATH="/opt/CAPEv2"
CURRENT_DIR=$(pwd)

print_info "=== Thay đổi vị trí CAPEv2 từ $OLD_CAPE_PATH sang $NEW_CAPE_PATH ==="

# Kiểm tra quyền
if [ "$EUID" -ne 0 ]; then
    print_error "Script này cần chạy với quyền root (sudo)"
    exit 1
fi

# Kiểm tra đường dẫn mới
if [ -e "$NEW_CAPE_PATH" ]; then
    print_warning "Đường dẫn $NEW_CAPE_PATH đã tồn tại"
    read -p "Bạn có muốn tiếp tục? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Tạo thư mục mới
print_info "Tạo thư mục $NEW_CAPE_PATH"
mkdir -p "$NEW_CAPE_PATH"

# Copy hoặc move CAPEv2
if [ -d "$OLD_CAPE_PATH" ]; then
    print_info "Di chuyển CAPEv2 từ $OLD_CAPE_PATH sang $NEW_CAPE_PATH"
    cp -r "$OLD_CAPE_PATH"/* "$NEW_CAPE_PATH/"
    
    # Backup old directory
    print_info "Backup thư mục cũ thành $OLD_CAPE_PATH.backup"
    mv "$OLD_CAPE_PATH" "$OLD_CAPE_PATH.backup"
else
    print_info "Thư mục $OLD_CAPE_PATH không tồn tại, clone CAPEv2 mới"
    cd "$(dirname "$NEW_CAPE_PATH")"
    git clone https://github.com/kevoreilly/CAPEv2/ "$(basename "$NEW_CAPE_PATH")"
fi

# Cập nhật ownership
USER_NAME=${SUDO_USER:-cape}
print_info "Cập nhật ownership cho user: $USER_NAME"
chown -R "$USER_NAME:$USER_NAME" "$NEW_CAPE_PATH"

# Cập nhật các file cấu hình
print_info "Cập nhật các file cấu hình..."

# 1. Systemd services
SYSTEMD_FILES=(
    "/lib/systemd/system/cape.service"
    "/lib/systemd/system/cape-processor.service"
    "/lib/systemd/system/cape-web.service"
    "/lib/systemd/system/cape-rooter.service"
    "/lib/systemd/system/suricata.service"
    "/lib/systemd/system/guacd.service"
    "/lib/systemd/system/guac-web.service"
)

for file in "${SYSTEMD_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_info "Cập nhật $file"
        sed -i "s|$OLD_CAPE_PATH|$NEW_CAPE_PATH|g" "$file"
    fi
done

# 2. Crontab
print_info "Cập nhật crontab"
crontab -l | sed "s|$OLD_CAPE_PATH|$NEW_CAPE_PATH|g" | crontab -

# 3. AppArmor (nếu có)
if [ -f "/etc/apparmor.d/local/usr.sbin.clamd" ]; then
    print_info "Cập nhật AppArmor config"
    sed -i "s|$OLD_CAPE_PATH|$NEW_CAPE_PATH|g" /etc/apparmor.d/local/usr.sbin.clamd
    apparmor_parser -r /etc/apparmor.d/usr.sbin.clamd 2>/dev/null || true
fi

# 4. UWSGI config (nếu có)
if [ -f "/etc/uwsgi/apps-available/cape_dist.ini" ]; then
    print_info "Cập nhật UWSGI config"
    sed -i "s|$OLD_CAPE_PATH|$NEW_CAPE_PATH|g" /etc/uwsgi/apps-available/cape_dist.ini
fi

# 5. Nginx config (nếu có)
NGINX_CONFIGS=$(find /etc/nginx -name "*.conf" -type f 2>/dev/null || true)
for config in $NGINX_CONFIGS; do
    if grep -q "$OLD_CAPE_PATH" "$config" 2>/dev/null; then
        print_info "Cập nhật Nginx config: $config"
        sed -i "s|$OLD_CAPE_PATH|$NEW_CAPE_PATH|g" "$config"
    fi
done

# 6. Tạo symlink để backward compatibility
print_info "Tạo symbolic link từ $OLD_CAPE_PATH -> $NEW_CAPE_PATH"
ln -sf "$NEW_CAPE_PATH" "$OLD_CAPE_PATH"

# Reload systemd
print_info "Reload systemd daemon"
systemctl daemon-reload

print_info "=== Hoàn thành! ==="
print_info "CAPEv2 đã được di chuyển sang: $NEW_CAPE_PATH"
print_info "Symbolic link: $OLD_CAPE_PATH -> $NEW_CAPE_PATH"
print_info ""
print_info "Các bước tiếp theo:"
print_info "1. Kiểm tra cấu hình: cd $NEW_CAPE_PATH && ls -la"
print_info "2. Cài đặt dependencies: cd $NEW_CAPE_PATH && poetry install"
print_info "3. Khởi động services: systemctl start cape cape-processor cape-web"
print_info "4. Kiểm tra logs: journalctl -u cape -f"
print_info ""
print_warning "Lưu ý: Backup cũ tại $OLD_CAPE_PATH.backup"
