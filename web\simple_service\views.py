import os
import json
import time
import tempfile
from pathlib import Path
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile

# Import CAPEv2 components
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'lib'))

from cuckoo.core.database import Database, TASK_PENDING, TASK_RUNNING, TASK_COMPLETED, TASK_REPORTED
from cuckoo.common.config import Config


class SimpleAnalysisService:
    """Synchronous file analysis service"""
    
    def __init__(self):
        self.db = Database()
        self.config = Config()
        
    def submit_and_wait(self, file_path, options=None, timeout=300, max_wait=1800):
        """
        Submit file for analysis and wait for completion
        
        Args:
            file_path: Path to file to analyze
            options: Analysis options dict
            timeout: Analysis timeout in seconds
            max_wait: Maximum time to wait for completion in seconds
            
        Returns:
            dict: Analysis results or error
        """
        try:
            # Submit task to database
            task_id = self.db.add_path(
                file_path=file_path,
                timeout=timeout,
                options=options or {},
                priority=1
            )
            
            if not task_id:
                return {"error": True, "message": "Failed to submit task"}
            
            # Wait for analysis completion
            start_time = time.time()
            while time.time() - start_time < max_wait:
                task = self.db.view_task(task_id)
                if not task:
                    return {"error": True, "message": "Task not found"}
                
                status = task.status
                
                if status == TASK_COMPLETED:
                    # Wait a bit more for processing to complete
                    time.sleep(5)
                    break
                elif status == TASK_REPORTED:
                    break
                elif status in ["failed_analysis", "failed_processing", "failed_reporting"]:
                    return {"error": True, "message": f"Analysis failed with status: {status}"}
                
                time.sleep(2)  # Poll every 2 seconds
            else:
                return {"error": True, "message": "Analysis timeout - task may still be running"}
            
            # Get analysis results
            return self.get_results(task_id)
            
        except Exception as e:
            return {"error": True, "message": f"Analysis error: {str(e)}"}
    
    def get_results(self, task_id):
        """Get analysis results for a task"""
        try:
            # Path to analysis results
            analysis_path = os.path.join(
                self.config.cuckoo.get("resultserver", {}).get("store_csvlogs", "/tmp/cuckoo-tmp"),
                "..", "storage", "analyses", str(task_id)
            )
            
            # Try to get the report.json
            report_path = os.path.join(analysis_path, "reports", "report.json")
            if os.path.exists(report_path):
                with open(report_path, 'r') as f:
                    results = json.load(f)
                return {"error": False, "task_id": task_id, "results": results}
            
            # If no report.json, return basic task info
            task = self.db.view_task(task_id)
            if task:
                return {
                    "error": False, 
                    "task_id": task_id,
                    "status": task.status,
                    "message": "Analysis completed but report not yet available"
                }
            
            return {"error": True, "message": "No results found"}
            
        except Exception as e:
            return {"error": True, "message": f"Error retrieving results: {str(e)}"}


# Global service instance
analysis_service = SimpleAnalysisService()


@csrf_exempt
@require_http_methods(["POST"])
def analyze_file(request):
    """
    Endpoint to submit file and get analysis results synchronously
    
    POST /simple/analyze/
    Form data:
        - file: File to analyze
        - timeout: Analysis timeout (optional, default 300)
        - max_wait: Maximum wait time (optional, default 1800)
        - options: JSON string of analysis options (optional)
    
    Returns:
        JSON response with analysis results
    """
    if 'file' not in request.FILES:
        return JsonResponse({"error": True, "message": "No file provided"})
    
    uploaded_file = request.FILES['file']
    
    # Get optional parameters
    timeout = int(request.POST.get('timeout', 300))
    max_wait = int(request.POST.get('max_wait', 1800))
    options_str = request.POST.get('options', '{}')
    
    try:
        options = json.loads(options_str) if options_str else {}
    except json.JSONDecodeError:
        return JsonResponse({"error": True, "message": "Invalid options JSON"})
    
    # Save uploaded file to temporary location
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{uploaded_file.name}") as tmp_file:
            for chunk in uploaded_file.chunks():
                tmp_file.write(chunk)
            tmp_file_path = tmp_file.name
        
        # Submit for analysis and wait for results
        result = analysis_service.submit_and_wait(
            file_path=tmp_file_path,
            options=options,
            timeout=timeout,
            max_wait=max_wait
        )
        
        # Clean up temporary file
        try:
            os.unlink(tmp_file_path)
        except:
            pass
        
        return JsonResponse(result)
        
    except Exception as e:
        return JsonResponse({"error": True, "message": f"Processing error: {str(e)}"})


@csrf_exempt
@require_http_methods(["GET"])
def get_task_status(request, task_id):
    """
    Get status of a specific task
    
    GET /simple/status/<task_id>/
    """
    try:
        task = analysis_service.db.view_task(task_id)
        if not task:
            return JsonResponse({"error": True, "message": "Task not found"})
        
        return JsonResponse({
            "error": False,
            "task_id": task_id,
            "status": task.status,
            "target": task.target,
            "added_on": task.added_on.isoformat() if task.added_on else None,
            "started_on": task.started_on.isoformat() if task.started_on else None,
            "completed_on": task.completed_on.isoformat() if task.completed_on else None
        })
        
    except Exception as e:
        return JsonResponse({"error": True, "message": f"Error: {str(e)}"})


@csrf_exempt
@require_http_methods(["GET"])
def get_task_results(request, task_id):
    """
    Get results for a specific task
    
    GET /simple/results/<task_id>/
    """
    try:
        result = analysis_service.get_results(task_id)
        return JsonResponse(result)
        
    except Exception as e:
        return JsonResponse({"error": True, "message": f"Error: {str(e)}"})


def index(request):
    """Simple HTML interface for testing"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>CAPEv2 Simple Analysis Service</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; }
            .form-group { margin: 20px 0; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input, textarea, select { width: 100%; padding: 8px; margin-bottom: 10px; }
            button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; }
            button:hover { background: #005a87; }
            .result { margin-top: 20px; padding: 15px; background: #f5f5f5; border-radius: 5px; }
            .error { background: #ffebee; color: #c62828; }
            .success { background: #e8f5e8; color: #2e7d32; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>CAPEv2 Simple Analysis Service</h1>
            <p>Upload a file for synchronous malware analysis</p>
            
            <form id="analysisForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="file">File to analyze:</label>
                    <input type="file" id="file" name="file" required>
                </div>
                
                <div class="form-group">
                    <label for="timeout">Analysis timeout (seconds):</label>
                    <input type="number" id="timeout" name="timeout" value="300" min="60" max="3600">
                </div>
                
                <div class="form-group">
                    <label for="max_wait">Maximum wait time (seconds):</label>
                    <input type="number" id="max_wait" name="max_wait" value="1800" min="300" max="7200">
                </div>
                
                <div class="form-group">
                    <label for="options">Analysis options (JSON):</label>
                    <textarea id="options" name="options" rows="3" placeholder='{"option1": "value1"}'>{}</textarea>
                </div>
                
                <button type="submit">Analyze File</button>
            </form>
            
            <div id="result" class="result" style="display: none;"></div>
        </div>
        
        <script>
            document.getElementById('analysisForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                const resultDiv = document.getElementById('result');
                
                resultDiv.innerHTML = 'Analyzing file... This may take several minutes.';
                resultDiv.className = 'result';
                resultDiv.style.display = 'block';
                
                try {
                    const response = await fetch('/simple/analyze/', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const data = await response.json();
                    
                    if (data.error) {
                        resultDiv.innerHTML = `<strong>Error:</strong> ${data.message}`;
                        resultDiv.className = 'result error';
                    } else {
                        resultDiv.innerHTML = `<strong>Analysis Complete!</strong><br>
                                             Task ID: ${data.task_id}<br>
                                             <pre>${JSON.stringify(data.results || data, null, 2)}</pre>`;
                        resultDiv.className = 'result success';
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
                    resultDiv.className = 'result error';
                }
            });
        </script>
    </body>
    </html>
    """
    return HttpResponse(html)
