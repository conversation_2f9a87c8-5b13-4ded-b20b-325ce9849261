# Fix CAPEv2 Permissions - Logs Folder Missing

## Vấn đề: Permissions và quyền truy cập
Mặc dù agent có thể kết nối được result server, nhưng CAPEv2 không thể tạo/ghi logs folder do vấn đề permissions.

## BƯỚC 1: Ki<PERSON>m tra permissions hiện tại

### 1.1. Script kiểm tra permissions toàn diện
```bash
#!/bin/bash
# File: check_capev2_permissions.sh

CAPE_DIR="/opt/CAPEv2"

echo "=== CHECKING CAPEV2 PERMISSIONS ==="

# 1. Check CAPEv2 directory ownership
echo "1. CAPEv2 Directory Ownership:"
ls -la /opt/ | grep CAPEv2
echo ""

# 2. Check storage directory permissions
echo "2. Storage Directory Permissions:"
if [ -d "$CAPE_DIR/storage" ]; then
    ls -la $CAPE_DIR/storage/
    echo ""
    echo "Analyses directory:"
    ls -la $CAPE_DIR/storage/analyses/ 2>/dev/null | head -10
else
    echo "❌ Storage directory doesn't exist!"
fi

# 3. Check CAPEv2 process user
echo ""
echo "3. CAPEv2 Process User:"
ps aux | grep -E "(cuckoo|cape)" | grep -v grep

# 4. Check who can write to storage
echo ""
echo "4. Write Permissions Test:"
current_user=$(whoami)
echo "Current user: $current_user"

# Test write to storage
if [ -w "$CAPE_DIR/storage" ]; then
    echo "✅ Current user can write to storage"
else
    echo "❌ Current user CANNOT write to storage"
fi

# Test write to analyses
if [ -w "$CAPE_DIR/storage/analyses" ]; then
    echo "✅ Current user can write to analyses"
else
    echo "❌ Current user CANNOT write to analyses"
fi

# 5. Check systemd service user
echo ""
echo "5. Systemd Service Configuration:"
if systemctl is-active cape >/dev/null 2>&1; then
    echo "CAPE service is running"
    systemctl show cape | grep -E "(User|Group|ExecStart)"
else
    echo "CAPE service is not running"
fi

# 6. Check recent analysis directories permissions
echo ""
echo "6. Recent Analysis Directories:"
if [ -d "$CAPE_DIR/storage/analyses" ]; then
    for analysis_dir in $(ls -1t $CAPE_DIR/storage/analyses/ | head -3); do
        echo "Analysis $analysis_dir:"
        ls -la $CAPE_DIR/storage/analyses/$analysis_dir/ 2>/dev/null || echo "  Cannot access directory"
    done
fi

echo ""
echo "=== PERMISSIONS CHECK COMPLETED ==="
```

### 1.2. Test tạo thư mục logs
```bash
#!/bin/bash
# File: test_create_logs.sh

CAPE_DIR="/opt/CAPEv2"

echo "=== TESTING LOGS DIRECTORY CREATION ==="

# 1. Test tạo analysis directory
echo "1. Testing analysis directory creation..."
test_analysis_id="test_$(date +%s)"
test_dir="$CAPE_DIR/storage/analyses/$test_analysis_id"

if mkdir -p "$test_dir" 2>/dev/null; then
    echo "✅ Can create analysis directory: $test_dir"
    
    # Test tạo logs subdirectory
    logs_dir="$test_dir/logs"
    if mkdir -p "$logs_dir" 2>/dev/null; then
        echo "✅ Can create logs directory: $logs_dir"
        
        # Test tạo file trong logs
        test_file="$logs_dir/test.log"
        if echo "test" > "$test_file" 2>/dev/null; then
            echo "✅ Can create files in logs directory"
            
            # Cleanup
            rm -rf "$test_dir"
            echo "✅ Test cleanup successful"
        else
            echo "❌ CANNOT create files in logs directory"
        fi
    else
        echo "❌ CANNOT create logs directory"
    fi
else
    echo "❌ CANNOT create analysis directory"
    echo "This is the main problem!"
fi

# 2. Check umask
echo ""
echo "2. Current umask:"
umask

# 3. Check disk space
echo ""
echo "3. Disk space:"
df -h $CAPE_DIR

echo ""
echo "=== LOGS CREATION TEST COMPLETED ==="
```

## BƯỚC 2: Fix permissions

### 2.1. Fix ownership và permissions
```bash
#!/bin/bash
# File: fix_capev2_ownership.sh

CAPE_DIR="/opt/CAPEv2"

echo "=== FIXING CAPEV2 OWNERSHIP AND PERMISSIONS ==="

# 1. Determine correct user
echo "1. Determining correct user..."

# Check if cape user exists
if id cape >/dev/null 2>&1; then
    CAPE_USER="cape"
    CAPE_GROUP="cape"
    echo "Using cape user"
elif id cuckoo >/dev/null 2>&1; then
    CAPE_USER="cuckoo"
    CAPE_GROUP="cuckoo"
    echo "Using cuckoo user"
else
    CAPE_USER="$USER"
    CAPE_GROUP="$USER"
    echo "Using current user: $USER"
fi

echo "CAPE_USER: $CAPE_USER"
echo "CAPE_GROUP: $CAPE_GROUP"

# 2. Fix ownership
echo ""
echo "2. Fixing ownership..."
sudo chown -R $CAPE_USER:$CAPE_GROUP $CAPE_DIR
echo "✅ Ownership fixed"

# 3. Fix permissions
echo ""
echo "3. Fixing permissions..."

# Main directory
sudo chmod 755 $CAPE_DIR

# Storage directory
sudo chmod 755 $CAPE_DIR/storage
sudo chmod 755 $CAPE_DIR/storage/analyses

# Binaries
sudo chmod +x $CAPE_DIR/cuckoo.py
sudo chmod +x $CAPE_DIR/utils/*.py

# Config files
sudo chmod 644 $CAPE_DIR/conf/*.conf

# Log directory
sudo mkdir -p $CAPE_DIR/log
sudo chown $CAPE_USER:$CAPE_GROUP $CAPE_DIR/log
sudo chmod 755 $CAPE_DIR/log

echo "✅ Permissions fixed"

# 4. Test permissions
echo ""
echo "4. Testing permissions..."
sudo -u $CAPE_USER mkdir -p $CAPE_DIR/storage/analyses/test_permissions/logs
if [ $? -eq 0 ]; then
    echo "✅ Can create directories as $CAPE_USER"
    sudo -u $CAPE_USER rmdir $CAPE_DIR/storage/analyses/test_permissions/logs
    sudo -u $CAPE_USER rmdir $CAPE_DIR/storage/analyses/test_permissions
else
    echo "❌ Still cannot create directories as $CAPE_USER"
fi

echo ""
echo "=== OWNERSHIP AND PERMISSIONS FIX COMPLETED ==="
```

### 2.2. Fix systemd service user
```bash
#!/bin/bash
# File: fix_systemd_service.sh

echo "=== FIXING SYSTEMD SERVICE USER ==="

# 1. Determine CAPE user
if id cape >/dev/null 2>&1; then
    CAPE_USER="cape"
elif id cuckoo >/dev/null 2>&1; then
    CAPE_USER="cuckoo"
else
    CAPE_USER="$USER"
fi

echo "Using CAPE user: $CAPE_USER"

# 2. Update systemd service
echo ""
echo "2. Updating systemd service..."

# Backup existing service
sudo cp /etc/systemd/system/cape.service /etc/systemd/system/cape.service.backup 2>/dev/null || true

# Create/update service file
sudo tee /etc/systemd/system/cape.service << EOF
[Unit]
Description=CAPE Sandbox
After=network.target postgresql.service

[Service]
Type=simple
User=$CAPE_USER
Group=$CAPE_USER
WorkingDirectory=/opt/CAPEv2
ExecStart=/usr/bin/python3 /opt/CAPEv2/cuckoo.py
Restart=always
RestartSec=10
Environment=PYTHONPATH=/opt/CAPEv2

[Install]
WantedBy=multi-user.target
EOF

# 3. Update web service if exists
if [ -f "/etc/systemd/system/cape-web.service" ]; then
    echo "Updating web service..."
    sudo tee /etc/systemd/system/cape-web.service << EOF
[Unit]
Description=CAPE Web Interface
After=network.target cape.service

[Service]
Type=simple
User=$CAPE_USER
Group=$CAPE_USER
WorkingDirectory=/opt/CAPEv2
ExecStart=/usr/bin/python3 /opt/CAPEv2/utils/web.py --host 0.0.0.0 --port 8000
Restart=always
RestartSec=10
Environment=PYTHONPATH=/opt/CAPEv2

[Install]
WantedBy=multi-user.target
EOF
fi

# 4. Reload systemd
echo ""
echo "3. Reloading systemd..."
sudo systemctl daemon-reload

# 5. Restart services
echo ""
echo "4. Restarting services..."
sudo systemctl restart cape
sleep 5

if systemctl is-active cape >/dev/null 2>&1; then
    echo "✅ CAPE service restarted successfully"
else
    echo "❌ CAPE service failed to start"
    echo "Check logs: sudo journalctl -u cape -f"
fi

echo ""
echo "=== SYSTEMD SERVICE FIX COMPLETED ==="
```

## BƯỚC 3: Fix SELinux/AppArmor (nếu có)

### 3.1. Check và fix SELinux
```bash
#!/bin/bash
# File: fix_selinux.sh

echo "=== CHECKING AND FIXING SELINUX ==="

# 1. Check if SELinux is enabled
if command -v getenforce >/dev/null 2>&1; then
    selinux_status=$(getenforce)
    echo "SELinux status: $selinux_status"
    
    if [ "$selinux_status" = "Enforcing" ]; then
        echo "SELinux is enforcing - this might cause issues"
        
        # Set SELinux context for CAPEv2
        echo "Setting SELinux context..."
        sudo setsebool -P httpd_exec_enable 1
        sudo semanage fcontext -a -t bin_t "/opt/CAPEv2/cuckoo.py"
        sudo restorecon -v /opt/CAPEv2/cuckoo.py
        
        # Allow network connections
        sudo setsebool -P httpd_can_network_connect 1
        
        echo "✅ SELinux context set"
    else
        echo "✅ SELinux not enforcing"
    fi
else
    echo "✅ SELinux not installed"
fi

# 2. Check AppArmor
if command -v aa-status >/dev/null 2>&1; then
    echo ""
    echo "Checking AppArmor..."
    if aa-status | grep -q python; then
        echo "AppArmor profiles found for Python"
        echo "Consider disabling: sudo aa-disable /usr/bin/python3"
    else
        echo "✅ No AppArmor restrictions on Python"
    fi
else
    echo "✅ AppArmor not installed"
fi

echo ""
echo "=== SELINUX/APPARMOR CHECK COMPLETED ==="
```

### 3.2. Fix temporary directory permissions
```bash
#!/bin/bash
# File: fix_temp_permissions.sh

echo "=== FIXING TEMPORARY DIRECTORY PERMISSIONS ==="

CAPE_DIR="/opt/CAPEv2"

# 1. Check temp directory in config
echo "1. Checking temp directory configuration..."
temp_dir=$(grep "tmppath" $CAPE_DIR/conf/cuckoo.conf | cut -d'=' -f2 | tr -d ' ')
echo "Temp directory: $temp_dir"

# 2. Ensure temp directory exists and has correct permissions
if [ -n "$temp_dir" ] && [ "$temp_dir" != "/tmp" ]; then
    echo "Creating and fixing temp directory: $temp_dir"
    sudo mkdir -p "$temp_dir"
    sudo chmod 1777 "$temp_dir"  # Sticky bit like /tmp
    echo "✅ Temp directory fixed"
else
    echo "Using system /tmp directory"
fi

# 3. Check /tmp permissions
echo ""
echo "2. Checking /tmp permissions..."
ls -ld /tmp
if [ -w /tmp ]; then
    echo "✅ /tmp is writable"
else
    echo "❌ /tmp is not writable - this is unusual"
fi

# 4. Create CAPE-specific temp directory
cape_temp="/tmp/cape"
echo ""
echo "3. Creating CAPE temp directory: $cape_temp"
sudo mkdir -p "$cape_temp"
sudo chmod 777 "$cape_temp"

# Determine CAPE user
if id cape >/dev/null 2>&1; then
    CAPE_USER="cape"
elif id cuckoo >/dev/null 2>&1; then
    CAPE_USER="cuckoo"
else
    CAPE_USER="$USER"
fi

sudo chown $CAPE_USER:$CAPE_USER "$cape_temp"
echo "✅ CAPE temp directory created"

echo ""
echo "=== TEMP DIRECTORY PERMISSIONS FIX COMPLETED ==="
```

## BƯỚC 4: Complete permissions fix

### 4.1. Script fix toàn diện
```bash
#!/bin/bash
# File: complete_permissions_fix.sh

echo "=== COMPLETE CAPEV2 PERMISSIONS FIX ==="

# 1. Check current permissions
echo "Step 1: Checking current permissions..."
./check_capev2_permissions.sh > permissions_check.log
echo "Permissions check saved to: permissions_check.log"

# 2. Fix ownership and permissions
echo ""
echo "Step 2: Fixing ownership and permissions..."
./fix_capev2_ownership.sh

# 3. Fix systemd service
echo ""
echo "Step 3: Fixing systemd service..."
./fix_systemd_service.sh

# 4. Fix SELinux/AppArmor
echo ""
echo "Step 4: Checking SELinux/AppArmor..."
./fix_selinux.sh

# 5. Fix temp directories
echo ""
echo "Step 5: Fixing temp directories..."
./fix_temp_permissions.sh

# 6. Test logs creation
echo ""
echo "Step 6: Testing logs creation..."
./test_create_logs.sh

# 7. Restart CAPEv2
echo ""
echo "Step 7: Restarting CAPEv2..."
sudo systemctl restart cape
sleep 10

# 8. Final verification
echo ""
echo "Step 8: Final verification..."
if systemctl is-active cape >/dev/null 2>&1; then
    echo "✅ CAPE service is running"
    
    # Check if result server is listening
    if netstat -tlnp | grep :2042 >/dev/null; then
        echo "✅ Result server is listening"
    else
        echo "❌ Result server not listening"
    fi
else
    echo "❌ CAPE service failed to start"
    echo "Check logs: sudo journalctl -u cape -f"
fi

echo ""
echo "=== COMPLETE PERMISSIONS FIX COMPLETED ==="
echo ""
echo "Next steps:"
echo "1. Submit test analysis: cd /opt/CAPEv2 && python3 utils/submit.py /bin/ls"
echo "2. Check if logs folder is created in storage/analyses/"
echo "3. Monitor logs: tail -f /opt/CAPEv2/log/cuckoo.log"
```

## CÁCH SỬ DỤNG:

```bash
# 1. Check permissions hiện tại
chmod +x *.sh
./check_capev2_permissions.sh

# 2. Fix toàn diện
sudo ./complete_permissions_fix.sh

# 3. Test tạo logs
./test_create_logs.sh

# 4. Submit test analysis
cd /opt/CAPEv2
python3 utils/submit.py --timeout 60 /bin/ls

# 5. Check results
ls -la storage/analyses/*/logs/
```

## KẾT QUẢ MONG ĐỢI:

✅ **Sau khi fix permissions:**
- CAPEv2 có thể tạo thư mục analyses
- Logs folder được tạo thành công
- Behavior analysis có data
- Không còn permission errors

❌ **Trước khi fix:**
- Permission denied khi tạo logs
- Analysis folder không tạo được
- Behavior analysis rỗng
- Logs folder missing

**Vấn đề permissions thường là nguyên nhân chính!**
