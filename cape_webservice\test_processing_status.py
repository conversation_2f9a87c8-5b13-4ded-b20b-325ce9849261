#!/usr/bin/env python3
"""
Test script for handling "still being analyzed" status
"""

import requests
import time
import json

BASE_URL = "http://localhost:5000"

def test_processing_status():
    """Test handling of processing status"""
    print("🧪 Testing Processing Status Handling")
    print("=" * 50)
    
    # Create a test file that might take longer to analyze
    test_content = "This is a test file for processing status testing\n"
    test_content += "X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*\n"
    test_content += "A" * 10000  # Make it larger to potentially take more time
    
    # Submit analysis
    print("📤 Submitting file for analysis...")
    files = {'file': ('test_processing.txt', test_content)}
    data = {
        'options': 'procmemdump=1,unpacker=2,timeout=300',  # Longer timeout
        'force_reanalyze': 'true'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/analyze", files=files, data=data)
        if response.status_code != 200:
            print(f"❌ Failed to submit: {response.status_code}")
            return
        
        result = response.json()
        if result.get('error'):
            print(f"❌ Submission error: {result.get('message')}")
            return
        
        analysis_id = result['analysis_id']
        print(f"✅ Submitted successfully! Analysis ID: {analysis_id}")
        
    except Exception as e:
        print(f"❌ Error submitting: {e}")
        return
    
    # Monitor status changes with focus on processing
    print("\n🔍 Monitoring status changes (focus on processing)...")
    print("Expected flow: pending -> running -> processing -> reported")
    print("-" * 50)
    
    last_status = None
    status_history = []
    max_checks = 180  # 15 minutes max
    check_count = 0
    processing_detected = False
    
    while check_count < max_checks:
        try:
            response = requests.get(f"{BASE_URL}/api/status/{analysis_id}")
            if response.status_code == 200:
                result = response.json()
                if not result.get('error'):
                    current_status = result['status']
                    
                    if current_status != last_status:
                        timestamp = time.strftime("%H:%M:%S")
                        status_history.append((timestamp, current_status))
                        
                        # Status icons
                        status_icons = {
                            'pending': '⏳',
                            'running': '🔄',
                            'processing': '⚙️',
                            'completed': '✅',
                            'reported': '📄',
                            'failed': '❌'
                        }
                        
                        icon = status_icons.get(current_status, '❓')
                        print(f"{timestamp} - {icon} Status: {current_status}")
                        last_status = current_status
                        
                        # Track if we hit processing status
                        if current_status == 'processing':
                            processing_detected = True
                            print(f"   🎯 Processing status detected! This means CAPE is still working...")
                        
                        # Check if we've reached a final state
                        if current_status in ['reported', 'completed', 'failed']:
                            print(f"\n🎯 Final status reached: {current_status}")
                            break
            
            time.sleep(5)  # Check every 5 seconds
            check_count += 1
            
            # If we're in processing for a while, show progress
            if last_status == 'processing' and check_count % 12 == 0:  # Every minute
                print(f"   ⚙️  Still processing... (check #{check_count})")
            
        except Exception as e:
            print(f"⚠️  Error checking status: {e}")
            time.sleep(5)
            check_count += 1
    
    # Summary
    print("\n📊 Status Flow Summary:")
    print("=" * 30)
    for timestamp, status in status_history:
        print(f"{timestamp} - {status}")
    
    # Analysis
    print("\n🔍 Processing Status Analysis:")
    if processing_detected:
        print("✅ Processing status was correctly detected!")
        print("   This means the web service properly handled the 'still being analyzed' response")
        print("   and set the status to 'processing' instead of failing.")
    else:
        print("⚠️  Processing status was not detected.")
        print("   This could mean:")
        print("   - Analysis completed very quickly")
        print("   - CAPE server didn't return 'still being analyzed' error")
        print("   - The handling logic needs adjustment")
    
    # Check final result
    if last_status in ['reported', 'completed']:
        print(f"\n📄 Checking final report...")
        try:
            response = requests.get(f"{BASE_URL}/api/report/{analysis_id}")
            if response.status_code == 200:
                result = response.json()
                if not result.get('error'):
                    print("✅ Report is available!")
                    report = result['result'].get('report', {})
                    if report:
                        print(f"   Signatures: {len(report.get('signatures', []))}")
                        print(f"   Network hosts: {len(report.get('network', {}).get('hosts', []))}")
                        print(f"   Processes: {len(report.get('behavior', {}).get('processes', []))}")
                    else:
                        print("   Report structure is different than expected")
                else:
                    print(f"❌ Report error: {result.get('message')}")
            else:
                print(f"❌ Report not accessible: {response.status_code}")
        except Exception as e:
            print(f"❌ Error accessing report: {e}")
    
    print(f"\n🏁 Test completed! Total checks: {check_count}")
    
    # Recommendations
    print("\n💡 Recommendations:")
    if processing_detected:
        print("✅ The processing status handling is working correctly!")
        print("   Users will see 'processing' status instead of timeout errors.")
        print("   The page will auto-refresh until analysis completes.")
    else:
        print("🔧 Consider testing with a larger file or more complex analysis options")
        print("   to trigger the 'still being analyzed' scenario.")

if __name__ == '__main__':
    test_processing_status()
