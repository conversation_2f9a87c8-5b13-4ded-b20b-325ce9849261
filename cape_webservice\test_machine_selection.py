#!/usr/bin/env python3
"""
Test script for machine selection functionality
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_machine_api():
    """Test the machines API endpoint"""
    print("🔍 Testing Machine Selection API")
    print("=" * 50)
    
    # Test machines endpoint
    print("1️⃣ Testing /api/machines endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/api/machines")
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
            
            if not data.get('error') and data.get('machines'):
                machines = data['machines']
                print(f"\n✅ Found {len(machines)} machines:")
                for machine in machines:
                    status_icon = "🔒" if machine.get('locked') else "✅"
                    platform = f" ({machine.get('platform')})" if machine.get('platform') else ""
                    print(f"  {status_icon} {machine.get('name')}{platform}")
                return machines
            else:
                print(f"❌ Error in response: {data.get('message')}")
                return []
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return []

def test_machine_submission(machines):
    """Test submitting analysis with specific machine"""
    if not machines:
        print("\n⚠️  No machines available for testing")
        return
    
    print(f"\n2️⃣ Testing analysis submission with machine selection...")
    
    # Create test file
    test_content = "X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*"
    
    # Test with first available
    print(f"\n📤 Testing with 'first_available'...")
    files = {'file': ('test_machine_first.exe', test_content)}
    data = {
        'machine': 'first_available',
        'options': 'timeout=300',
        'force_reanalyze': 'true'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/analyze", files=files, data=data)
        if response.status_code == 200:
            result = response.json()
            if not result.get('error'):
                analysis_id = result['analysis_id']
                print(f"✅ Submitted with first_available: {analysis_id}")
            else:
                print(f"❌ Submission error: {result.get('message')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test with specific machine (if available)
    available_machines = [m for m in machines if not m.get('locked') and m.get('name') != 'first_available']
    if available_machines:
        machine = available_machines[0]
        machine_name = machine.get('name')
        
        print(f"\n📤 Testing with specific machine: {machine_name}...")
        files = {'file': ('test_machine_specific.exe', test_content)}
        data = {
            'machine': machine_name,
            'options': 'timeout=300',
            'force_reanalyze': 'true'
        }
        
        try:
            response = requests.post(f"{BASE_URL}/api/analyze", files=files, data=data)
            if response.status_code == 200:
                result = response.json()
                if not result.get('error'):
                    analysis_id = result['analysis_id']
                    print(f"✅ Submitted with {machine_name}: {analysis_id}")
                    
                    # Check if machine was actually used
                    time.sleep(2)  # Wait a bit
                    status_response = requests.get(f"{BASE_URL}/api/status/{analysis_id}")
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        print(f"   Analysis status: {status_data.get('status')}")
                        if 'machine' in status_data:
                            print(f"   Machine used: {status_data.get('machine')}")
                else:
                    print(f"❌ Submission error: {result.get('message')}")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
        except Exception as e:
            print(f"❌ Exception: {e}")
    else:
        print(f"\n⚠️  No unlocked machines available for specific testing")

def test_web_interface():
    """Test the web interface for machine selection"""
    print(f"\n3️⃣ Testing web interface...")
    
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            html_content = response.text
            
            # Check if machine select is present
            if 'id="machine"' in html_content:
                print("✅ Machine selection dropdown found in web interface")
                
                # Check for machine options
                if 'first_available' in html_content:
                    print("✅ 'First Available' option found")
                else:
                    print("❌ 'First Available' option not found")
                    
                # Count machine options
                option_count = html_content.count('<option value=')
                print(f"   Found {option_count} machine options in dropdown")
            else:
                print("❌ Machine selection dropdown not found")
        else:
            print(f"❌ Web interface error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_cape_service_directly():
    """Test CAPE service machine functionality directly"""
    print(f"\n4️⃣ Testing CAPE service directly...")
    
    try:
        from cape_service import CAPEAnalysisService
        from config import Config
        
        service = CAPEAnalysisService(
            cape_url=Config.CAPE_URL,
            timeout=10,
            max_wait=0,
            verbose=True
        )
        
        # Test get_machines
        print("📡 Testing get_machines()...")
        machines = service.get_machines()
        
        if machines:
            print(f"✅ Retrieved {len(machines)} machines from CAPE:")
            for machine in machines:
                print(f"   • {machine.get('name')} - {machine.get('status')} - {machine.get('platform')}")
        else:
            print("❌ No machines retrieved from CAPE")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main test function"""
    print("🧪 CAPE Machine Selection Testing")
    print("=" * 60)
    
    # Test API
    machines = test_machine_api()
    
    # Test submission with machines
    test_machine_submission(machines)
    
    # Test web interface
    test_web_interface()
    
    # Test CAPE service directly
    test_cape_service_directly()
    
    print(f"\n🏁 Testing completed!")
    
    # Summary
    print(f"\n📋 Summary:")
    print(f"   • Machine API endpoint: /api/machines")
    print(f"   • Web interface: Machine dropdown in upload form")
    print(f"   • API submission: Include 'machine' parameter")
    print(f"   • Default: 'first_available' lets CAPE choose")

if __name__ == '__main__':
    main()
