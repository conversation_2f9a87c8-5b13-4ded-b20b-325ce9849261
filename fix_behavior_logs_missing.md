# Fix Behavior Analysis - Logs Folder Missing

## Vấn đề từ Log:
```
[modules.processing.behavior] WARNING: Analysis results folder does not exist at path "/opt/CAPEv2/storage/analyses/19/logs"
[lib.cuckoo.core.plugins] INFO: Logs folder doesn't exist, maybe something with with analyzer folder, any change?
```

## Nguyên nhân chính:
**Agent trong VM không chạy hoặc không kết nối được với result server!**

## BƯỚC 1: Phân tích vấn đề

### 1.1. Luồng hoạt động bình thường:
```
1. CAPEv2 submit sample → start VM
2. Agent trong VM nhận task từ result server
3. Agent chạy sample và monitor behavior
4. <PERSON> gửi logs về result server
5. Result server lưu vào /opt/CAPEv2/storage/analyses/X/logs/
6. Processing modules đọc logs và tạo behavior report
```

### 1.2. Vấn đề hiện tại:
```
❌ Bước 4-5 THẤT BẠI: Agent kh<PERSON><PERSON> gửi logs về
→ <PERSON><PERSON><PERSON> mục logs không tồn tại
→ Behavior processing không có data
→ Behavior Analysis rỗng
```

## BƯỚC 2: Kiểm tra Agent trong VM

### 2.1. Script kiểm tra agent status
```bash
#!/bin/bash
# File: check_agent_status.sh

echo "=== CHECKING AGENT STATUS ==="

# 1. Check result server
echo "1. Result Server Status:"
if netstat -tlnp | grep :2042 >/dev/null; then
    echo "✅ Result server listening on port 2042"
    netstat -tlnp | grep :2042
else
    echo "❌ Result server NOT listening"
    echo "Restarting CAPEv2..."
    sudo systemctl restart cape
    sleep 5
    if netstat -tlnp | grep :2042 >/dev/null; then
        echo "✅ Result server now listening"
    else
        echo "❌ Result server failed to start"
        exit 1
    fi
fi

# 2. Check active connections
echo ""
echo "2. Active Agent Connections:"
connections=$(netstat -an | grep :2042 | grep ESTABLISHED)
if [ -n "$connections" ]; then
    echo "✅ Active agent connections found:"
    echo "$connections"
else
    echo "❌ NO active agent connections"
    echo "This is the main problem!"
fi

# 3. Check recent analysis folder
echo ""
echo "3. Recent Analysis Folders:"
if [ -d "/opt/CAPEv2/storage/analyses" ]; then
    latest=$(ls -1t /opt/CAPEv2/storage/analyses/ | head -3)
    for analysis in $latest; do
        echo "Analysis $analysis:"
        if [ -d "/opt/CAPEv2/storage/analyses/$analysis/logs" ]; then
            echo "  ✅ logs folder exists"
            log_count=$(ls /opt/CAPEv2/storage/analyses/$analysis/logs/ 2>/dev/null | wc -l)
            echo "  📁 $log_count log files"
        else
            echo "  ❌ logs folder MISSING"
        fi
    done
else
    echo "❌ No analyses directory found"
fi

echo ""
echo "=== STATUS CHECK COMPLETED ==="
```

### 2.2. Tạo agent debug ISO
```bash
#!/bin/bash
# File: create_agent_debug_iso.sh

CAPE_DIR="/opt/CAPEv2"

echo "Creating agent debug ISO..."

# Tạo thư mục debug
rm -rf /tmp/agent-debug
mkdir -p /tmp/agent-debug

# Copy agent files
cp $CAPE_DIR/agent/agent.py /tmp/agent-debug/
cp -r $CAPE_DIR/agent/*.py /tmp/agent-debug/ 2>/dev/null || true

# Tạo connection test script
cat > /tmp/agent-debug/test_connection.py << 'EOF'
#!/usr/bin/env python3
import socket
import sys
import time

def test_connection():
    print("=== CAPE Agent Connection Test ===")
    
    host = "*************"
    port = 2042
    
    print(f"Testing connection to {host}:{port}...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        print("Attempting to connect...")
        result = sock.connect_ex((host, port))
        
        if result == 0:
            print("✅ SUCCESS: Connected to result server!")
            
            # Test sending data
            try:
                test_msg = b"CAPE_CONNECTION_TEST\n"
                sock.send(test_msg)
                print("✅ SUCCESS: Can send data to result server!")
                return True
            except Exception as e:
                print(f"⚠️  Connected but cannot send data: {e}")
                return False
        else:
            print(f"❌ FAILED: Cannot connect (error code: {result})")
            print("Possible causes:")
            print("- Result server not running")
            print("- Windows Firewall blocking")
            print("- Network configuration issue")
            return False
            
    except Exception as e:
        print(f"❌ FAILED: Connection error: {e}")
        return False
    finally:
        try:
            sock.close()
        except:
            pass

if __name__ == "__main__":
    success = test_connection()
    input("\nPress Enter to continue...")
    sys.exit(0 if success else 1)
EOF

# Tạo Windows install script
cat > /tmp/agent-debug/install_agent.bat << 'EOF'
@echo off
echo === CAPE Agent Installation ===

REM Check Administrator privileges
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Please run as Administrator!
    pause
    exit /b 1
)

REM Check Python
echo Checking Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found!
    echo Please install Python 3.8+ and add to PATH
    pause
    exit /b 1
)
echo ✅ Python found

REM Create agent directory
echo Creating agent directory...
if not exist "C:\cape-agent" mkdir C:\cape-agent

REM Copy files
echo Copying agent files...
copy *.py C:\cape-agent\
if %errorlevel% neq 0 (
    echo ERROR: Failed to copy files
    pause
    exit /b 1
)
echo ✅ Files copied

REM Test connection
echo Testing connection to result server...
cd C:\cape-agent
python test_connection.py

REM Create startup scripts
echo Creating startup scripts...
echo @echo off > C:\cape-agent\start_agent.bat
echo cd C:\cape-agent >> C:\cape-agent\start_agent.bat
echo python agent.py ************* 2042 >> C:\cape-agent\start_agent.bat

echo @echo off > C:\cape-agent\start_agent_debug.bat
echo cd C:\cape-agent >> C:\cape-agent\start_agent_debug.bat
echo echo Starting CAPE Agent with debug... >> C:\cape-agent\start_agent_debug.bat
echo python agent.py ************* 2042 >> C:\cape-agent\start_agent_debug.bat
echo echo Agent stopped. Check output above. >> C:\cape-agent\start_agent_debug.bat
echo pause >> C:\cape-agent\start_agent_debug.bat

REM Add to startup
echo Adding to Windows startup...
copy C:\cape-agent\start_agent.bat "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\" >nul 2>&1

echo.
echo === Installation Complete ===
echo.
echo Files installed to: C:\cape-agent
echo Auto-start enabled: Agent will start on boot
echo.
echo To start now: C:\cape-agent\start_agent_debug.bat
echo To test connection: C:\cape-agent\test_connection.py
echo.
pause
EOF

# Tạo firewall disable script
cat > /tmp/agent-debug/disable_firewall.bat << 'EOF'
@echo off
echo === Disabling Windows Firewall ===

REM Check Administrator privileges
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Please run as Administrator!
    pause
    exit /b 1
)

echo Disabling Windows Firewall...
netsh advfirewall set allprofiles state off

echo Allowing Python through firewall (backup)...
netsh advfirewall firewall add rule name="Python CAPE Agent" dir=out action=allow program="python.exe"
netsh advfirewall firewall add rule name="Python CAPE Agent 2" dir=out action=allow program="C:\Python*\python.exe"

echo Allowing port 2042...
netsh advfirewall firewall add rule name="CAPE Result Server" dir=out action=allow protocol=TCP remoteport=2042

echo.
echo ✅ Windows Firewall configured for CAPE Agent
echo.
pause
EOF

# Tạo manual test script
cat > /tmp/agent-debug/manual_test.bat << 'EOF'
@echo off
echo === Manual Agent Test ===

echo 1. Testing network connectivity...
ping -n 1 *************
if %errorlevel% neq 0 (
    echo ❌ Cannot ping host!
    pause
    exit /b 1
)
echo ✅ Host reachable

echo.
echo 2. Testing Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python not working!
    pause
    exit /b 1
)
echo ✅ Python working

echo.
echo 3. Testing connection to result server...
cd C:\cape-agent
python test_connection.py

echo.
echo 4. Starting agent manually (Ctrl+C to stop)...
python agent.py ************* 2042
EOF

# Tạo README
cat > /tmp/agent-debug/README.txt << 'EOF'
CAPE Agent Debug Package
========================

Files:
- agent.py: Main CAPE agent
- test_connection.py: Test connection to result server
- install_agent.bat: Install agent and setup auto-start
- disable_firewall.bat: Disable Windows Firewall
- manual_test.bat: Manual testing and debugging

Installation Steps:
1. Run disable_firewall.bat as Administrator
2. Run install_agent.bat as Administrator
3. Test with manual_test.bat
4. Agent should auto-start on boot

Troubleshooting:
- If connection test fails: Check result server on host
- If Python not found: Install Python and add to PATH
- If agent stops: Check Windows Firewall and antivirus
- If no behavior data: Ensure agent connects successfully

Expected Output:
- Connection test should show "SUCCESS: Connected to result server!"
- Agent should show "Connected to *************:2042"
- Host should show active connection in netstat
EOF

# Tạo ISO
genisoimage -o /var/lib/libvirt/images/iso/cape-agent-debug.iso -V "CAPE_AGENT_DEBUG" -r -J /tmp/agent-debug/

echo "✅ Agent debug ISO created: /var/lib/libvirt/images/iso/cape-agent-debug.iso"
echo ""
echo "Next steps:"
echo "1. Attach ISO to VM"
echo "2. Install agent in VM"
echo "3. Test connection"
echo "4. Submit new analysis"
```

## BƯỚC 3: Fix trong VM

### 3.1. Attach ISO và install agent
```bash
#!/bin/bash
# File: attach_and_install_agent.sh

echo "Attaching agent debug ISO to VMs..."

# Attach to all VMs
for vm in cape1 cuckoo1; do
    echo "Attaching to $vm..."
    sudo virsh attach-disk $vm /var/lib/libvirt/images/iso/cape-agent-debug.iso hdc --type cdrom --mode readonly --config
done

echo ""
echo "✅ ISO attached to VMs"
echo ""
echo "MANUAL STEPS IN EACH VM:"
echo ""
echo "1. Open File Explorer, go to CD drive"
echo "2. Run disable_firewall.bat as Administrator"
echo "3. Run install_agent.bat as Administrator"
echo "4. Run manual_test.bat to verify"
echo "5. Agent should auto-start on next boot"
echo ""
echo "Expected output in VM:"
echo "✅ SUCCESS: Connected to result server!"
echo "✅ Agent connected to *************:2042"
```

### 3.2. Monitor agent connections
```bash
#!/bin/bash
# File: monitor_agent_connections.sh

echo "=== MONITORING AGENT CONNECTIONS ==="
echo "Press Ctrl+C to stop monitoring"
echo ""

while true; do
    timestamp=$(date '+%H:%M:%S')
    echo "[$timestamp] Checking connections..."
    
    # Check active connections
    connections=$(netstat -an | grep :2042 | grep ESTABLISHED)
    if [ -n "$connections" ]; then
        echo "✅ Active agent connections:"
        echo "$connections" | sed 's/^/  /'
    else
        echo "❌ No active agent connections"
    fi
    
    # Check recent logs
    echo "Recent CAPEv2 logs:"
    tail -3 /opt/CAPEv2/log/cuckoo.log 2>/dev/null | grep -E "(Task|agent|connect)" | sed 's/^/  /'
    
    echo "----------------------------------------"
    sleep 5
done
```

## BƯỚC 4: Test và verify

### 4.1. Submit test analysis
```bash
#!/bin/bash
# File: test_behavior_analysis.sh

echo "=== TESTING BEHAVIOR ANALYSIS ==="

# 1. Check agent connections first
echo "1. Checking agent connections..."
if ! netstat -an | grep :2042 | grep ESTABLISHED >/dev/null; then
    echo "❌ No agent connections! Install agent in VMs first."
    exit 1
fi
echo "✅ Agent connections found"

# 2. Create simple test file
echo ""
echo "2. Creating test file..."
cat > /tmp/cape_behavior_test.py << 'EOF'
#!/usr/bin/env python3
import os
import time

print("CAPE Behavior Test Starting...")

# Test file operations
try:
    with open("cape_test_file.txt", "w") as f:
        f.write("CAPE behavior analysis test file")
    print("✅ File created: cape_test_file.txt")
except Exception as e:
    print(f"❌ File creation failed: {e}")

# Test process operations
try:
    import subprocess
    result = subprocess.run(["whoami"], capture_output=True, text=True, timeout=5)
    print(f"✅ Process executed: {result.stdout.strip()}")
except Exception as e:
    print(f"❌ Process execution failed: {e}")

# Sleep to allow monitoring
print("Sleeping for 30 seconds for behavior monitoring...")
time.sleep(30)

print("CAPE Behavior Test Completed")
EOF

chmod +x /tmp/cape_behavior_test.py

# 3. Submit analysis
echo ""
echo "3. Submitting test analysis..."
cd /opt/CAPEv2
python3 utils/submit.py --timeout 120 /tmp/cape_behavior_test.py

echo ""
echo "4. Monitor analysis progress..."
echo "Check web interface: http://localhost:8000"
echo "Monitor logs: tail -f log/cuckoo.log"
echo ""
echo "Expected results:"
echo "✅ Logs folder should exist in storage/analyses/X/logs/"
echo "✅ Behavior section should have data"
echo "✅ File operations should be recorded"
```

## BƯỚC 5: Complete fix script

### 5.1. Script fix hoàn chỉnh
```bash
#!/bin/bash
# File: complete_behavior_fix.sh

echo "=== COMPLETE BEHAVIOR ANALYSIS FIX ==="

# 1. Check current status
echo "Step 1: Checking current status..."
./check_agent_status.sh

# 2. Create agent debug ISO
echo ""
echo "Step 2: Creating agent debug ISO..."
./create_agent_debug_iso.sh

# 3. Attach ISO to VMs
echo ""
echo "Step 3: Attaching ISO to VMs..."
./attach_and_install_agent.sh

# 4. Start monitoring
echo ""
echo "Step 4: Starting connection monitor..."
./monitor_agent_connections.sh > agent_monitor.log 2>&1 &
MONITOR_PID=$!
echo "Monitor started with PID: $MONITOR_PID"
echo "Monitor log: tail -f agent_monitor.log"

echo ""
echo "=== FIX SETUP COMPLETED ==="
echo ""
echo "MANUAL STEPS REQUIRED:"
echo ""
echo "IN EACH VM (cape1, cuckoo1):"
echo "1. Open File Explorer → CD drive"
echo "2. Run disable_firewall.bat as Administrator"
echo "3. Run install_agent.bat as Administrator"
echo "4. Verify with manual_test.bat"
echo "5. Restart VM to test auto-start"
echo ""
echo "THEN TEST:"
echo "./test_behavior_analysis.sh"
echo ""
echo "To stop monitor: kill $MONITOR_PID"
```

## CÁCH SỬ DỤNG:

```bash
# 1. Run complete fix
chmod +x *.sh
./complete_behavior_fix.sh

# 2. Follow manual steps in VMs

# 3. Test behavior analysis
./test_behavior_analysis.sh

# 4. Check results
# http://localhost:8000
```

## KẾT QUẢ MONG ĐỢI:

✅ **Sau khi fix:**
- Agent connections trong netstat
- Logs folder tồn tại: `/opt/CAPEv2/storage/analyses/X/logs/`
- Behavior Analysis có data
- Không còn warning "logs folder doesn't exist"

❌ **Trước khi fix:**
- Không có agent connections
- Logs folder missing
- Behavior Analysis rỗng
- Warning trong logs

**Vấn đề chính là agent không chạy trong VM!**
