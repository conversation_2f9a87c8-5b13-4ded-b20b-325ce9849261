# CAPE Report Templates

<PERSON><PERSON><PERSON> mục này chứa các template HTML để hiển thị báo cáo phân tích CAPE một cách đẹp mắt và chuyên nghiệp.

## Cấu trúc Files

### 1. `base_report.html`
- Template c<PERSON> sở cho tất cả các báo cáo
- Chứa CSS styling, navigation, và layout chung
- Sử dụng Bootstrap 5 và Font Awesome icons
- Responsive design cho mobile và desktop

### 2. `analysis_report.html`
- Báo cáo phân tích tổng quan
- Hiển thị thông tin file, signatures, network activity
- Bao gồm processing statistics với progress bars
- Có chức năng search và filter

### 3. `behavior_report.html`
- Báo cáo chi tiết về behavior analysis
- Process tree với timeline view
- API calls summary và detailed view
- File system activity và registry changes
- Memory analysis results

### 4. `summary_report.html`
- <PERSON><PERSON>o c<PERSON>o tóm tắt executive-level
- Threat assessment và recommendations
- Key findings và quick stats
- Analysis timeline

## Tính năng chính

### 🎨 Design Features
- **Modern UI**: Sử dụng Bootstrap 5 với custom CSS
- **Responsive**: Tương thích với mọi kích thước màn hình
- **Dark/Light themes**: Hỗ trợ cả hai chế độ
- **Smooth animations**: Hiệu ứng chuyển động mượt mà
- **Professional styling**: Thiết kế chuyên nghiệp cho enterprise

### 🔍 Interactive Features
- **Search functionality**: Tìm kiếm trong processes, network, strings
- **Collapsible sections**: Thu gọn/mở rộng các phần chi tiết
- **Progress bars**: Hiển thị processing statistics
- **Timeline view**: Xem process execution theo thời gian
- **Filtering**: Lọc dữ liệu theo nhiều tiêu chí

### 📊 Data Visualization
- **Metric cards**: Hiển thị số liệu quan trọng
- **Progress indicators**: Thanh tiến trình cho statistics
- **Status badges**: Nhãn trạng thái với màu sắc
- **Tables**: Bảng dữ liệu có thể sắp xếp
- **Code blocks**: Hiển thị code và logs đẹp mắt

## Cách sử dụng

### 1. Trong Flask Application

```python
from flask import render_template

@app.route('/report/<int:analysis_id>')
def show_report(analysis_id):
    report_data = get_analysis_report(analysis_id)
    return render_template('report_templates/analysis_report.html', 
                         report=report_data)

@app.route('/report/<int:analysis_id>/behavior')
def show_behavior_report(analysis_id):
    report_data = get_analysis_report(analysis_id)
    return render_template('report_templates/behavior_report.html', 
                         report=report_data)

@app.route('/report/<int:analysis_id>/summary')
def show_summary_report(analysis_id):
    report_data = get_analysis_report(analysis_id)
    return render_template('report_templates/summary_report.html', 
                         report=report_data)
```

### 2. Template Filters cần thiết

Thêm các filters sau vào Flask app:

```python
@app.template_filter('score_to_status')
def score_to_status(score):
    if score >= 7:
        return 'danger'
    elif score >= 4:
        return 'warning'
    else:
        return 'success'

@app.template_filter('score_to_verdict')
def score_to_verdict(score):
    if score >= 7:
        return 'MALICIOUS'
    elif score >= 4:
        return 'SUSPICIOUS'
    else:
        return 'CLEAN'

@app.template_filter('severity_to_color')
def severity_to_color(severity):
    severity_map = {
        'high': 'danger',
        'medium': 'warning',
        'low': 'info',
        'info': 'secondary'
    }
    return severity_map.get(severity.lower(), 'secondary')

@app.template_filter('filesizeformat')
def filesizeformat(bytes):
    for unit in ['B', 'KB', 'MB', 'GB']:
        if bytes < 1024.0:
            return f"{bytes:.1f} {unit}"
        bytes /= 1024.0
    return f"{bytes:.1f} TB"
```

### 3. Cấu trúc dữ liệu mong đợi

Templates mong đợi dữ liệu report có cấu trúc như sau:

```python
report = {
    'info': {
        'id': 123,
        'started': '2024-01-01 10:00:00',
        'ended': '2024-01-01 10:05:00',
        'duration': 300,
        'score': 8,
        'platform': 'windows',
        'version': '1.0',
        'machine': {'name': 'win10-vm'}
    },
    'target': {
        'file': {
            'name': 'malware.exe',
            'size': 1024000,
            'type': 'PE32',
            'md5': 'abc123...',
            'sha1': 'def456...',
            'sha256': 'ghi789...'
        }
    },
    'signatures': [...],
    'behavior': {
        'processes': [...],
        'summary': {
            'files': [...],
            'keys': [...]
        }
    },
    'network': {
        'tcp': [...],
        'udp': [...],
        'dns': [...]
    },
    'dropped': [...],
    'strings': [...],
    'memory': {...}
}
```

## Customization

### Thay đổi màu sắc
Chỉnh sửa CSS variables trong `base_report.html`:

```css
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
}
```

### Thêm sections mới
Extend từ `base_report.html` và thêm content:

```html
{% extends "base_report.html" %}

{% block content %}
<section class="report-section">
    <h2 class="section-title">
        <i class="fas fa-custom-icon me-2"></i>
        Custom Section
    </h2>
    <!-- Your content here -->
</section>
{% endblock %}
```

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Dependencies

- Bootstrap 5.1.3
- Font Awesome 6.0.0
- Modern browser với ES6 support

## Notes

- Templates sử dụng Jinja2 syntax
- Responsive design cho mobile
- Print-friendly styling
- Accessibility compliant (WCAG 2.1)
- SEO optimized structure
