# CAPEv2 Network Analysis & Suricata Configuration Guide

## 🌐 **Tổng quan**

Hướng dẫn cấu hình Network Analysis và Suricata IDS trong CAPEv2 để phân tích network behavior của malware.

## 📁 **File cấu hình chính**

### 1. **conf/processing.conf** - Network & Suricata Processing
### 2. **conf/auxiliary.conf** - Network Packet Capture  
### 3. **conf/cuckoo.conf** - Network Routing
### 4. **conf/kvm.conf** - VM Network Interface

---

## 🔧 **1. Cấu hình Network Analysis**

### **File: conf/processing.conf**

```ini
[network]
# Enable network analysis
enabled = yes
# Sort PCAP files by timestamp
sort_pcap = yes
# DNS whitelisting to ignore domains/IPs
dnswhitelist = yes
dnswhitelist_file = extra/whitelist_domains.txt
# IP whitelisting  
ipwhitelist = yes
ipwhitelist_file = extra/whitelist_ips.txt
# Network passlist
network_passlist = no
network_passlist_file = extra/whitelist_network.txt
# Enable country lookup (requires GeoIP database)
country_lookup = yes

[detections]
enabled = yes
# Enable behavior signatures
behavior = yes
yara = yes
# Enable Suricata alerts
suricata = yes
virustotal = no
clamav = no
```

---

## 🛡️ **2. Cấu hình Suricata IDS**

### **File: conf/processing.conf**

```ini
[suricata]
# Enable Suricata IDS
enabled = yes
# Suricata binary path
bin = /usr/bin/suricata
# Suricata config file
conf = /etc/suricata/suricata.yaml
# Socket file for communication
socket_file = /tmp/suricata-command.socket
# Rules files path
rules_path = /var/lib/suricata/rules/
# Enable eve.json output
eve_log = yes
# Alert threshold (0 = all alerts)
alert_threshold = 0
# Enable file extraction
file_extraction = yes
# Max file size to extract (MB)
max_file_size = 100
```

---

## 📡 **3. Cấu hình Network Packet Capture**

### **File: conf/auxiliary.conf**

```ini
[sniffer]
# Enable network packet capture
enabled = yes
# Enable remote tcpdump support
remote = no
# Tcpdump binary path
tcpdump = /usr/bin/tcpdump
# Network interface to monitor (thay đổi theo setup)
interface = virbr1
# Berkeley packet filter
bpf = not arp

[AzSniffer]
# Disable Azure sniffer (chỉ dùng cho Azure VMs)
enabled = no
```

---

## ⚙️ **4. Cấu hình Network Routing**

### **File: conf/cuckoo.conf**

```ini
[cuckoo]
# Enable network routing
machinery = kvm

[processing]
# Enable network processing
sort_pcap = yes
# Enable DNS resolution
resolve_dns = yes

[resultserver]
# Result server cho network communication
ip = 0.0.0.0
port = 2042
# Increase upload size for network captures
upload_max_size = 256
```

### **File: conf/kvm.conf**

```ini
[kvm]
# Network interface
interface = virbr1

[cuckoo1]
label = cuckoo1
platform = windows
ip = ***************
# Network interface cho VM
interface = virbr1
# Result server IP
resultserver_ip = *************
```

---

## 🔧 **5. Cài đặt Suricata**

### **A. Install Suricata**

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install suricata suricata-update -y

# Hoặc compile từ source (recommended cho performance)
wget https://www.openinfosecfoundation.org/download/suricata-6.0.14.tar.gz
tar xzf suricata-6.0.14.tar.gz
cd suricata-6.0.14
./configure --enable-nfqueue --prefix=/usr --sysconfdir=/etc --localstatedir=/var
make && sudo make install
```

### **B. Cấu hình Suricata**

```bash
# Edit Suricata config
sudo nano /etc/suricata/suricata.yaml
```

**Key settings trong suricata.yaml:**
```yaml
vars:
  address-groups:
    HOME_NET: "[***********/16,10.0.0.0/8,**********/12]"
    EXTERNAL_NET: "!$HOME_NET"

af-packet:
  - interface: virbr1
    cluster-id: 99
    cluster-type: cluster_flow

outputs:
  - eve-log:
      enabled: yes
      filetype: regular
      filename: eve.json
      types:
        - alert
        - http
        - dns
        - tls
        - files
        - smtp
```

### **C. Update Suricata Rules**

```bash
# Update rules
sudo suricata-update

# Enable specific rulesets
sudo suricata-update enable-source et/open
sudo suricata-update enable-source oisf/trafficid
sudo suricata-update enable-source ptresearch/attackdetection
sudo suricata-update

# List available sources
sudo suricata-update list-sources
```

### **D. Test Suricata**

```bash
# Test Suricata config
sudo suricata -T -c /etc/suricata/suricata.yaml

# Test with interface
sudo suricata -c /etc/suricata/suricata.yaml -i virbr1 --init-errors-fatal

# Check Suricata service
sudo systemctl status suricata
sudo systemctl enable suricata
sudo systemctl start suricata
```

---

## 🌐 **6. Network Interface Setup**

### **A. Kiểm tra Network Interface**

```bash
# List network interfaces
ip addr show

# Check bridge interfaces
brctl show

# Check KVM network
virsh net-list
virsh net-info default
```

### **B. Tạo Bridge Interface (nếu cần)**

```bash
# Tạo bridge cho KVM
sudo virsh net-define /dev/stdin <<EOF
<network>
  <name>cape</name>
  <forward mode='nat'/>
  <bridge name='virbr1' stp='on' delay='0'/>
  <ip address='*************' netmask='*************'>
    <dhcp>
      <range start='***************' end='***************'/>
    </dhcp>
  </ip>
</network>
EOF

sudo virsh net-start cape
sudo virsh net-autostart cape
```

---

## 📊 **7. Kết quả mong đợi**

### **Network Analysis sẽ cung cấp:**
- ✅ **PCAP files**: Raw network traffic
- ✅ **DNS queries**: Domain resolutions  
- ✅ **HTTP requests**: Web traffic analysis
- ✅ **TCP/UDP connections**: Network connections
- ✅ **Geolocation**: Country/ISP information
- ✅ **Protocol analysis**: Protocol breakdown

### **Suricata Alerts sẽ detect:**
- 🚨 **Malware C&C communication**
- 🚨 **Suspicious network patterns**
- 🚨 **Known malicious IPs/domains**
- 🚨 **Protocol anomalies**
- 🚨 **Data exfiltration attempts**
- 🚨 **Exploit attempts**
- 🚨 **Botnet traffic**

---

## 🚀 **8. Script tự động Enable Network + Suricata**

### **File: enable_network_suricata.sh**

```bash
#!/bin/bash
# CAPEv2 Network Analysis & Suricata Auto-Enable Script

echo "=== CAPEv2 Network Analysis & Suricata Setup ==="

CAPE_DIR="/opt/CAPEv2"
CONF_DIR="$CAPE_DIR/conf"

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo "Please run this script as normal user (not root)"
   exit 1
fi

# 1. Install Suricata
echo "1. Installing Suricata..."
sudo apt update
sudo apt install suricata suricata-update -y

# 2. Update Suricata rules
echo "2. Updating Suricata rules..."
sudo suricata-update
sudo suricata-update enable-source et/open
sudo suricata-update enable-source oisf/trafficid
sudo suricata-update

# 3. Backup existing configs
echo "3. Backing up existing configs..."
mkdir -p $CONF_DIR/backup_$(date +%Y%m%d_%H%M%S)
cp $CONF_DIR/*.conf $CONF_DIR/backup_$(date +%Y%m%d_%H%M%S)/ 2>/dev/null || true

# 4. Enable Network Analysis in processing.conf
echo "4. Configuring processing.conf..."
sed -i '/\[network\]/,/enabled/ s/enabled = no/enabled = yes/' $CONF_DIR/processing.conf
sed -i '/\[network\]/,/sort_pcap/ s/sort_pcap = no/sort_pcap = yes/' $CONF_DIR/processing.conf
sed -i '/\[suricata\]/,/enabled/ s/enabled = no/enabled = yes/' $CONF_DIR/processing.conf
sed -i '/\[detections\]/,/suricata/ s/suricata = no/suricata = yes/' $CONF_DIR/processing.conf

# 5. Enable Sniffer in auxiliary.conf
echo "5. Configuring auxiliary.conf..."
sed -i '/\[sniffer\]/,/enabled/ s/enabled = no/enabled = yes/' $CONF_DIR/auxiliary.conf

# 6. Configure cuckoo.conf
echo "6. Configuring cuckoo.conf..."
sed -i '/\[processing\]/,/sort_pcap/ s/sort_pcap = no/sort_pcap = yes/' $CONF_DIR/cuckoo.conf
sed -i '/\[processing\]/,/resolve_dns/ s/resolve_dns = no/resolve_dns = yes/' $CONF_DIR/cuckoo.conf

# 7. Test Suricata config
echo "7. Testing Suricata configuration..."
sudo suricata -T -c /etc/suricata/suricata.yaml

if [ $? -eq 0 ]; then
    echo "✅ Suricata configuration is valid"
else
    echo "❌ Suricata configuration has errors"
    exit 1
fi

# 8. Enable and start Suricata service
echo "8. Starting Suricata service..."
sudo systemctl enable suricata
sudo systemctl start suricata

# 9. Restart CAPE services
echo "9. Restarting CAPE services..."
sudo systemctl restart cape cape-processor cape-web cape-rooter

# 10. Verify setup
echo "10. Verifying setup..."
sleep 5

# Check services status
echo "=== Service Status ==="
sudo systemctl is-active suricata && echo "✅ Suricata: Active" || echo "❌ Suricata: Inactive"
sudo systemctl is-active cape && echo "✅ CAPE: Active" || echo "❌ CAPE: Inactive"
sudo systemctl is-active cape-processor && echo "✅ CAPE Processor: Active" || echo "❌ CAPE Processor: Inactive"
sudo systemctl is-active cape-web && echo "✅ CAPE Web: Active" || echo "❌ CAPE Web: Inactive"

# Check network interface
echo "=== Network Interface ==="
ip addr show virbr1 >/dev/null 2>&1 && echo "✅ virbr1: Available" || echo "❌ virbr1: Not found"

echo ""
echo "=== Setup Complete ==="
echo "✅ Network Analysis: Enabled"
echo "✅ Suricata IDS: Enabled"
echo "✅ Packet Capture: Enabled"
echo ""
echo "📝 Next steps:"
echo "1. Submit a sample for analysis"
echo "2. Check network tab in web interface"
echo "3. Review Suricata alerts in analysis results"
echo ""
echo "📁 Config files location:"
echo "- Processing: $CONF_DIR/processing.conf"
echo "- Auxiliary: $CONF_DIR/auxiliary.conf"
echo "- Cuckoo: $CONF_DIR/cuckoo.conf"
echo ""
echo "📊 Suricata logs:"
echo "- Alerts: /var/log/suricata/eve.json"
echo "- Fast log: /var/log/suricata/fast.log"
```

### **Cách sử dụng script:**

```bash
# Download và chạy script
wget -O enable_network_suricata.sh https://raw.githubusercontent.com/your-repo/enable_network_suricata.sh
chmod +x enable_network_suricata.sh
./enable_network_suricata.sh
```

---

## 🔍 **9. Troubleshooting**

### **A. Suricata không start**
```bash
# Check Suricata logs
sudo journalctl -u suricata -f

# Check config syntax
sudo suricata -T -c /etc/suricata/suricata.yaml

# Check interface
sudo suricata -c /etc/suricata/suricata.yaml -i virbr1 --init-errors-fatal
```

### **B. Network interface không tồn tại**
```bash
# List available interfaces
ip link show

# Create bridge if needed
sudo virsh net-start default
sudo virsh net-autostart default
```

### **C. PCAP files không được tạo**
```bash
# Check tcpdump permissions
sudo chmod +x /usr/bin/tcpdump

# Check interface in auxiliary.conf
grep -A 5 "\[sniffer\]" /opt/CAPEv2/conf/auxiliary.conf

# Test tcpdump manually
sudo tcpdump -i virbr1 -w test.pcap
```

### **D. Suricata rules không update**
```bash
# Manual rules update
sudo suricata-update --force

# Check rules location
ls -la /var/lib/suricata/rules/

# Check Suricata config for rules path
grep "rule-files:" /etc/suricata/suricata.yaml
```

---

## 📈 **10. Performance Tuning**

### **A. Suricata Performance**
```yaml
# /etc/suricata/suricata.yaml
threading:
  set-cpu-affinity: no
  cpu-affinity:
    - management-cpu-set:
        cpu: [ 0 ]
    - receive-cpu-set:
        cpu: [ 0 ]
    - worker-cpu-set:
        cpu: [ "1-3" ]

# Increase buffer sizes
af-packet:
  - interface: virbr1
    buffer-size: 64mb
    ring-size: 2048
```

### **B. Network Analysis Performance**
```ini
# conf/processing.conf
[network]
# Disable country lookup if not needed
country_lookup = no
# Reduce PCAP processing
sort_pcap = no
# Use whitelist to reduce noise
dnswhitelist = yes
ipwhitelist = yes
```

---

## ✅ **11. Verification Checklist**

- [ ] Suricata service running: `sudo systemctl status suricata`
- [ ] Network interface available: `ip addr show virbr1`
- [ ] CAPE services running: `sudo systemctl status cape*`
- [ ] Tcpdump executable: `which tcpdump`
- [ ] Suricata rules updated: `ls /var/lib/suricata/rules/`
- [ ] Config files syntax valid
- [ ] Test analysis shows network data
- [ ] Suricata alerts appearing in results

---

**🎯 Sau khi hoàn thành setup, bạn sẽ có đầy đủ Network Analysis và Suricata IDS để phân tích network behavior của malware!**
