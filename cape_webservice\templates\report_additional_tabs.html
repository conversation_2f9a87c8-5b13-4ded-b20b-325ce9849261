                    <!-- CAPE Tab -->
                    {% if report.CAPE %}
                    <div class="tab-pane fade" id="cape" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-bug me-2"></i>CAPE Extracted Payloads</h4>
                            
                            {% if report.CAPE.payloads %}
                            <div class="mb-4">
                                <h5><i class="fas fa-download me-2"></i>Payloads ({{ report.CAPE.payloads|length }})</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Type</th>
                                                <th>Module Path</th>
                                                <th>Size</th>
                                                <th>SHA256</th>
                                                <th>Yara Rules</th>
                                                <th>Config</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for payload in report.CAPE.payloads %}
                                            <tr>
                                                <td><span class="badge bg-danger">{{ payload.type if payload.type else 'Unknown' }}</span></td>
                                                <td><small>{{ payload.module_path if payload.module_path else 'N/A' }}</small></td>
                                                <td>{{ "{:,}".format(payload.size) if payload.size else 'N/A' }} bytes</td>
                                                <td><code class="hash-value">{{ payload.sha256 if payload.sha256 else 'N/A' }}</code></td>
                                                <td>
                                                    {% if payload.cape_yara %}
                                                    {% for yara in payload.cape_yara %}
                                                    <span class="badge bg-warning me-1">{{ yara.name }}</span>
                                                    {% endfor %}
                                                    {% else %}
                                                    N/A
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if payload.config %}
                                                    <button class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#configModal{{ loop.index }}">
                                                        View Config
                                                    </button>
                                                    {% else %}
                                                    N/A
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Config Modals -->
                            {% for payload in report.CAPE.payloads %}
                            {% if payload.config %}
                            <div class="modal fade" id="configModal{{ loop.index }}" tabindex="-1">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Payload Configuration</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <pre class="bg-light p-3"><code>{{ payload.config | tojson(indent=2) }}</code></pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            {% endfor %}
                            {% endif %}

                            {% if report.CAPE.configs %}
                            <div class="mb-4">
                                <h5><i class="fas fa-cog me-2"></i>Extracted Configurations</h5>
                                <div class="accordion" id="configsAccordion">
                                    {% for config in report.CAPE.configs %}
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="config{{ loop.index }}">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                    data-bs-target="#configCollapse{{ loop.index }}">
                                                {{ config.family if config.family else 'Configuration' }} - {{ config.type if config.type else 'Unknown Type' }}
                                            </button>
                                        </h2>
                                        <div id="configCollapse{{ loop.index }}" class="accordion-collapse collapse" 
                                             data-bs-parent="#configsAccordion">
                                            <div class="accordion-body">
                                                <pre class="bg-light p-2"><code>{{ config | tojson(indent=2) }}</code></pre>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Dropped Files Tab -->
                    {% if report.dropped %}
                    <div class="tab-pane fade" id="dropped" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-file-code me-2"></i>Dropped Files ({{ report.dropped|length }})</h4>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>File Name</th>
                                            <th>Path</th>
                                            <th>Size</th>
                                            <th>Type</th>
                                            <th>MD5</th>
                                            <th>SHA256</th>
                                            <th>Yara</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for file in report.dropped %}
                                        <tr>
                                            <td><strong>{{ file.name }}</strong></td>
                                            <td><small>{{ file.path if file.path else 'N/A' }}</small></td>
                                            <td>{{ "{:,}".format(file.size) if file.size else 'N/A' }} bytes</td>
                                            <td>{{ file.type if file.type else 'Unknown' }}</td>
                                            <td><code class="hash-value">{{ file.md5 }}</code></td>
                                            <td><code class="hash-value">{{ file.sha256 if file.sha256 else 'N/A' }}</code></td>
                                            <td>
                                                {% if file.yara %}
                                                {% for yara in file.yara %}
                                                <span class="badge bg-info me-1">{{ yara.name }}</span>
                                                {% endfor %}
                                                {% else %}
                                                N/A
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Static Analysis Tab -->
                    {% if report.static %}
                    <div class="tab-pane fade" id="static" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-search-plus me-2"></i>Static Analysis</h4>
                            
                            <!-- PE Information -->
                            {% if report.static.pe %}
                            <div class="mb-4">
                                <h5><i class="fas fa-file-alt me-2"></i>PE Information</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <tr><th>Machine</th><td>{{ report.static.pe.machine if report.static.pe.machine else 'N/A' }}</td></tr>
                                                <tr><th>Timestamp</th><td>{{ report.static.pe.timestamp if report.static.pe.timestamp else 'N/A' }}</td></tr>
                                                <tr><th>Entry Point</th><td><code>{{ report.static.pe.entrypoint if report.static.pe.entrypoint else 'N/A' }}</code></td></tr>
                                                <tr><th>Image Base</th><td><code>{{ report.static.pe.imagebase if report.static.pe.imagebase else 'N/A' }}</code></td></tr>
                                                <tr><th>Checksum</th><td><code>{{ report.static.pe.checksum if report.static.pe.checksum else 'N/A' }}</code></td></tr>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <tr><th>Sections</th><td>{{ report.static.pe.sections|length if report.static.pe.sections else 0 }}</td></tr>
                                                <tr><th>Imports</th><td>{{ report.static.pe.imports|length if report.static.pe.imports else 0 }}</td></tr>
                                                <tr><th>Exports</th><td>{{ report.static.pe.exports|length if report.static.pe.exports else 0 }}</td></tr>
                                                <tr><th>Resources</th><td>{{ report.static.pe.resources|length if report.static.pe.resources else 0 }}</td></tr>
                                                <tr><th>Version Info</th><td>{{ 'Yes' if report.static.pe.versioninfo else 'No' }}</td></tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- PE Sections -->
                            {% if report.static.pe.sections %}
                            <div class="mb-4">
                                <h5><i class="fas fa-layer-group me-2"></i>PE Sections</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Virtual Address</th>
                                                <th>Virtual Size</th>
                                                <th>Raw Size</th>
                                                <th>Entropy</th>
                                                <th>MD5</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for section in report.static.pe.sections %}
                                            <tr>
                                                <td><strong>{{ section.name }}</strong></td>
                                                <td><code>{{ section.virtual_address if section.virtual_address else 'N/A' }}</code></td>
                                                <td>{{ section.virtual_size if section.virtual_size else 'N/A' }}</td>
                                                <td>{{ section.size_of_data if section.size_of_data else 'N/A' }}</td>
                                                <td>{{ "%.2f"|format(section.entropy) if section.entropy else 'N/A' }}</td>
                                                <td><code class="hash-value">{{ section.md5 if section.md5 else 'N/A' }}</code></td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}

                            <!-- Imports -->
                            {% if report.static.pe.imports %}
                            <div class="mb-4">
                                <h5><i class="fas fa-download me-2"></i>Imported Functions</h5>
                                <div class="row">
                                    {% for dll, functions in report.static.pe.imports.items() %}
                                    <div class="col-md-6 mb-3">
                                        <h6>{{ dll }} ({{ functions|length }} functions)</h6>
                                        <div class="bg-light p-2 rounded" style="max-height: 200px; overflow-y: auto;">
                                            {% for func in functions %}
                                            <span class="badge bg-secondary me-1 mb-1">{{ func }}</span>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}

                            <!-- Exports -->
                            {% if report.static.pe.exports %}
                            <div class="mb-4">
                                <h5><i class="fas fa-upload me-2"></i>Exported Functions</h5>
                                <div class="bg-light p-2 rounded">
                                    {% for export in report.static.pe.exports %}
                                    <span class="badge bg-success me-1 mb-1">{{ export.name if export.name else export.address }}</span>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}

                            <!-- Version Info -->
                            {% if report.static.pe.versioninfo %}
                            <div class="mb-4">
                                <h5><i class="fas fa-info me-2"></i>Version Information</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        {% for key, value in report.static.pe.versioninfo.items() %}
                                        <tr>
                                            <th>{{ key }}</th>
                                            <td>{{ value }}</td>
                                        </tr>
                                        {% endfor %}
                                    </table>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
