{% extends "base_report.html" %}

{% block title %}Behavior Analysis - {{ report.info.id }}{% endblock %}

{% block content %}
<!-- Report Header -->
<div class="report-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 mb-2">
                    <i class="fas fa-microscope me-3"></i>
                    Behavior Analysis
                </h1>
                <p class="lead mb-0">
                    Detailed behavioral analysis of: <strong>{{ report.target.file.name }}</strong>
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="#" class="btn btn-custom btn-outline-light">
                    <i class="fas fa-download me-2"></i>
                    Export Report
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Process Tree -->
    {% if report.behavior.processes %}
    <section id="processes" class="report-section">
        <h2 class="section-title">
            <i class="fas fa-sitemap me-2"></i>
            Process Tree ({{ report.behavior.processes|length }} processes)
        </h2>
        
        <div class="mb-3">
            <input type="text" class="form-control search-box" id="processSearch" 
                   placeholder="Search processes...">
        </div>
        
        <div class="timeline" id="processTimeline">
            {% for process in report.behavior.processes %}
            <div class="timeline-item process-item" data-pid="{{ process.pid }}">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h5 class="mb-1">
                        <i class="fas fa-cog me-2"></i>
                        {{ process.process_name }}
                        <small class="text-muted">(PID: {{ process.pid }})</small>
                    </h5>
                    <span class="badge bg-primary">{{ process.calls|length }} calls</span>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-1"><strong>Command Line:</strong></p>
                        <div class="code-block">{{ process.command_line or 'N/A' }}</div>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1"><strong>Parent PID:</strong> {{ process.ppid or 'N/A' }}</p>
                        <p class="mb-1"><strong>Started:</strong> {{ process.first_seen or 'N/A' }}</p>
                        <p class="mb-1"><strong>Ended:</strong> {{ process.last_seen or 'N/A' }}</p>
                    </div>
                </div>
                
                <!-- API Calls Summary -->
                {% if process.calls %}
                <div class="mt-3">
                    <h6><i class="fas fa-list me-2"></i>API Calls Summary</h6>
                    <div class="row">
                        {% set api_summary = process.calls | groupby('api') %}
                        {% for api, calls in api_summary[:6] %}
                        <div class="col-md-4 col-sm-6 mb-2">
                            <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                                <span class="fw-bold">{{ api }}</span>
                                <span class="badge bg-secondary">{{ calls|length }}</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    {% if api_summary|length > 6 %}
                    <button class="btn btn-sm btn-outline-primary mt-2" type="button" 
                            data-bs-toggle="collapse" data-bs-target="#moreApis{{ process.pid }}">
                        <i class="fas fa-plus me-1"></i>
                        Show {{ api_summary|length - 6 }} more APIs
                    </button>
                    <div class="collapse mt-2" id="moreApis{{ process.pid }}">
                        <div class="row">
                            {% for api, calls in api_summary[6:] %}
                            <div class="col-md-4 col-sm-6 mb-2">
                                <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                                    <span class="fw-bold">{{ api }}</span>
                                    <span class="badge bg-secondary">{{ calls|length }}</span>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
                
                <!-- Detailed API Calls -->
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-info" type="button" 
                            data-bs-toggle="collapse" data-bs-target="#apiCalls{{ process.pid }}">
                        <i class="fas fa-code me-1"></i>
                        View Detailed API Calls ({{ process.calls|length }})
                    </button>
                    <div class="collapse mt-2" id="apiCalls{{ process.pid }}">
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>Timestamp</th>
                                        <th>API</th>
                                        <th>Arguments</th>
                                        <th>Return</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for call in process.calls[:50] %}
                                    <tr>
                                        <td><small>{{ call.timestamp }}</small></td>
                                        <td><code>{{ call.api }}</code></td>
                                        <td>
                                            <small>
                                                {% for arg in call.arguments[:3] %}
                                                    {{ arg.name }}={{ arg.value|truncate(30) }}{% if not loop.last %}, {% endif %}
                                                {% endfor %}
                                                {% if call.arguments|length > 3 %}...{% endif %}
                                            </small>
                                        </td>
                                        <td><small>{{ call.return_value|truncate(20) }}</small></td>
                                    </tr>
                                    {% endfor %}
                                    {% if process.calls|length > 50 %}
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">
                                            <em>... and {{ process.calls|length - 50 }} more calls</em>
                                        </td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </section>
    {% endif %}

    <!-- File System Activity -->
    {% if report.behavior.summary.files %}
    <section class="report-section">
        <h2 class="section-title">
            <i class="fas fa-folder-open me-2"></i>
            File System Activity
        </h2>
        
        <div class="row">
            <!-- Files Created -->
            {% if report.behavior.summary.files %}
            <div class="col-md-6">
                <h4><i class="fas fa-plus-circle me-2 text-success"></i>Files Created</h4>
                <div class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;">
                    {% for file in report.behavior.summary.files %}
                    <div class="mb-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <code class="text-break">{{ file }}</code>
                            <small class="text-muted ms-2">
                                <i class="fas fa-file"></i>
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            <!-- Registry Keys -->
            {% if report.behavior.summary.keys %}
            <div class="col-md-6">
                <h4><i class="fas fa-key me-2 text-warning"></i>Registry Keys</h4>
                <div class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;">
                    {% for key in report.behavior.summary.keys %}
                    <div class="mb-2">
                        <code class="text-break">{{ key }}</code>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </section>
    {% endif %}

    <!-- Dropped Files -->
    {% if report.dropped %}
    <section class="report-section">
        <h2 class="section-title">
            <i class="fas fa-download me-2"></i>
            Dropped Files ({{ report.dropped|length }})
        </h2>
        
        <div class="table-responsive">
            <table class="table table-custom">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Size</th>
                        <th>Type</th>
                        <th>MD5</th>
                        <th>Path</th>
                    </tr>
                </thead>
                <tbody>
                    {% for file in report.dropped %}
                    <tr>
                        <td><strong>{{ file.name }}</strong></td>
                        <td>{{ file.size|filesizeformat }}</td>
                        <td>
                            <span class="badge bg-info">{{ file.type }}</span>
                        </td>
                        <td><code>{{ file.md5 }}</code></td>
                        <td><small>{{ file.path }}</small></td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </section>
    {% endif %}

    <!-- Extracted Strings -->
    {% if report.strings %}
    <section class="report-section">
        <h2 class="section-title">
            <i class="fas fa-font me-2"></i>
            Extracted Strings ({{ report.strings|length }})
        </h2>
        
        <div class="mb-3">
            <input type="text" class="form-control search-box" id="stringSearch" 
                   placeholder="Search strings...">
        </div>
        
        <div class="bg-light p-3 rounded" style="max-height: 600px; overflow-y: auto;" id="stringsContainer">
            {% for string in report.strings %}
            <div class="mb-1 string-item">
                <code>{{ string }}</code>
            </div>
            {% endfor %}
        </div>
    </section>
    {% endif %}

    <!-- Memory Analysis -->
    {% if report.memory %}
    <section class="report-section">
        <h2 class="section-title">
            <i class="fas fa-memory me-2"></i>
            Memory Analysis
        </h2>
        
        {% if report.memory.imginfo %}
        <div class="row">
            <div class="col-md-6">
                <h4><i class="fas fa-info-circle me-2"></i>Image Information</h4>
                <table class="table table-custom">
                    <tbody>
                        {% for key, value in report.memory.imginfo.items() %}
                        <tr>
                            <td><strong>{{ key|title }}</strong></td>
                            <td>{{ value }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
        
        {% if report.memory.pslist %}
        <h4><i class="fas fa-list me-2"></i>Process List</h4>
        <div class="table-responsive">
            <table class="table table-custom">
                <thead>
                    <tr>
                        <th>PID</th>
                        <th>PPID</th>
                        <th>Name</th>
                        <th>Threads</th>
                        <th>Handles</th>
                        <th>Start Time</th>
                    </tr>
                </thead>
                <tbody>
                    {% for process in report.memory.pslist %}
                    <tr>
                        <td>{{ process.pid }}</td>
                        <td>{{ process.ppid }}</td>
                        <td><strong>{{ process.name }}</strong></td>
                        <td>{{ process.threads }}</td>
                        <td>{{ process.handles }}</td>
                        <td><small>{{ process.start_time }}</small></td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
    </section>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Process search functionality
    const processSearch = document.getElementById('processSearch');
    if (processSearch) {
        processSearch.addEventListener('input', function() {
            const filter = this.value.toLowerCase();
            const items = document.querySelectorAll('.process-item');
            
            items.forEach(function(item) {
                const text = item.textContent.toLowerCase();
                item.style.display = text.includes(filter) ? 'block' : 'none';
            });
        });
    }
    
    // String search functionality
    const stringSearch = document.getElementById('stringSearch');
    if (stringSearch) {
        stringSearch.addEventListener('input', function() {
            const filter = this.value.toLowerCase();
            const items = document.querySelectorAll('.string-item');
            
            items.forEach(function(item) {
                const text = item.textContent.toLowerCase();
                item.style.display = text.includes(filter) ? 'block' : 'none';
            });
        });
    }
    
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
</script>
{% endblock %}
