<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CAPE Web Service{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .status-pending { color: #ffc107; }
        .status-running { color: #0dcaf0; }
        .status-processing { color: #0d6efd; }
        .status-completed { color: #198754; }
        .status-reported { color: #198754; }
        .status-failed { color: #dc3545; }
        .analysis-card {
            transition: transform 0.2s;
        }
        .analysis-card:hover {
            transform: translateY(-2px);
        }
        .file-info {
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            padding: 1rem;
        }
        .option-group {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .report-section {
            margin-bottom: 2rem;
        }
        .report-section h4 {
            border-bottom: 2px solid #0d6efd;
            padding-bottom: 0.5rem;
            margin-bottom: 1rem;
        }
        .json-viewer {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            max-height: 500px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
        .badge-clickable {
            cursor: pointer;
            transition: all 0.2s;
        }
        .badge-clickable:hover {
            transform: scale(1.05);
            opacity: 0.8;
        }
        .hash-value {
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .hash-value:hover {
            background-color: #e9ecef;
        }
        .section-header {
            border-left: 4px solid #0d6efd;
            padding-left: 1rem;
            margin-bottom: 1rem;
        }
        .severity-high { border-left: 4px solid #dc3545; }
        .severity-medium { border-left: 4px solid #ffc107; }
        .severity-low { border-left: 4px solid #198754; }
        .network-card {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background-color: #f8f9fa;
        }
        .process-tree {
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
        .api-call {
            display: inline-block;
            margin: 0.125rem;
            padding: 0.25rem 0.5rem;
            background-color: #e9ecef;
            border-radius: 0.25rem;
            font-size: 0.75rem;
        }
        .table-search {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: white;
            padding: 0.5rem 0;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-shield-virus me-2"></i>CAPE Web Service
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-upload me-1"></i>Upload
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('list_analyses') }}">
                            <i class="fas fa-list me-1"></i>Analyses
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('json_viewer') }}">
                            <i class="fas fa-file-code me-1"></i>JSON Viewer
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <main class="container mt-4">
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <footer class="bg-light mt-5 py-4">
        <div class="container text-center">
            <p class="text-muted mb-0">
                <i class="fas fa-shield-virus me-2"></i>CAPE Web Service - Malware Analysis Platform
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
