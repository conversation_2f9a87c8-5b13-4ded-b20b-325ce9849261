# Hướng dẫn tạo VM bằng virt-manager và quản lý bằng terminal

## Tổng quan
Tài liệu này hướng dẫn chi tiết cách tạo máy ảo KVM bằng virt-manager GUI và quản lý bằng lệnh terminal cho CAPEv2.

## PHẦN 1: Cài đặt và chuẩn bị

### 1.1. Cài đặt virt-manager và dependencies
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y virt-manager qemu-kvm libvirt-daemon-system libvirt-clients bridge-utils virtinst virt-viewer

# CentOS/RHEL
sudo yum install -y virt-manager qemu-kvm libvirt virt-install virt-viewer bridge-utils

# Start và enable libvirt
sudo systemctl start libvirtd
sudo systemctl enable libvirtd

# Thêm user vào groups
sudo usermod -aG libvirt $USER
sudo usermod -aG kvm $USER

# Logout và login lại để áp dụng groups
```

### 1.2. <PERSON><PERSON><PERSON> bị thư mục và ISO
```bash
# Tạo thư mục cho VMs
sudo mkdir -p /var/lib/libvirt/images/cape
sudo mkdir -p /var/lib/libvirt/images/iso

# Set permissions
sudo chown -R libvirt-qemu:libvirt-qemu /var/lib/libvirt/images/cape
sudo chmod 755 /var/lib/libvirt/images/cape
sudo chmod 755 /var/lib/libvirt/images/iso

# Copy Windows ISO files (thay đổi path theo thực tế)
# sudo cp /path/to/windows10.iso /var/lib/libvirt/images/iso/
# sudo cp /path/to/windows7.iso /var/lib/libvirt/images/iso/
```

### 1.3. Tạo network cho CAPE
```bash
# Tạo file network XML
cat > /tmp/cape-network.xml << 'EOF'
<network>
  <name>cape-network</name>
  <forward mode='nat'>
    <nat>
      <port start='1024' end='65535'/>
    </nat>
  </forward>
  <bridge name='virbr1' stp='on' delay='0'/>
  <ip address='*************' netmask='*************'>
    <dhcp>
      <range start='**************' end='***************'/>
    </dhcp>
  </ip>
</network>
EOF

# Define và start network
sudo virsh net-define /tmp/cape-network.xml
sudo virsh net-start cape-network
sudo virsh net-autostart cape-network

# Kiểm tra network
sudo virsh net-list --all
```

## PHẦN 2: Tạo VM bằng virt-manager GUI

### 2.1. Khởi động virt-manager
```bash
# Khởi động virt-manager
virt-manager

# Hoặc với sudo nếu cần
sudo virt-manager
```

### 2.2. Tạo VM cape1 (Windows 10 x64) - GUI Steps

#### Bước 1: New VM
1. Click "Create a new virtual machine"
2. Chọn "Local install media (ISO image or CDROM)"
3. Click "Forward"

#### Bước 2: Choose ISO
1. Click "Browse" → "Browse Local"
2. Navigate to `/var/lib/libvirt/images/iso/`
3. Chọn `windows10.iso`
4. OS type: "Microsoft Windows"
5. Version: "Microsoft Windows 10"
6. Click "Forward"

#### Bước 3: Memory and CPU
1. Memory (RAM): `4096` MB
2. CPUs: `2`
3. Click "Forward"

#### Bước 4: Storage
1. Chọn "Create a disk image for the virtual machine"
2. Size: `50.0` GB
3. Click "Forward"

#### Bước 5: Final Configuration
1. Name: `cape1`
2. Network selection: `cape-network`
3. Check "Customize configuration before install"
4. Click "Finish"

#### Bước 6: Customize Hardware
1. **Overview**: 
   - Chipset: `Q35`
   - Firmware: `UEFI x86_64`

2. **CPUs**:
   - Current allocation: `2`
   - Topology: Manual, Sockets: `1`, Cores: `2`, Threads: `1`

3. **Memory**:
   - Current allocation: `4096` MB

4. **Disk**:
   - Disk bus: `VirtIO`
   - Cache mode: `none`

5. **Network**:
   - Network source: `Virtual network 'cape-network': NAT`
   - Device model: `virtio`

6. **Add Hardware** → **Graphics**:
   - Type: `VNC server`
   - Listen type: `All interfaces`
   - Port: `5901`

7. Click "Begin Installation"

### 2.3. Tạo VM cuckoo1 (Windows 7 x86) - GUI Steps

Lặp lại các bước tương tự với:
- Name: `cuckoo1`
- ISO: `windows7.iso`
- OS Version: "Microsoft Windows 7"
- Memory: `2048` MB
- Storage: `40.0` GB
- VNC Port: `5902`

## PHẦN 3: Quản lý VM bằng lệnh terminal

### 3.1. Lệnh cơ bản quản lý VM

#### Xem danh sách VMs
```bash
# Xem tất cả VMs
sudo virsh list --all

# Xem chỉ VMs đang chạy
sudo virsh list

# Xem thông tin chi tiết VM
sudo virsh dominfo cape1
sudo virsh dominfo cuckoo1
```

#### Start/Stop VMs
```bash
# Start VMs
sudo virsh start cape1
sudo virsh start cuckoo1

# Shutdown VMs (graceful)
sudo virsh shutdown cape1
sudo virsh shutdown cuckoo1

# Force shutdown VMs
sudo virsh destroy cape1
sudo virsh destroy cuckoo1

# Reboot VMs
sudo virsh reboot cape1
sudo virsh reboot cuckoo1

# Auto-start VMs khi boot host
sudo virsh autostart cape1
sudo virsh autostart cuckoo1

# Disable auto-start
sudo virsh autostart --disable cape1
sudo virsh autostart --disable cuckoo1
```

#### Suspend/Resume VMs
```bash
# Suspend VMs
sudo virsh suspend cape1
sudo virsh suspend cuckoo1

# Resume VMs
sudo virsh resume cape1
sudo virsh resume cuckoo1

# Save VM state to file
sudo virsh save cape1 /var/lib/libvirt/images/cape/cape1.save

# Restore VM from saved state
sudo virsh restore /var/lib/libvirt/images/cape/cape1.save
```

### 3.2. Quản lý snapshots

#### Tạo snapshots
```bash
# Tạo snapshot cho cape1
sudo virsh snapshot-create-as cape1 clean "Clean snapshot for malware analysis"

# Tạo snapshot cho cuckoo1
sudo virsh snapshot-create-as cuckoo1 clean "Clean snapshot for malware analysis"

# Tạo snapshot với metadata
sudo virsh snapshot-create-as cape1 infected "After malware infection" --description "Snapshot after running malware sample"
```

#### Quản lý snapshots
```bash
# Xem danh sách snapshots
sudo virsh snapshot-list cape1
sudo virsh snapshot-list cuckoo1

# Xem thông tin snapshot
sudo virsh snapshot-info cape1 clean

# Revert về snapshot
sudo virsh snapshot-revert cape1 clean
sudo virsh snapshot-revert cuckoo1 clean

# Xóa snapshot
sudo virsh snapshot-delete cape1 infected
```

### 3.3. Quản lý network

#### Xem thông tin network
```bash
# Xem danh sách networks
sudo virsh net-list --all

# Xem thông tin chi tiết network
sudo virsh net-info cape-network

# Xem cấu hình XML của network
sudo virsh net-dumpxml cape-network

# Xem DHCP leases
sudo virsh net-dhcp-leases cape-network
```

#### Quản lý network
```bash
# Start/stop network
sudo virsh net-start cape-network
sudo virsh net-destroy cape-network

# Auto-start network
sudo virsh net-autostart cape-network

# Edit network configuration
sudo virsh net-edit cape-network
```

### 3.4. Quản lý storage

#### Xem thông tin disk
```bash
# Xem danh sách disks của VM
sudo virsh domblklist cape1
sudo virsh domblklist cuckoo1

# Xem thông tin disk
sudo qemu-img info /var/lib/libvirt/images/cape/cape1.qcow2
sudo qemu-img info /var/lib/libvirt/images/cape/cuckoo1.qcow2
```

#### Attach/Detach devices
```bash
# Attach ISO file
sudo virsh attach-disk cape1 /var/lib/libvirt/images/iso/cape-agent.iso hdc --type cdrom --mode readonly

# Detach ISO file
sudo virsh detach-disk cape1 hdc

# Attach USB device (ví dụ)
# sudo virsh attach-device cape1 usb-device.xml
```

### 3.5. Console và VNC access

#### Console access
```bash
# Kết nối console (nếu VM hỗ trợ)
sudo virsh console cape1

# Thoát console: Ctrl+]
```

#### VNC access
```bash
# Xem VNC port
sudo virsh vncdisplay cape1
sudo virsh vncdisplay cuckoo1

# Kết nối VNC từ máy khác
# vncviewer YOUR_HOST_IP:5901  # cape1
# vncviewer YOUR_HOST_IP:5902  # cuckoo1

# Hoặc sử dụng virt-viewer
virt-viewer cape1
virt-viewer cuckoo1
```

## PHẦN 4: Cấu hình VM sau khi cài Windows

### 4.1. Cấu hình IP tĩnh trong VMs

#### Trong cape1 (Windows 10):
```cmd
# Mở Command Prompt as Administrator
# Set IP tĩnh
netsh interface ip set address name="Ethernet" static *************** ************* *************

# Set DNS
netsh interface ip set dns name="Ethernet" static *******
netsh interface ip add dns name="Ethernet" ******* index=2

# Kiểm tra cấu hình
ipconfig /all
ping *************
ping *******
```

#### Trong cuckoo1 (Windows 7):
```cmd
# Mở Command Prompt as Administrator
# Set IP tĩnh
netsh interface ip set address name="Local Area Connection" static *************** ************* *************

# Set DNS
netsh interface ip set dns name="Local Area Connection" static *******
netsh interface ip add dns name="Local Area Connection" ******* index=2

# Kiểm tra cấu hình
ipconfig /all
ping *************
ping *******
```

### 4.2. Disable bảo mật trong VMs

#### Disable Windows Defender (Windows 10):
```powershell
# Mở PowerShell as Administrator
Set-MpPreference -DisableRealtimeMonitoring $true
Set-MpPreference -DisableBehaviorMonitoring $true
Set-MpPreference -DisableBlockAtFirstSeen $true
Set-MpPreference -DisableIOAVProtection $true
Set-MpPreference -DisablePrivacyMode $true
Set-MpPreference -DisableScriptScanning $true
```

#### Disable Windows Firewall (cả 2 VMs):
```cmd
# Command Prompt as Administrator
netsh advfirewall set allprofiles state off
```

#### Disable UAC (cả 2 VMs):
```cmd
# Command Prompt as Administrator
reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v EnableLUA /t REG_DWORD /d 0 /f

# Restart VM sau khi thay đổi
```

## PHẦN 5: Scripts tự động quản lý VMs

### 5.1. Script quản lý VMs
```bash
#!/bin/bash
# File: manage_cape_vms.sh

VM_LIST="cape1 cuckoo1"

case "$1" in
    start)
        echo "Starting CAPE VMs..."
        for vm in $VM_LIST; do
            echo "Starting $vm..."
            sudo virsh start $vm
        done
        echo "All VMs started"
        ;;
    stop)
        echo "Stopping CAPE VMs..."
        for vm in $VM_LIST; do
            echo "Stopping $vm..."
            sudo virsh shutdown $vm
        done
        echo "All VMs stopped"
        ;;
    force-stop)
        echo "Force stopping CAPE VMs..."
        for vm in $VM_LIST; do
            echo "Force stopping $vm..."
            sudo virsh destroy $vm
        done
        echo "All VMs force stopped"
        ;;
    restart)
        echo "Restarting CAPE VMs..."
        for vm in $VM_LIST; do
            echo "Restarting $vm..."
            sudo virsh shutdown $vm
        done
        sleep 10
        for vm in $VM_LIST; do
            echo "Starting $vm..."
            sudo virsh start $vm
        done
        echo "All VMs restarted"
        ;;
    status)
        echo "VM Status:"
        sudo virsh list --all
        ;;
    reset)
        echo "Resetting VMs to clean snapshots..."
        for vm in $VM_LIST; do
            echo "Resetting $vm..."
            sudo virsh shutdown $vm
        done
        sleep 15
        for vm in $VM_LIST; do
            echo "Reverting $vm to clean snapshot..."
            sudo virsh snapshot-revert $vm clean
            sudo virsh start $vm
        done
        echo "All VMs reset to clean snapshots"
        ;;
    snapshot)
        if [ -z "$2" ]; then
            echo "Usage: $0 snapshot <snapshot_name>"
            exit 1
        fi
        echo "Creating snapshot '$2' for all VMs..."
        for vm in $VM_LIST; do
            echo "Creating snapshot for $vm..."
            sudo virsh snapshot-create-as $vm $2 "Snapshot created on $(date)"
        done
        echo "Snapshots created"
        ;;
    list-snapshots)
        for vm in $VM_LIST; do
            echo "Snapshots for $vm:"
            sudo virsh snapshot-list $vm
            echo ""
        done
        ;;
    *)
        echo "Usage: $0 {start|stop|force-stop|restart|status|reset|snapshot <name>|list-snapshots}"
        echo ""
        echo "Commands:"
        echo "  start           - Start all VMs"
        echo "  stop            - Gracefully shutdown all VMs"
        echo "  force-stop      - Force stop all VMs"
        echo "  restart         - Restart all VMs"
        echo "  status          - Show VM status"
        echo "  reset           - Reset VMs to clean snapshots"
        echo "  snapshot <name> - Create snapshot with given name"
        echo "  list-snapshots  - List all snapshots"
        exit 1
        ;;
esac
```

### 5.2. Script monitoring VMs
```bash
#!/bin/bash
# File: monitor_cape_vms.sh

VM_LIST="cape1 cuckoo1"
VM_IPS="*************** ***************"

echo "CAPE VMs Monitoring Report - $(date)"
echo "=========================================="

# Check VM status
echo "VM Status:"
sudo virsh list --all | grep -E "(cape1|cuckoo1)"
echo ""

# Check network connectivity
echo "Network Connectivity:"
for ip in $VM_IPS; do
    if ping -c 1 -W 2 $ip >/dev/null 2>&1; then
        echo "✓ $ip: Reachable"
    else
        echo "✗ $ip: Not reachable"
    fi
done
echo ""

# Check result server port
echo "Result Server Port (2042):"
if netstat -tlnp | grep :2042 >/dev/null; then
    echo "✓ Port 2042: Listening"
else
    echo "✗ Port 2042: Not listening"
fi
echo ""

# Check network interface
echo "Network Interface:"
if ip link show virbr1 >/dev/null 2>&1; then
    echo "✓ virbr1: Available"
    ip addr show virbr1 | grep "inet "
else
    echo "✗ virbr1: Not found"
fi
echo ""

# Check snapshots
echo "Available Snapshots:"
for vm in $VM_LIST; do
    echo "$vm snapshots:"
    sudo virsh snapshot-list $vm --name 2>/dev/null | sed 's/^/  /'
done
echo ""

# Check disk usage
echo "Disk Usage:"
for vm in $VM_LIST; do
    disk_path="/var/lib/libvirt/images/cape/${vm}.qcow2"
    if [ -f "$disk_path" ]; then
        size=$(du -h "$disk_path" | cut -f1)
        echo "$vm: $size"
    fi
done
```

### 5.3. Cách sử dụng scripts

```bash
# Tạo thư mục scripts
mkdir -p /opt/CAPEv2/scripts

# Copy scripts
cp manage_cape_vms.sh /opt/CAPEv2/scripts/
cp monitor_cape_vms.sh /opt/CAPEv2/scripts/

# Set permissions
chmod +x /opt/CAPEv2/scripts/*.sh

# Sử dụng scripts
cd /opt/CAPEv2/scripts

# Start VMs
./manage_cape_vms.sh start

# Stop VMs
./manage_cape_vms.sh stop

# Reset VMs về clean snapshot
./manage_cape_vms.sh reset

# Tạo snapshot mới
./manage_cape_vms.sh snapshot backup-$(date +%Y%m%d)

# Monitor VMs
./monitor_cape_vms.sh

# Xem status
./manage_cape_vms.sh status
```

## PHẦN 6: Troubleshooting

### 6.1. Lỗi thường gặp

#### VM không start
```bash
# Kiểm tra libvirt service
sudo systemctl status libvirtd

# Restart libvirt
sudo systemctl restart libvirtd

# Kiểm tra VM definition
sudo virsh dumpxml cape1

# Check logs
sudo tail -f /var/log/libvirt/qemu/cape1.log
```

#### Không kết nối được VNC
```bash
# Kiểm tra VNC port
sudo virsh vncdisplay cape1

# Kiểm tra firewall
sudo ufw status
sudo iptables -L | grep 590

# Allow VNC ports
sudo ufw allow 5901
sudo ufw allow 5902
```

#### Network không hoạt động
```bash
# Restart network
sudo virsh net-destroy cape-network
sudo virsh net-start cape-network

# Kiểm tra bridge
brctl show
ip addr show virbr1
```

### 6.2. Lệnh debug hữu ích

```bash
# Xem logs chi tiết
sudo tail -f /var/log/libvirt/libvirtd.log

# Kiểm tra qemu process
ps aux | grep qemu

# Kiểm tra network namespace
sudo ip netns list

# Test QEMU trực tiếp
sudo qemu-system-x86_64 -version

# Kiểm tra KVM support
sudo kvm-ok
lscpu | grep Virtualization
```

## Tóm tắt lệnh quan trọng

```bash
# Quản lý VMs
sudo virsh list --all                    # Xem tất cả VMs
sudo virsh start cape1 cuckoo1          # Start VMs
sudo virsh shutdown cape1 cuckoo1       # Stop VMs
sudo virsh snapshot-revert cape1 clean  # Reset snapshot

# Monitoring
sudo virsh dominfo cape1                # Thông tin VM
sudo virsh vncdisplay cape1             # VNC port
ping ***************                    # Test connectivity

# Network
sudo virsh net-list --all               # Xem networks
sudo virsh net-dhcp-leases cape-network # DHCP leases
```
