#!/usr/bin/env python3
"""
CAPE Web Service Runner
Simple script to run the web service
"""

import os
import sys

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app

if __name__ == '__main__':
    print("=" * 60)
    print("🛡️  CAPE Web Service")
    print("=" * 60)
    print(f"📡 CAPE Server: {app.config['CAPE_URL']}")
    print(f"📁 Upload folder: {app.config['UPLOAD_FOLDER']}")
    print(f"⏱️  Analysis timeout: {app.config['CAPE_TIMEOUT']}s")
    print(f"⏳ Max wait time: {app.config['CAPE_MAX_WAIT']}s")
    print("=" * 60)
    print("🌐 Web interface: http://localhost:5000")
    print("📋 API endpoints:")
    print("   POST /api/analyze - Submit file for analysis")
    print("   GET /api/status/<analysis_id> - Check status")
    print("   GET /api/report/<analysis_id> - Get report")
    print("=" * 60)
    print("🚀 Starting server...")
    print()
    
    app.run(host='0.0.0.0', port=5000, debug=True)
