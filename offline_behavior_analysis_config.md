# Cấu hình CAPEv2 cho Behavior Analysis Offline

## Tổng quan
Tài liệu này hướng dẫn cấu hình CAPEv2 để chạy hoàn toàn offline, chỉ phân tích behavior mà không có network analysis, tcpdump, hay VirusTotal.

## BƯỚC 1: Cấu hình routing.conf (Hoàn toàn offline)

### File: conf/routing.conf
```ini
[routing]
# Disable pcap generation hoàn toàn
enable_pcap = no

# Không có routing - hoàn toàn cô lập
route = none

# Không có internet interface
internet = none

# Disable NAT
nat = no

# Disable local routing
no_local_routing = no

# Routing table
rt_table = main

# Reject tất cả segments
reject_segments = none

# Reject tất cả host ports
reject_hostports = none

# Disable auto routing
auto_rt = no

# Enable drop route để chặn hoàn toàn
drop = yes

# Disable interface verification
verify_interface = no

# Disable routing table verification
verify_rt_table = no

[inetsim]
# Disable InetSim
enabled = no

[tor]
# Disable Tor
enabled = no

[vpn]
# Disable VPN
enabled = no

[socks5]
# Disable SOCKS5
enabled = no
```

## BƯỚC 2: C<PERSON>u hình auxiliary.conf (Disable network modules)

### File: conf/auxiliary.conf
```ini
[auxiliary_modules]
# Disable tất cả network-related modules
sniffer = no
mitm = no
services = yes
curtain = no
human = no
disguise = no
filepickup = yes
reboot = no
screenshot = yes
procmon = yes
procmemory = yes

[sniffer]
# Disable network sniffer hoàn toàn
enabled = no

[mitm]
# Disable MITM
enabled = no

[services]
# Enable services monitoring (không cần network)
enabled = yes

[curtain]
# Disable curtain
enabled = no

[human]
# Disable human simulation
enabled = no

[disguise]
# Disable disguise
enabled = no

[filepickup]
# Enable file pickup (không cần network)
enabled = yes

[reboot]
# Disable reboot
enabled = no

[screenshot]
# Enable screenshots
enabled = yes

[procmon]
# Enable process monitoring
enabled = yes

[procmemory]
# Enable process memory analysis
enabled = yes
```

## BƯỚC 3: Cấu hình processing.conf (Chỉ behavior analysis)

### File: conf/processing.conf
```ini
[analysisinfo]
# Enable analysis info
enabled = yes

[behavior]
# Enable behavior analysis - QUAN TRỌNG NHẤT
enabled = yes

[debug]
# Enable debug info
enabled = yes

[dropped]
# Enable dropped files analysis
enabled = yes

[network]
# DISABLE network analysis
enabled = no

[static]
# Enable static analysis
enabled = yes

[strings]
# Enable strings extraction
enabled = yes

[targetinfo]
# Enable target info
enabled = yes

[virustotal]
# DISABLE VirusTotal (cần internet)
enabled = no

[suricata]
# DISABLE Suricata (network IDS)
enabled = no

[clamav]
# Enable ClamAV (local antivirus scan)
enabled = yes

[yara]
# Enable YARA rules (local)
enabled = yes

[procmemory]
# Enable process memory analysis
enabled = yes
idapro = no

[procmon]
# Enable process monitor
enabled = yes

[screenshots]
# Enable screenshots
enabled = yes

[memory]
# Enable memory dump analysis
enabled = yes

[misp]
# DISABLE MISP (cần internet)
enabled = no

[irma]
# DISABLE IRMA (cần internet)
enabled = no

[apkinfo]
# Enable APK info (cho Android)
enabled = yes

[droidmon]
# Enable Droidmon (cho Android)
enabled = yes

[googleplay]
# DISABLE Google Play (cần internet)
enabled = no

[resubmitexe]
# DISABLE resubmit (có thể cần network)
enabled = no

[curtain]
# DISABLE curtain
enabled = no

[dumptls]
# DISABLE TLS dump (network related)
enabled = no
```

## BƯỚC 4: Cấu hình reporting.conf (Chỉ local reporting)

### File: conf/reporting.conf
```ini
[jsondump]
# Enable JSON dump (local)
enabled = yes
indent = 4
calls = yes

[reporthtml]
# Enable HTML report (local)
enabled = yes

[mongodb]
# DISABLE MongoDB reporting (nếu không dùng)
enabled = no

[elasticsearch]
# DISABLE Elasticsearch (nếu không dùng)
enabled = no

[moloch]
# DISABLE Moloch (network analysis)
enabled = no

[resubmitexe]
# DISABLE resubmit
enabled = no

[mattermost]
# DISABLE Mattermost notifications
enabled = no

[callback]
# DISABLE callback
enabled = no

[misp]
# DISABLE MISP upload
enabled = no

[compressresults]
# Enable compress results
enabled = yes

[bsonlogs]
# Enable BSON logs
enabled = yes

[singlefile]
# Enable single file report
enabled = yes
```

## BƯỚC 5: Cấu hình cuckoo.conf (Behavior focus)

### File: conf/cuckoo.conf
```ini
[cuckoo]
# Sử dụng KVM
machinery = kvm

# Thư mục tmp
tmppath = /tmp

# Enable memory dump cho behavior analysis
memory_dump = yes

# Timeout cho analysis (tăng lên để có đủ thời gian)
analysis_timeout = 300

# Critical timeout
critical_timeout = 120

[resultserver]
# Result server vẫn cần cho behavior analysis
ip = 0.0.0.0
port = 2042
force_port = no
upload_max_size = 128

[processing]
# Timeout cho processing
analysis_timeout = 300
critical_timeout = 120

# DISABLE network-related processing
sort_pcap = no

# DISABLE DNS lookups
resolve_dns = no

[database]
# Database connection
connection = postgresql://cape:cape_password@localhost:5432/cape
timeout = 60

[timeouts]
# Timeouts
default = 300
critical = 120
vm = 120

[remotecontrol]
# Disable remote control
enabled = no
```

## BƯỚC 6: Cấu hình memory.conf (Enhanced behavior analysis)

### File: conf/memory.conf
```ini
[basic]
# Enable memory analysis cho behavior
enabled = yes

# Guest profile (auto-detect)
guest_profile = 

# Không xóa memory dump để phân tích sau
delete_memdump = no

[malfind]
# Tìm malicious code injection
enabled = yes
filter = yes

[apihooks]
# Phát hiện API hooks
enabled = yes
filter = yes

[pslist]
# List processes
enabled = yes
filter = no

[psxview]
# Cross-view process listing
enabled = yes
filter = no

[callbacks]
# System callbacks
enabled = yes
filter = no

[idt]
# Interrupt Descriptor Table
enabled = yes
filter = no

[timers]
# System timers
enabled = yes
filter = no

[messagehooks]
# Message hooks
enabled = yes
filter = no

[getsids]
# Security identifiers
enabled = yes
filter = no

[privs]
# Process privileges
enabled = yes
filter = no

[dlllist]
# DLL listing
enabled = yes
filter = yes

[handles]
# Process handles
enabled = yes
filter = yes

[ldrmodules]
# Loaded modules
enabled = yes
filter = yes

[mutantscan]
# Mutants scan
enabled = yes
filter = yes

[devicetree]
# Device tree
enabled = yes
filter = yes

[svcscan]
# Services scan
enabled = yes
filter = yes

[modscan]
# Modules scan
enabled = yes
filter = yes

[ssdt]
# System Service Descriptor Table
enabled = yes
filter = yes

[driverscan]
# Driver scan
enabled = yes
filter = yes

[filescan]
# File objects scan
enabled = yes
filter = yes

[netscan]
# DISABLE network scan
enabled = no

[regapi]
# Registry API calls
enabled = yes
filter = yes

[userassist]
# UserAssist registry
enabled = yes
filter = yes

[shellbags]
# Shell bags
enabled = yes
filter = yes

[volshell]
# Volatility shell
enabled = no

[windows]
# Windows analysis
enabled = yes
filter = yes
```

## BƯỚC 7: Script tự động cấu hình offline

### Script: apply_offline_config.sh
```bash
#!/bin/bash
# File: apply_offline_config.sh

CAPE_DIR="/opt/CAPEv2"
CONF_DIR="$CAPE_DIR/conf"

echo "Configuring CAPEv2 for offline behavior analysis..."

# Backup existing configs
echo "Backing up existing configs..."
mkdir -p $CONF_DIR/backup-offline
cp $CONF_DIR/*.conf $CONF_DIR/backup-offline/ 2>/dev/null || true

# Apply routing.conf (completely offline)
cat > $CONF_DIR/routing.conf << 'EOF'
[routing]
enable_pcap = no
route = none
internet = none
nat = no
no_local_routing = no
rt_table = main
reject_segments = none
reject_hostports = none
auto_rt = no
drop = yes
verify_interface = no
verify_rt_table = no

[inetsim]
enabled = no

[tor]
enabled = no

[vpn]
enabled = no

[socks5]
enabled = no
EOF

# Apply auxiliary.conf (disable network modules)
cat > $CONF_DIR/auxiliary.conf << 'EOF'
[auxiliary_modules]
sniffer = no
mitm = no
services = yes
curtain = no
human = no
disguise = no
filepickup = yes
reboot = no
screenshot = yes
procmon = yes
procmemory = yes

[sniffer]
enabled = no

[mitm]
enabled = no

[services]
enabled = yes

[filepickup]
enabled = yes

[screenshot]
enabled = yes

[procmon]
enabled = yes

[procmemory]
enabled = yes
EOF

# Apply processing.conf (behavior focus)
cat > $CONF_DIR/processing.conf << 'EOF'
[analysisinfo]
enabled = yes

[behavior]
enabled = yes

[debug]
enabled = yes

[dropped]
enabled = yes

[network]
enabled = no

[static]
enabled = yes

[strings]
enabled = yes

[targetinfo]
enabled = yes

[virustotal]
enabled = no

[suricata]
enabled = no

[clamav]
enabled = yes

[yara]
enabled = yes

[procmemory]
enabled = yes
idapro = no

[procmon]
enabled = yes

[screenshots]
enabled = yes

[memory]
enabled = yes

[misp]
enabled = no

[irma]
enabled = no

[resubmitexe]
enabled = no

[curtain]
enabled = no

[dumptls]
enabled = no
EOF

# Apply reporting.conf (local only)
cat > $CONF_DIR/reporting.conf << 'EOF'
[jsondump]
enabled = yes
indent = 4
calls = yes

[reporthtml]
enabled = yes

[mongodb]
enabled = no

[elasticsearch]
enabled = no

[moloch]
enabled = no

[resubmitexe]
enabled = no

[mattermost]
enabled = no

[callback]
enabled = no

[misp]
enabled = no

[compressresults]
enabled = yes

[bsonlogs]
enabled = yes

[singlefile]
enabled = yes
EOF

# Update cuckoo.conf for behavior focus
sed -i 's/memory_dump = .*/memory_dump = yes/' $CONF_DIR/cuckoo.conf
sed -i 's/analysis_timeout = .*/analysis_timeout = 300/' $CONF_DIR/cuckoo.conf
sed -i 's/critical_timeout = .*/critical_timeout = 120/' $CONF_DIR/cuckoo.conf
sed -i 's/sort_pcap = .*/sort_pcap = no/' $CONF_DIR/cuckoo.conf
sed -i 's/resolve_dns = .*/resolve_dns = no/' $CONF_DIR/cuckoo.conf

echo "Offline configuration applied successfully!"
echo ""
echo "Features ENABLED:"
echo "✓ Behavior Analysis"
echo "✓ Static Analysis"
echo "✓ Memory Analysis"
echo "✓ Process Monitoring"
echo "✓ Screenshots"
echo "✓ File Drops"
echo "✓ YARA Rules"
echo "✓ Local ClamAV"
echo ""
echo "Features DISABLED:"
echo "✗ Network Analysis"
echo "✗ PCAP Generation"
echo "✗ VirusTotal"
echo "✗ Internet Access"
echo "✗ DNS Resolution"
echo "✗ Suricata IDS"
echo "✗ MISP Integration"
echo ""
echo "Please restart CAPEv2 services to apply changes."
```

## BƯỚC 8: Kiểm tra cấu hình offline

### Script: check_offline_config.sh
```bash
#!/bin/bash
# File: check_offline_config.sh

CAPE_DIR="/opt/CAPEv2"
CONF_DIR="$CAPE_DIR/conf"

echo "Checking CAPEv2 offline configuration..."
echo "========================================"

# Check routing.conf
echo "Routing Configuration:"
if grep -q "route = none" $CONF_DIR/routing.conf; then
    echo "✓ Routing: Disabled (offline mode)"
else
    echo "✗ Routing: Still enabled"
fi

if grep -q "enable_pcap = no" $CONF_DIR/routing.conf; then
    echo "✓ PCAP: Disabled"
else
    echo "✗ PCAP: Still enabled"
fi

# Check auxiliary.conf
echo ""
echo "Auxiliary Modules:"
if grep -q "sniffer.*=.*no" $CONF_DIR/auxiliary.conf; then
    echo "✓ Network Sniffer: Disabled"
else
    echo "✗ Network Sniffer: Still enabled"
fi

# Check processing.conf
echo ""
echo "Processing Modules:"
if grep -q "behavior.*=.*yes" $CONF_DIR/processing.conf; then
    echo "✓ Behavior Analysis: Enabled"
else
    echo "✗ Behavior Analysis: Disabled"
fi

if grep -q "network.*=.*no" $CONF_DIR/processing.conf; then
    echo "✓ Network Analysis: Disabled"
else
    echo "✗ Network Analysis: Still enabled"
fi

if grep -q "virustotal.*=.*no" $CONF_DIR/processing.conf; then
    echo "✓ VirusTotal: Disabled"
else
    echo "✗ VirusTotal: Still enabled"
fi

# Check memory analysis
if grep -q "memory.*=.*yes" $CONF_DIR/processing.conf; then
    echo "✓ Memory Analysis: Enabled"
else
    echo "✗ Memory Analysis: Disabled"
fi

# Check reporting
echo ""
echo "Reporting:"
if grep -q "jsondump.*=.*yes" $CONF_DIR/reporting.conf; then
    echo "✓ JSON Reports: Enabled"
else
    echo "✗ JSON Reports: Disabled"
fi

if grep -q "reporthtml.*=.*yes" $CONF_DIR/reporting.conf; then
    echo "✓ HTML Reports: Enabled"
else
    echo "✗ HTML Reports: Disabled"
fi

echo ""
echo "Configuration check completed!"
echo "CAPEv2 is configured for offline behavior analysis only."
```

## BƯỚC 9: Test behavior analysis

### Script: test_behavior_analysis.sh
```bash
#!/bin/bash
# File: test_behavior_analysis.sh

CAPE_DIR="/opt/CAPEv2"

echo "Testing behavior analysis configuration..."

# Create test executable
cat > /tmp/test_behavior.py << 'EOF'
import os
import time
import subprocess

# Test file operations
with open("C:\\test_file.txt", "w") as f:
    f.write("Test behavior analysis")

# Test registry operations
try:
    import winreg
    key = winreg.CreateKey(winreg.HKEY_CURRENT_USER, "Software\\TestBehavior")
    winreg.SetValueEx(key, "TestValue", 0, winreg.REG_SZ, "BehaviorTest")
    winreg.CloseKey(key)
except:
    pass

# Test process creation
try:
    subprocess.run(["notepad.exe"], timeout=5)
except:
    pass

time.sleep(10)
EOF

echo "Test script created at /tmp/test_behavior.py"
echo ""
echo "To test behavior analysis:"
echo "1. Start CAPEv2: cd $CAPE_DIR && python3 cuckoo.py"
echo "2. Submit test: python3 utils/submit.py /tmp/test_behavior.py"
echo "3. Check results in web interface"
echo ""
echo "Expected behavior analysis results:"
echo "- File operations (create test_file.txt)"
echo "- Registry modifications"
echo "- Process creation (notepad.exe)"
echo "- API calls monitoring"
echo "- Memory analysis"
echo "- Screenshots"
```

## BƯỚC 10: Sử dụng

```bash
# Apply offline configuration
chmod +x apply_offline_config.sh
sudo ./apply_offline_config.sh

# Check configuration
chmod +x check_offline_config.sh
./check_offline_config.sh

# Restart CAPEv2 services
sudo systemctl restart cape-rooter
sudo systemctl restart cape
sudo systemctl restart cape-web

# Test behavior analysis
chmod +x test_behavior_analysis.sh
./test_behavior_analysis.sh

# Submit sample for behavior analysis only
cd /opt/CAPEv2
python3 utils/submit.py --timeout 300 /path/to/malware.exe
```

## Kết quả mong đợi

✅ **Behavior Analysis sẽ bao gồm:**
- API calls monitoring
- File system operations
- Registry modifications
- Process creation/injection
- Memory analysis
- Screenshots
- Dropped files analysis
- Static analysis (PE, strings, YARA)

❌ **Không có:**
- Network traffic analysis
- PCAP files
- VirusTotal lookups
- DNS queries
- Internet connectivity
- Suricata alerts

Cấu hình này tối ưu cho phân tích behavior offline hoàn toàn, tập trung vào hành vi của malware trong VM mà không cần network.

## BƯỚC 11: FIX DNS/VT Leakage - BLOCK HOÀN TOÀN

### 11.1. Block DNS hoàn toàn bằng iptables
```bash
#!/bin/bash
# File: block_all_dns_vt.sh

echo "Blocking ALL DNS and external connections..."

# Block DNS queries (port 53)
iptables -A OUTPUT -p udp --dport 53 -j DROP
iptables -A OUTPUT -p tcp --dport 53 -j DROP

# Block HTTP/HTTPS to VT and other services
iptables -A OUTPUT -p tcp --dport 80 -j DROP
iptables -A OUTPUT -p tcp --dport 443 -j DROP

# Block specific VT domains (backup)
iptables -A OUTPUT -d ******* -j DROP
iptables -A OUTPUT -d ******* -j DROP
iptables -A OUTPUT -d ******* -j DROP

# Allow only local communication
iptables -A OUTPUT -d 127.0.0.1 -j ACCEPT
iptables -A OUTPUT -d *************/24 -j ACCEPT
iptables -A OUTPUT -d ***********/24 -j ACCEPT

# Block everything else
iptables -A OUTPUT -j DROP

# Save rules
iptables-save > /etc/iptables/rules.v4

echo "All external connections blocked!"
```

### 11.2. Disable DNS trong tất cả modules
```bash
# File: disable_all_dns.sh

CAPE_DIR="/opt/CAPEv2"

# Disable DNS trong processing modules
find $CAPE_DIR/modules/processing -name "*.py" -exec sed -i 's/resolve_dns.*=.*True/resolve_dns = False/g' {} \;
find $CAPE_DIR/modules/processing -name "*.py" -exec sed -i 's/dns_lookup.*=.*True/dns_lookup = False/g' {} \;

# Disable VT trong tất cả modules
find $CAPE_DIR/modules -name "*.py" -exec sed -i 's/virustotal.*=.*True/virustotal = False/g' {} \;
find $CAPE_DIR/modules -name "*.py" -exec sed -i 's/vt_check.*=.*True/vt_check = False/g' {} \;

echo "DNS and VT disabled in all modules"
```

### 11.3. Cấu hình /etc/hosts để block VT domains
```bash
# File: block_vt_hosts.sh

# Add to /etc/hosts to block VT domains
cat >> /etc/hosts << 'EOF'
# Block VirusTotal and other threat intel services
127.0.0.1 www.virustotal.com
127.0.0.1 virustotal.com
127.0.0.1 api.virustotal.com
127.0.0.1 vtapi.virustotal.com
127.0.0.1 malwr.com
127.0.0.1 hybrid-analysis.com
127.0.0.1 joesandbox.com
127.0.0.1 threatminer.org
127.0.0.1 otx.alienvault.com
127.0.0.1 urlvoid.com
127.0.0.1 ipvoid.com
EOF

echo "VT domains blocked in /etc/hosts"
```

## BƯỚC 12: FIX Behavior Analysis - AGENT ISSUES

### 12.1. Kiểm tra agent trong VMs
```bash
#!/bin/bash
# File: check_agent_status.sh

echo "Checking CAPE agent status in VMs..."

# Test kết nối tới VMs
for vm_ip in *************01 *************02; do
    echo "Testing connection to $vm_ip..."

    # Test ping
    if ping -c 1 -W 2 $vm_ip >/dev/null 2>&1; then
        echo "✓ $vm_ip: Ping OK"
    else
        echo "✗ $vm_ip: Ping FAILED"
        continue
    fi

    # Test agent port (usually runs on random port, but result server is 2042)
    if timeout 3 bash -c "</dev/tcp/$vm_ip/2042" 2>/dev/null; then
        echo "✓ $vm_ip: Can connect to result server port"
    else
        echo "✗ $vm_ip: Cannot connect to result server"
        echo "  Need to check agent in VM"
    fi
done

# Check result server
echo ""
echo "Checking result server..."
if netstat -tlnp | grep :2042 >/dev/null; then
    echo "✓ Result server listening on port 2042"
else
    echo "✗ Result server NOT listening"
fi
```

### 12.2. Script cài đặt agent trong VMs
```bash
#!/bin/bash
# File: setup_agent_in_vms.sh

CAPE_DIR="/opt/CAPEv2"

echo "Creating agent ISO for VMs..."

# Tạo thư mục agent
mkdir -p /tmp/cape-agent-setup

# Copy agent files
cp $CAPE_DIR/agent/agent.py /tmp/cape-agent-setup/
cp -r $CAPE_DIR/agent/* /tmp/cape-agent-setup/ 2>/dev/null || true

# Tạo startup script cho Windows
cat > /tmp/cape-agent-setup/start_agent.bat << 'EOF'
@echo off
cd C:\cape-agent
python agent.py ************* 2042
EOF

# Tạo install script cho Windows
cat > /tmp/cape-agent-setup/install_agent.bat << 'EOF'
@echo off
echo Installing CAPE Agent...

REM Create directory
mkdir C:\cape-agent

REM Copy agent files
copy agent.py C:\cape-agent\
copy *.py C:\cape-agent\

REM Create startup shortcut
copy start_agent.bat "C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\"

echo Agent installed! Will start automatically on boot.
pause
EOF

# Tạo ISO
genisoimage -o /var/lib/libvirt/images/iso/cape-agent-setup.iso -V "CAPE_AGENT" -r -J /tmp/cape-agent-setup/

echo "Agent ISO created: /var/lib/libvirt/images/iso/cape-agent-setup.iso"
echo ""
echo "Next steps:"
echo "1. Attach ISO to VMs: sudo virsh attach-disk cape1 /var/lib/libvirt/images/iso/cape-agent-setup.iso hdc --type cdrom --mode readonly"
echo "2. In VM, run install_agent.bat as Administrator"
echo "3. Restart VM to test auto-start"
```

### 12.3. Debug behavior analysis
```bash
#!/bin/bash
# File: debug_behavior_analysis.sh

CAPE_DIR="/opt/CAPEv2"

echo "Debugging behavior analysis..."

# Check if behavior module is loaded
echo "Checking behavior processing module..."
if [ -f "$CAPE_DIR/modules/processing/behavior.py" ]; then
    echo "✓ Behavior module exists"

    # Check if it's enabled in config
    if grep -q "behavior.*=.*yes" $CAPE_DIR/conf/processing.conf; then
        echo "✓ Behavior module enabled in config"
    else
        echo "✗ Behavior module disabled in config"
    fi
else
    echo "✗ Behavior module missing!"
fi

# Check analysis logs
echo ""
echo "Recent analysis logs:"
if [ -f "$CAPE_DIR/log/cuckoo.log" ]; then
    tail -20 $CAPE_DIR/log/cuckoo.log | grep -i "behavior\|analysis\|agent"
else
    echo "No cuckoo.log found"
fi

# Check if VMs are reachable
echo ""
echo "VM connectivity:"
for vm_ip in *************01 *************02; do
    if ping -c 1 -W 2 $vm_ip >/dev/null 2>&1; then
        echo "✓ $vm_ip reachable"
    else
        echo "✗ $vm_ip not reachable"
    fi
done

# Check database for recent analyses
echo ""
echo "Recent analyses in database:"
cd $CAPE_DIR
python3 -c "
try:
    from lib.cuckoo.core.database import Database
    db = Database()
    analyses = db.list_tasks(limit=5)
    for analysis in analyses:
        print(f'ID: {analysis.id}, Status: {analysis.status}, Target: {analysis.target}')
except Exception as e:
    print(f'Database error: {e}')
"
```

## BƯỚC 13: Complete Fix Script

### Script: complete_offline_fix.sh
```bash
#!/bin/bash
# File: complete_offline_fix.sh

CAPE_DIR="/opt/CAPEv2"
CONF_DIR="$CAPE_DIR/conf"

echo "Applying COMPLETE offline fix for CAPEv2..."

# 1. Block ALL external connections
echo "1. Blocking all external connections..."
iptables -F OUTPUT
iptables -A OUTPUT -d 127.0.0.1 -j ACCEPT
iptables -A OUTPUT -d *************/24 -j ACCEPT
iptables -A OUTPUT -d ***********/24 -j ACCEPT
iptables -A OUTPUT -p udp --dport 53 -j DROP
iptables -A OUTPUT -p tcp --dport 80 -j DROP
iptables -A OUTPUT -p tcp --dport 443 -j DROP
iptables -A OUTPUT -j DROP
iptables-save > /etc/iptables/rules.v4

# 2. Block VT domains in hosts file
echo "2. Blocking VT domains..."
cat >> /etc/hosts << 'EOF'
127.0.0.1 www.virustotal.com
127.0.0.1 virustotal.com
127.0.0.1 api.virustotal.com
EOF

# 3. Disable DNS in all processing
echo "3. Disabling DNS resolution..."
sed -i 's/resolve_dns = on/resolve_dns = off/g' $CONF_DIR/cuckoo.conf
sed -i 's/resolve_dns = yes/resolve_dns = no/g' $CONF_DIR/processing.conf

# 4. Force disable VT in all modules
echo "4. Disabling VirusTotal..."
find $CAPE_DIR/modules -name "*.py" -exec sed -i 's/enabled.*=.*True/enabled = False/g' {} \; 2>/dev/null || true

# 5. Update processing.conf with STRICT offline settings
cat > $CONF_DIR/processing.conf << 'EOF'
[analysisinfo]
enabled = yes

[behavior]
enabled = yes

[debug]
enabled = yes

[dropped]
enabled = yes

[network]
enabled = no

[static]
enabled = yes

[strings]
enabled = yes

[targetinfo]
enabled = yes

[virustotal]
enabled = no

[suricata]
enabled = no

[clamav]
enabled = no

[yara]
enabled = yes

[procmemory]
enabled = yes

[procmon]
enabled = yes

[screenshots]
enabled = yes

[memory]
enabled = yes

[misp]
enabled = no
EOF

# 6. Restart services
echo "5. Restarting services..."
systemctl restart cape 2>/dev/null || true

echo ""
echo "COMPLETE OFFLINE FIX APPLIED!"
echo ""
echo "Next steps to fix behavior analysis:"
echo "1. Check VMs have agent installed and running"
echo "2. Test with: python3 utils/submit.py --timeout 300 /bin/ls"
echo "3. Check logs: tail -f log/cuckoo.log"
```

## BƯỚC 14: Manual Agent Setup trong VMs

### Trong cape1 và cuckoo1 (Windows):

1. **Download Python** (nếu chưa có):
   - Vào https://python.org/downloads/
   - Cài Python 3.8+

2. **Copy agent từ ISO**:
   ```cmd
   # Mount ISO trong VM
   # Copy tất cả files từ CD vào C:\cape-agent\
   ```

3. **Tạo startup script**:
   ```cmd
   # Tạo file C:\cape-agent\start_agent.bat
   @echo off
   cd C:\cape-agent
   python agent.py ************* 2042
   ```

4. **Auto-start agent**:
   ```cmd
   # Copy start_agent.bat vào:
   # C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\
   ```

5. **Test agent**:
   ```cmd
   # Chạy thủ công để test:
   cd C:\cape-agent
   python agent.py ************* 2042
   ```

## Sử dụng:

```bash
# Apply complete fix
chmod +x complete_offline_fix.sh
sudo ./complete_offline_fix.sh

# Check agent status
chmod +x check_agent_status.sh
./check_agent_status.sh

# Debug behavior analysis
chmod +x debug_behavior_analysis.sh
./debug_behavior_analysis.sh

# Test analysis
cd /opt/CAPEv2
python3 utils/submit.py --timeout 300 /bin/ls
```

Với fix này, sẽ **HOÀN TOÀN BLOCK** mọi DNS/VT leakage và fix behavior analysis!
