#!/usr/bin/env python3
"""
CAPE Analysis Service for Web Service
Based on cape_simple_service.py
"""

import requests
import time
import json
import os
import hashlib
from pathlib import Path


class CAPEAnalysisService:
    def __init__(self, cape_url="http://localhost:8000", timeout=300, max_wait=1800, verbose=False):
        """
        Initialize CAPE analysis service
        
        Args:
            cape_url: Base URL of CAPEv2 web interface
            timeout: Analysis timeout in seconds
            max_wait: Maximum time to wait for completion
            verbose: Show detailed progress
        """
        self.cape_url = cape_url.rstrip('/')
        self.timeout = timeout
        self.max_wait = max_wait
        self.verbose = verbose
        self.session = requests.Session()
        
        # Default analysis options
        self.default_options = {
            'procmemdump': '1',
            'import_reconstruction': '1',
            'unpacker': '2',
            'norefer': '1',
            'no-iat': '1'
        }

    def _print(self, message, force=False):
        """Print message only if verbose mode is on or force is True"""
        if self.verbose or force:
            print(message)
    
    def calculate_file_hash(self, file_path, hash_type='md5'):
        """Calculate file hash"""
        try:
            hash_func = getattr(hashlib, hash_type)()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_func.update(chunk)
            return hash_func.hexdigest()
        except Exception as e:
            self._print(f"Error calculating {hash_type}: {e}", force=True)
            return None

    def search_existing_analysis(self, file_hash):
        """Search for existing analysis by file hash"""
        url = f"{self.cape_url}/apiv2/tasks/search/md5/{file_hash}/"
        
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                result = response.json()
                if not result.get('error') and result.get('data'):
                    tasks = result['data']
                    if tasks:
                        # Check tasks from most recent to oldest
                        for task in sorted(tasks, key=lambda x: x.get('id', 0), reverse=True):
                            if task.get('status') in ['completed', 'reported']:
                                task_id = task['id']
                                self._print(f"Found existing analysis: Task ID {task_id}")
                                
                                # Verify report is actually available
                                if self._check_report_availability(task_id):
                                    self._print(f"Report confirmed available for Task ID {task_id}")
                                    return task_id
                                else:
                                    self._print(f"Report not available for Task ID {task_id}, checking next...")
                                    continue
            return None
        except Exception as e:
            self._print(f"Warning: Could not search existing analysis: {e}", force=True)
            return None

    def _check_report_availability(self, task_id):
        """Check if report is actually available for a task"""
        url = f"{self.cape_url}/apiv2/tasks/get/report/{task_id}/json/"

        try:
            response = self.session.get(url)
            if response.status_code == 200:
                # Check if the response contains actual report data or error
                try:
                    result = response.json()
                    if result.get('error') and 'still being analyzed' in str(result.get('error_value', '')).lower():
                        self._print(f"Report check: {result.get('error_value')}")
                        return False
                    # If no error or different error, consider it available
                    return not result.get('error', False)
                except:
                    # If can't parse JSON, but status is 200, consider it available
                    return True
            return False
        except:
            return False

    def get_machines(self):
        """Get list of available machines"""
        url = f"{self.cape_url}/apiv2/cuckoo/machines/"

        try:
            response = self.session.get(url)
            if response.status_code == 200:
                result = response.json()
                if not result.get('error') and result.get('data'):
                    machines = result['data']
                    self._print(f"Available machines: {[m.get('name') for m in machines]}")
                    return machines
            return []
        except Exception as e:
            self._print(f"Error getting machines: {e}")
            return []

    def submit_file(self, file_path, options=None, machine=None):
        """Submit file for analysis"""
        url = f"{self.cape_url}/apiv2/tasks/create/file/"

        try:
            with open(file_path, 'rb') as f:
                files = {'file': (os.path.basename(file_path), f)}
                data = {
                    'timeout': self.timeout,
                    'priority': 1
                }

                # Set machine if specified
                if machine and machine.lower() != 'first_available':
                    data['machine'] = machine
                    self._print(f"Using machine: {machine}")
                else:
                    self._print("Using first available machine")

                # Parse options string if provided
                merged_options = self.default_options.copy()
                if options:
                    if isinstance(options, str):
                        # Parse options string like "procmemdump=1,unpacker=2"
                        for option in options.split(','):
                            if '=' in option:
                                key, value = option.strip().split('=', 1)
                                merged_options[key.strip()] = value.strip()
                    elif isinstance(options, dict):
                        merged_options.update(options)

                # Handle analysis options
                analysis_options = []
                standard_options = ['machine', 'package', 'timeout', 'priority', 'memory', 'enforce_timeout']

                for key, value in merged_options.items():
                    if key in standard_options:
                        data[key] = value
                    else:
                        # Analysis-specific options go into options string
                        analysis_options.append(f"{key}={value}")

                # Combine analysis options into options string
                if analysis_options:
                    data['options'] = ','.join(analysis_options)
                    self._print(f"Analysis options: {data['options']}")

                self._print(f"Submitting with data: {data}")
                response = self.session.post(url, files=files, data=data)
                
                if response.status_code == 200:
                    result = response.json()
                    if not result.get('error') and result.get('data', {}).get('task_ids'):
                        task_id = result['data']['task_ids'][0]
                        self._print(f"File submitted successfully. Task ID: {task_id}")
                        return task_id
                    else:
                        self._print(f"Submission failed: {result.get('error_value', 'Unknown error')}", force=True)
                        return None
                else:
                    self._print(f"HTTP Error {response.status_code}: {response.text}", force=True)
                    return None
                    
        except Exception as e:
            self._print(f"Error submitting file: {e}", force=True)
            return None

    def check_status(self, task_id):
        """Check task status"""
        url = f"{self.cape_url}/apiv2/tasks/view/{task_id}/"

        try:
            response = self.session.get(url)
            if response.status_code == 200:
                result = response.json()
                if not result.get('error') and result.get('data'):
                    status = result['data'].get('status')
                    self._print(f"Status check for task {task_id}: {status}")
                    return status
                else:
                    self._print(f"Status check failed - no data or error in response")
            else:
                self._print(f"Status check failed with HTTP {response.status_code}")
            return None
        except Exception as e:
            self._print(f"Error checking status: {e}")
            return None

    def wait_for_completion(self, task_id):
        """Wait for analysis to complete"""
        self._print(f"Waiting for analysis completion (max {self.max_wait}s)...")

        start_time = time.time()
        last_status = None
        check_count = 0

        while time.time() - start_time < self.max_wait:
            status = self.check_status(task_id)
            check_count += 1

            if status != last_status:
                self._print(f"Status: {status} (check #{check_count})")
                last_status = status

            if status == 'pending':
                self._print("⏳ Task is pending...")
            elif status == 'running':
                self._print("🔄 Analysis is running...")
            elif status == 'processing':
                self._print("⚙️  Processing results...")
                # Continue waiting, don't return yet
            elif status == 'reported':
                self._print("✅ Analysis completed and reported!")
                return True
            elif status == 'completed':
                self._print("✅ Analysis completed!")
                # For completed status, wait a bit more to see if it becomes 'reported'
                # or check if report is available
                time.sleep(5)
                if self._check_report_availability(task_id):
                    self._print("✅ Report is available!")
                    return True
                # If no report yet, continue waiting
            elif status in ['failed_analysis', 'failed_processing', 'failed_reporting']:
                self._print(f"❌ Analysis failed with status: {status}", force=True)
                return False
            elif status is None:
                self._print(f"⚠️  Could not get status (check #{check_count})")
                # Try alternative method to check if report is available
                if self._check_report_availability(task_id):
                    self._print("✅ Report is available, assuming analysis completed!")
                    return True

            time.sleep(5)  # Check every 5 seconds

        self._print("⏰ Timeout waiting for analysis completion", force=True)
        self._print("🔍 Checking if report is available despite timeout...")

        # Final check - maybe the report is ready even if status check failed
        if self._check_report_availability(task_id):
            self._print("✅ Report found! Analysis appears to be complete.")
            return True

        return False
    
    def get_report(self, task_id, format='json'):
        """Get analysis report"""
        url = f"{self.cape_url}/apiv2/tasks/get/report/{task_id}/{format}/"

        try:
            self._print(f"Getting report from: {url}")
            response = self.session.get(url)
            self._print(f"Report response status: {response.status_code}")

            if response.status_code == 200:
                if format == 'json' or format == 'lite':
                    result = response.json()
                    # Check if the response contains an error about still being analyzed
                    if result.get('error'):
                        error_value = str(result.get('error_value', '')).lower()
                        self._print(f"Report contains error: {result.get('error_value')}")
                        if 'still being analyzed' in error_value:
                            self._print(f"🔍 DETECTED: Task is still being analyzed!")
                            return None
                    return result
                else:
                    return response.text
            elif response.status_code == 404:
                self._print(f"{format.upper()} report not found, trying alternatives...")

                # Try alternative formats if requested format fails
                if format == 'htmlsummary':
                    fallback_formats = ['lite', 'json']
                elif format in ['lite', 'json']:
                    fallback_formats = ['json', 'lite', 'htmlsummary']
                else:
                    fallback_formats = ['lite', 'json', 'htmlsummary']

                for fallback_format in fallback_formats:
                    fallback_url = f"{self.cape_url}/apiv2/tasks/get/report/{task_id}/{fallback_format}/"
                    try:
                        fallback_response = self.session.get(fallback_url)
                        if fallback_response.status_code == 200:
                            self._print(f"Using {fallback_format.upper()} format instead")
                            if fallback_format == 'json' or fallback_format == 'lite':
                                result = fallback_response.json()
                                # Check for error in fallback response too
                                if result.get('error') and 'still being analyzed' in str(result.get('error_value', '')).lower():
                                    self._print(f"Fallback report not ready yet: {result.get('error_value')}")
                                    return None
                                return result
                            else:
                                return fallback_response.text
                    except:
                        continue
            else:
                # Handle other HTTP status codes
                try:
                    error_response = response.json()
                    if error_response.get('error') and 'still being analyzed' in str(error_response.get('error_value', '')).lower():
                        self._print(f"Report not ready (HTTP {response.status_code}): {error_response.get('error_value')}")
                        return None
                except:
                    pass
                self._print(f"HTTP Error {response.status_code} when getting report")

            return None
        except Exception as e:
            self._print(f"Error getting report: {e}", force=True)
            return None
    
    def analyze_file(self, file_path, options=None, machine=None, force_reanalyze=False):
        """Complete analysis pipeline"""
        self._print(f"Starting analysis of: {file_path}")

        # Check if file exists
        if not os.path.exists(file_path):
            return {"error": True, "message": f"File not found: {file_path}"}

        # Calculate file hash
        file_hash = self.calculate_file_hash(file_path)
        if not file_hash:
            return {"error": True, "message": "Could not calculate file hash"}

        self._print(f"File hash (MD5): {file_hash}")

        # Check for existing analysis unless forced reanalysis
        task_id = None
        was_cached = False
        if not force_reanalyze:
            self._print("Checking for existing analysis...")
            task_id = self.search_existing_analysis(file_hash)
            if task_id:
                was_cached = True

        # If no existing analysis found or forced reanalysis, submit new task
        if task_id is None:
            if force_reanalyze:
                self._print("Force reanalysis requested")
            else:
                self._print("No existing analysis found or reports unavailable")

            self._print("Submitting file for new analysis...")
            task_id = self.submit_file(file_path, options, machine)
            if not task_id:
                return {"error": True, "message": "Failed to submit file"}
            
            # Wait for completion
            if not self.wait_for_completion(task_id):
                # Check if task is still running vs actually failed
                current_status = self.check_status(task_id)
                self._print(f"Analysis timeout reached. Current status: {current_status}")

                if current_status in ['pending', 'running', 'processing']:
                    # Task is still active, return as "still processing"
                    return {
                        "error": True,
                        "message": "Analysis is still being processed on CAPE server",
                        "task_id": task_id,
                        "file_hash": file_hash,
                        "was_cached": was_cached,
                        "still_processing": True
                    }
                else:
                    # Task actually failed or unknown status
                    return {"error": True, "message": "Analysis did not complete in time", "task_id": task_id}

        # Get report
        self._print("Retrieving analysis report...")
        report = self.get_report(task_id, 'json')

        if report is None:
            # Check if it's because task is still being analyzed
            current_status = self.check_status(task_id)
            if current_status in ['pending', 'running', 'processing']:
                return {
                    "error": True,
                    "message": "Analysis is still being processed on CAPE server",
                    "task_id": task_id,
                    "file_hash": file_hash,
                    "was_cached": was_cached,
                    "still_processing": True
                }
            else:
                return {"error": True, "message": "Failed to retrieve report", "task_id": task_id}
        
        self._print("Analysis completed successfully!", force=True)
        return {
            "error": False,
            "task_id": task_id,
            "file_hash": file_hash,
            "report": report,
            "message": "Analysis completed successfully",
            "was_cached": was_cached
        }
