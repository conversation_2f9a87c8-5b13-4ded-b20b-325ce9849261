{% extends "base.html" %}

{% block title %}Analysis Status - CAPE Web Service{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h3 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>Analysis Status
                </h3>
            </div>
            <div class="card-body">
                <!-- Analysis Info -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="file-info">
                            <h5><i class="fas fa-file me-2"></i>File Information</h5>
                            <p><strong>Filename:</strong> {{ analysis.filename }}</p>
                            <p><strong>Analysis ID:</strong> <code>{{ analysis.id }}</code></p>
                            {% if analysis.cape_task_id %}
                            <p><strong>CAPE Task ID:</strong> <code>{{ analysis.cape_task_id }}</code></p>
                            {% endif %}
                            {% if analysis.file_hash %}
                            <p><strong>File Hash:</strong> <code>{{ analysis.file_hash }}</code></p>
                            {% endif %}
                            {% if analysis.file_info %}
                            <p><strong>File Size:</strong> {{ "%.2f"|format(analysis.file_info.size / 1024 / 1024) }} MB</p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="file-info">
                            <h5><i class="fas fa-clock me-2"></i>Timeline</h5>
                            <p><strong>Created:</strong> {{ analysis.created_at }}</p>
                            {% if analysis.started_at %}
                            <p><strong>Started:</strong> {{ analysis.started_at }}</p>
                            {% endif %}
                            {% if analysis.completed_at %}
                            <p><strong>Completed:</strong> {{ analysis.completed_at }}</p>
                            {% endif %}
                            {% if analysis.was_cached %}
                            <p><span class="badge bg-success"><i class="fas fa-database me-1"></i>Cached Result</span></p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Status -->
                <div class="text-center mb-4">
                    <h4>Current Status</h4>
                    {% if analysis.status == 'pending' %}
                    <div class="alert alert-warning">
                        <i class="fas fa-hourglass-start fa-2x status-pending mb-2"></i>
                        <h5>Pending</h5>
                        <p>Analysis is queued and waiting to start...</p>
                    </div>
                    {% elif analysis.status == 'running' %}
                    <div class="alert alert-info">
                        <i class="fas fa-spinner fa-spin fa-2x status-running mb-2"></i>
                        <h5>Running</h5>
                        <p>Analysis is currently in progress...</p>
                    </div>
                    {% elif analysis.status == 'processing' %}
                    <div class="alert alert-primary">
                        <i class="fas fa-cogs fa-spin fa-2x text-primary mb-2"></i>
                        <h5>Processing</h5>
                        <p>Analysis completed, processing results and generating report...</p>
                    </div>
                    {% elif analysis.status == 'completed' %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle fa-2x status-completed mb-2"></i>
                        <h5>Completed</h5>
                        <p>Analysis completed successfully!</p>
                    </div>
                    {% elif analysis.status == 'reported' %}
                    <div class="alert alert-success">
                        <i class="fas fa-file-alt fa-2x text-success mb-2"></i>
                        <h5>Reported</h5>
                        <p>Analysis completed and report is ready!</p>
                    </div>
                    {% elif analysis.status == 'failed' %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle fa-2x status-failed mb-2"></i>
                        <h5>Failed</h5>
                        <p>Analysis failed. Please check the error details below.</p>
                    </div>
                    {% endif %}
                </div>

                <!-- Analysis Options -->
                <div class="mb-4">
                    <h5><i class="fas fa-cogs me-2"></i>Analysis Configuration</h5>
                    <div class="file-info">
                        <p><strong>Mode:</strong> 
                            <span class="badge bg-primary">{{ analysis.analysis_mode.title() }}</span>
                        </p>
                        {% if analysis.force_reanalyze %}
                        <p><span class="badge bg-warning"><i class="fas fa-redo me-1"></i>Force Re-analysis</span></p>
                        {% endif %}
                        {% if analysis.options %}
                        <p><strong>Options:</strong></p>
                        <div class="row">
                            {% for key, value in analysis.options.items() %}
                            <div class="col-md-6">
                                <span class="badge bg-secondary me-1">{{ key }}</span>
                                <span class="text-muted">{{ value }}</span>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Error Details -->
                {% if analysis.status == 'failed' and analysis.result and analysis.result.error %}
                <div class="mb-4">
                    <h5><i class="fas fa-exclamation-circle me-2"></i>Error Details</h5>
                    <div class="alert alert-danger">
                        {{ analysis.result.message }}
                    </div>
                </div>
                {% endif %}

                <!-- Actions -->
                <div class="text-center">
                    {% if analysis.status in ['completed', 'reported'] %}
                    <a href="{{ url_for('view_report', analysis_id=analysis.id) }}" class="btn btn-success btn-lg me-2">
                        <i class="fas fa-file-alt me-2"></i>View Report
                    </a>
                    {% endif %}
                    
                    <button onclick="location.reload()" class="btn btn-outline-primary">
                        <i class="fas fa-sync-alt me-2"></i>Refresh Status
                    </button>
                    
                    <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-upload me-2"></i>Upload Another File
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-refresh page every 10 seconds if analysis is still running
{% if analysis.status in ['pending', 'running', 'processing'] %}
setTimeout(function() {
    location.reload();
}, 10000);
{% endif %}
</script>
{% endblock %}
