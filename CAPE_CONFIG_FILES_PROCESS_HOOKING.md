# CAPEv2 Configuration Files liên quan đến Process Hooking

## Tổng quan các file cấu hình quan trọng

### 1. **conf/processing.conf** - C<PERSON>u hình xử lý behavior analysis

```ini
[behavior]
enabled = yes
# Toggle specific modules within the BehaviorAnalysis class
anomaly = yes
processtree = yes
summary = yes
enhanced = yes
encryptedbuffers = yes
# Should the server use a compressed version of behavioural logs?
loop_detection = no
# The number of calls per process to process. 0 switches the limit off.
analysis_call_limit = 0
# Use ram to boost processing speed
ram_boost = no

[CAPE]
enabled = yes
# Ex targetinfo standalone module
targetinfo = yes
# Ex dropped standalone module
dropped = yes
# Ex procdump standalone module
procdump = yes
# Amount of text to carve from plaintext files (bytes)
buffer = 8192
# Process files not bigger than value below in Mb
max_file_size = 90
# Scan for UserDB.TXT signature matches
userdb_signature = no
```

### 2. **conf/auxiliary.conf** - Cấu hình auxiliary modules

```ini
[auxiliary_modules]
amsi = no
browser = yes
curtain = no
digisig = yes
disguise = yes
evtx = no
human_windows = yes
procmon = no
recentfiles = no
screenshots_windows = yes
sysmon_windows = no
tlsdump = yes
usage = no
file_pickup = no
permissions = no
filecollector = yes
wmi_etw = no
dns_etw = no

[procmon]
enabled = yes

[sysmon_windows]
enabled = yes

[evtx]
enabled = yes
```

### 3. **conf/cuckoo.conf** - Cấu hình chính

```ini
[cuckoo]
machinery = kvm
tmppath = /tmp
# Enable memory dump cho behavior analysis
memory_dump = yes
# Timeout settings
analysis_timeout = 600
critical_timeout = 300

[processing]
# Timeout cho processing
analysis_timeout = 600
critical_timeout = 300
# Enable network processing
sort_pcap = yes
resolve_dns = yes

[resultserver]
ip = 0.0.0.0
port = 2042
force_port = no
upload_max_size = 256
```

### 4. **conf/memory.conf** - Cấu hình memory analysis

```ini
[basic]
enabled = yes
guest_profile =
delete_memdump = no

[malfind]
enabled = yes
filter = yes

[apihooks]
enabled = yes
filter = yes

[pslist]
enabled = yes
filter = no

[psscan]
enabled = yes
filter = off

[callbacks]
enabled = no
filter = off
```

## Files KHÔNG tồn tại nhưng có thể tạo

### 5. **conf/analyzer.conf** (Cần tạo)

```ini
[analyzer]
# Process injection settings
inject_processes = yes
protected_processes = no
injection_timeout = 30
retry_injection = yes
system_process_injection = yes

[injection]
# Enhanced injection for system processes
dllhost_injection = yes
svchost_injection = yes
explorer_injection = yes
retry_count = 3
retry_delay = 0.1

[monitoring]
# Monitor DLL settings
monitor_dll = capemon.dll
monitor_dll_64 = capemon_x64.dll
injection_method = standard
```

### 6. **conf/system_processes.conf** (Cần tạo)

```ini
[system_processes]
# Enable enhanced system process injection
enabled = yes

# Processes to attempt injection
target_processes = dllhost.exe,svchost.exe,explorer.exe,services.exe

# Injection retry attempts
retry_attempts = 3
retry_delay = 0.1
verbose_logging = yes

[dllhost]
priority = high
method = standard
max_instances = 10

[svchost]
priority = high  
method = standard
max_instances = 20

[explorer]
priority = medium
method = standard
max_instances = 1
```

## Files trong analyzer directory

### 7. **analyzer/windows/analysis.conf** (Tạo runtime)

File này được tạo tự động cho mỗi analysis, chứa:

```ini
[cuckoo]
debug = off
analysis_timeout = 120
critical_timeout = 600
delete_original = off
machine_manager = kvm
use_sniffer = no
tcpdump = /usr/sbin/tcpdump
interface = vboxnet0

[analysis]
file_name = sample.exe
file_type = PE32
package = exe
options = 
timeout = 120
```

### 8. **analyzer/windows/lib/core/config.py**

Class để đọc configuration files:

<augment_code_snippet path="analyzer/windows/lib/core/config.py" mode="EXCERPT">
````python
class Config:
    def __init__(self, cfg):
        """@param cfg: configuration file."""
        config = configparser.ConfigParser(allow_no_value=True, interpolation=None)
        config.read(cfg)

        for section in config.sections():
            for name, raw_value in config.items(section):
                if name == "file_name":
                    value = config.get(section, name)
                    if len(value) >= 2 and value[0] == value[-1] == "'":
                        value = value[1:-1]
                else:
                    try:
                        value = config.getboolean(section, name)
                    except ValueError:
                        try:
                            value = config.getint(section, name)
                        except ValueError:
                            value = config.get(section, name)
                setattr(self, name, value)
````
</augment_code_snippet>

## Cách sử dụng configuration

### Trong analyzer.py:

<augment_code_snippet path="analyzer/windows/analyzer.py" mode="EXCERPT">
````python
# Parse the analysis configuration file generated by the agent.
self.config = Config(cfg="analysis.conf")
self.options = self.config.get_options()
````
</augment_code_snippet>

### Trong process injection:

<augment_code_snippet path="analyzer/windows/analyzer.py" mode="EXCERPT">
````python
proc.inject(interest=filepath, nosleepskip=True)
self.analyzer.LASTINJECT_TIME = timeit.default_timer()
log.info("Injected into process with pid %s and name %s", proc.pid, filename)
````
</augment_code_snippet>

## Thứ tự ưu tiên cấu hình

1. **analysis.conf** (runtime) - Cao nhất
2. **conf/cuckoo.conf** - Cấu hình chính
3. **conf/processing.conf** - Behavior analysis
4. **conf/auxiliary.conf** - Auxiliary modules
5. **conf/analyzer.conf** - Analyzer specific (nếu có)
6. **Hard-coded defaults** trong source code

## Tạo cấu hình tùy chỉnh

```bash
# Tạo analyzer.conf
cat > conf/analyzer.conf << 'EOF'
[analyzer]
inject_processes = yes
system_process_injection = yes
retry_injection = yes
injection_timeout = 30

[injection]
dllhost_injection = yes
svchost_injection = yes
explorer_injection = yes
retry_count = 5
retry_delay = 0.2
EOF

# Tạo system_processes.conf
cat > conf/system_processes.conf << 'EOF'
[system_processes]
enabled = yes
target_processes = dllhost.exe,svchost.exe,explorer.exe
retry_attempts = 3
verbose_logging = yes
EOF
```

## Kiểm tra cấu hình

```python
from lib.core.config import Config

# Đọc cấu hình
config = Config("analysis.conf")
print(f"Inject processes: {getattr(config, 'inject_processes', 'not set')}")
print(f"System injection: {getattr(config, 'system_process_injection', 'not set')}")
```
