#!/bin/bash

# Script để tùy chỉnh cape2.sh với đường dẫn cài đặt khác
# Sử dụng: ./customize_cape_installer.sh /path/to/new/location

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

function print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

function print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

function print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

if [ $# -ne 1 ]; then
    print_error "Sử dụng: $0 <đường_dẫn_cài_đặt>"
    echo "Ví dụ:"
    echo "  $0 /home/<USER>/CAPEv2"
    echo "  $0 /data/CAPEv2"
    echo "  $0 /srv/CAPEv2"
    echo "  $0 /mnt/CAPEv2        # Khuyến nghị cho storage lớn"
    echo "  $0 /mnt/storage/CAPEv2"
    exit 1
fi

NEW_CAPE_PATH="$1"
OLD_CAPE_PATH="/opt/CAPEv2"
INSTALLER_FILE="installer/cape2.sh"
CUSTOM_INSTALLER="installer/cape2_custom.sh"

print_info "=== Tùy chỉnh cape2.sh cho đường dẫn: $NEW_CAPE_PATH ==="

# Kiểm tra file installer
if [ ! -f "$INSTALLER_FILE" ]; then
    print_error "Không tìm thấy file $INSTALLER_FILE"
    exit 1
fi

# Tạo bản copy
print_info "Tạo bản custom: $CUSTOM_INSTALLER"
cp "$INSTALLER_FILE" "$CUSTOM_INSTALLER"

# Thay thế tất cả /opt/CAPEv2 bằng đường dẫn mới
print_info "Thay thế đường dẫn trong installer..."
sed -i "s|$OLD_CAPE_PATH|$NEW_CAPE_PATH|g" "$CUSTOM_INSTALLER"

# Thêm biến CAPE_PATH vào đầu file
print_info "Thêm biến CAPE_PATH..."
sed -i "/^# Static values/a\\
# Custom CAPE installation path\\
CAPE_PATH=\"$NEW_CAPE_PATH\"" "$CUSTOM_INSTALLER"

# Cập nhật function usage
sed -i "s|Example: \$0 all ************|Example: \$0 all ************\\
    Custom path: $NEW_CAPE_PATH|" "$CUSTOM_INSTALLER"

# Thêm function tạo thư mục
sed -i "/^function usage()/i\\
function create_cape_directory() {\\
    echo \"[+] Creating CAPE directory: \$CAPE_PATH\"\\
    mkdir -p \"\$CAPE_PATH\"\\
    if [ ! -d \"\$CAPE_PATH\" ]; then\\
        echo \"[-] Failed to create directory: \$CAPE_PATH\"\\
        exit 1\\
    fi\\
}\\
" "$CUSTOM_INSTALLER"

# Thêm gọi function tạo thư mục trong install_sandbox
sed -i "/function install_sandbox()/a\\
    create_cape_directory" "$CUSTOM_INSTALLER"

# Cập nhật ownership paths
sed -i "s|chown \${USER}:\${USER} -R \"/opt/CAPEv2/\"|chown \${USER}:\${USER} -R \"\$CAPE_PATH/\"|g" "$CUSTOM_INSTALLER"

print_info "Tạo script wrapper..."
cat > "install_cape_custom.sh" << EOF
#!/bin/bash

# Wrapper script để cài đặt CAPEv2 tại vị trí tùy chỉnh
# Đường dẫn: $NEW_CAPE_PATH

set -e

CAPE_PATH="$NEW_CAPE_PATH"
USER_NAME=\${SUDO_USER:-cape}

echo "=== Cài đặt CAPEv2 tại: \$CAPE_PATH ==="

# Kiểm tra quyền root
if [ "\$EUID" -ne 0 ]; then
   echo 'Script này cần chạy với quyền root (sudo)'
   exit 1
fi

# Tạo user cape nếu chưa có
if ! id "\$USER_NAME" &>/dev/null; then
    echo "[+] Tạo user: \$USER_NAME"
    useradd -m -s /bin/bash "\$USER_NAME"
fi

# Tạo thư mục CAPE
echo "[+] Tạo thư mục: \$CAPE_PATH"
mkdir -p "\$CAPE_PATH"
chown "\$USER_NAME:\$USER_NAME" "\$CAPE_PATH"

# Clone CAPEv2 nếu chưa có
if [ ! -d "\$CAPE_PATH/.git" ]; then
    echo "[+] Clone CAPEv2..."
    cd "\$(dirname "\$CAPE_PATH")"
    sudo -u "\$USER_NAME" git clone https://github.com/kevoreilly/CAPEv2/ "\$(basename "\$CAPE_PATH")"
fi

# Chạy installer custom
echo "[+] Chạy installer..."
cd "\$CAPE_PATH"
bash installer/cape2_custom.sh "\$@"

echo "=== Cài đặt hoàn tất! ==="
echo "CAPE location: \$CAPE_PATH"
echo "User: \$USER_NAME"
EOF

chmod +x "install_cape_custom.sh"

print_info "=== Hoàn thành! ==="
print_info "Đã tạo các file:"
print_info "  - $CUSTOM_INSTALLER (installer đã tùy chỉnh)"
print_info "  - install_cape_custom.sh (wrapper script)"
print_info ""
print_info "Cách sử dụng:"
print_info "  sudo ./install_cape_custom.sh all ************"
print_info ""
print_info "Hoặc chạy trực tiếp:"
print_info "  sudo bash $CUSTOM_INSTALLER all ************"
