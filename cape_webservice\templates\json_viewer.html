{% extends "base.html" %}

{% block title %}JSON Report Viewer - CAPE Web Service{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h3 class="card-title mb-0">
                    <i class="fas fa-file-code me-2"></i>JSON Report Viewer
                </h3>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <p class="text-muted">
                        Upload a CAPE analysis report JSON file (e.g., <code>12345_report.json</code>) to view detailed analysis results.
                    </p>
                </div>

                <form method="POST" action="{{ url_for('view_json') }}" enctype="multipart/form-data">
                    <!-- File Upload -->
                    <div class="mb-4">
                        <label for="json_file" class="form-label">
                            <i class="fas fa-file-upload me-1"></i>Select JSON Report File
                        </label>
                        <input type="file" class="form-control" id="json_file" name="json_file" 
                               accept=".json" required>
                        <div class="form-text">
                            Supported format: JSON files (.json)
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-info btn-lg">
                            <i class="fas fa-eye me-2"></i>View Report
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Info Cards -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-chart-pie fa-2x text-primary mb-2"></i>
                        <h6>Comprehensive Analysis</h6>
                        <p class="card-text small text-muted">View all analysis sections including behavior, network, signatures</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-search fa-2x text-success mb-2"></i>
                        <h6>Interactive Interface</h6>
                        <p class="card-text small text-muted">Search, filter, and explore analysis data easily</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-download fa-2x text-info mb-2"></i>
                        <h6>Export Options</h6>
                        <p class="card-text small text-muted">Download and copy analysis data</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sample JSON Structure -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Expected JSON Structure
                </h5>
            </div>
            <div class="card-body">
                <p>The JSON file should contain a CAPE analysis report with the following main sections:</p>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i><code>info</code> - Analysis metadata</li>
                            <li><i class="fas fa-check text-success me-2"></i><code>target</code> - Target file information</li>
                            <li><i class="fas fa-check text-success me-2"></i><code>signatures</code> - Detection signatures</li>
                            <li><i class="fas fa-check text-success me-2"></i><code>behavior</code> - Behavioral analysis</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i><code>network</code> - Network activity</li>
                            <li><i class="fas fa-check text-success me-2"></i><code>dropped</code> - Dropped files</li>
                            <li><i class="fas fa-check text-success me-2"></i><code>CAPE</code> - Extracted payloads</li>
                            <li><i class="fas fa-check text-success me-2"></i><code>static</code> - Static analysis</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('json_file');
    const form = document.querySelector('form');
    
    // File validation
    fileInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            if (!file.name.toLowerCase().endsWith('.json')) {
                alert('Please select a JSON file (.json)');
                this.value = '';
                return;
            }
            
            // Check file size (max 50MB)
            if (file.size > 50 * 1024 * 1024) {
                alert('File is too large. Maximum size is 50MB.');
                this.value = '';
                return;
            }
        }
    });
    
    // Form submission with loading indicator
    form.addEventListener('submit', function() {
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
        
        // Re-enable button after 10 seconds (in case of error)
        setTimeout(function() {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }, 10000);
    });
});
</script>
{% endblock %}
