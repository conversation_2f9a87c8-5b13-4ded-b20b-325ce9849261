# Hướng dẫn cấu hình mạng CAPEv2 cho card mạng enp4s0 (IP: ************)

## Tổng quan
Tài liệu này hướng dẫn cấu hình các file trong thư mục `conf/` để setup card mạng `enp4s0` với IP `************` cho các máy VM phân tích mã độc trong CAPEv2.

## 1. C<PERSON><PERSON> hì<PERSON> Routing (routing.conf)

### File: `conf/routing.conf`
```ini
[routing]
# Enable pcap generation for non live connections
enable_pcap = yes

# Default network routing mode - set to "internet" để cho phép VM truy cập internet
route = internet

# Network interface cho "dirty line" - sử dụng card mạng enp4s0
internet = enp4s0

# Enable NAT masquerading
nat = yes

# Disable local routing bypass
no_local_routing = no

# Routing table name/id for dirty line interface
rt_table = main

# Reject segments - c<PERSON> thể thêm các subnet nội bộ cần bảo vệ
# Ví dụ: reject_segments = ***********/24,10.0.0.0/8
reject_segments = none

# Reject host ports - có thể block các port nhạy cảm
# Ví dụ: reject_hostports = 22,3389,5900
reject_hostports = none

# Auto routing table initialization
auto_rt = yes

# Drop route for complete isolation (optional)
drop = no

# Verify interface is up
verify_interface = yes

# Verify routing table exists
verify_rt_table = yes

[inetsim]
enabled = no

[tor]
enabled = no

[vpn]
enabled = no

[socks5]
enabled = no
```

## 2. Cấu hình Result Server (cuckoo.conf)

### File: `conf/cuckoo.conf`
```ini
[resultserver]
# IP address của host - sử dụng IP của card enp4s0
ip = ************

# Port cho result server
port = 2042

# Force port
force_port = no
```

## 3. Cấu hình Network Sniffer (auxiliary.conf)

### File: `conf/auxiliary.conf`
```ini
[sniffer]
# Enable tcpdump
enabled = yes

# Disable remote tcpdump
remote = no

# Path to tcpdump
tcpdump = /usr/bin/tcpdump

# Network interface for packet capture - sử dụng enp4s0
interface = enp4s0

# Berkeley packet filter
bpf = not arp and not icmp
```

## 4. Cấu hình Virtual Machines

### 4.1. VirtualBox (virtualbox.conf)
```ini
[virtualbox]
# Interface cho VirtualBox host-only network
interface = vboxnet0

[cuckoo1]
# IP của VM trong subnet VirtualBox
ip = **************

# Result server IP như VM nhìn thấy (IP của host)
resultserver_ip = ************

# Interface override cho VM này (nếu cần)
interface = vboxnet0
```

### 4.2. KVM/QEMU (kvm.conf)
```ini
[kvm]
# Interface cho KVM bridge network
interface = virbr0

[cuckoo1]
# IP của VM trong KVM network
ip = ***************

# Result server IP như VM nhìn thấy
resultserver_ip = ************

# Interface override cho VM này
interface = virbr0
```

## 5. Cấu hình Bridge Network (nếu sử dụng)

### Tạo bridge network cho enp4s0:
```bash
# Tạo bridge br0
sudo ip link add name br0 type bridge

# Gán enp4s0 vào bridge
sudo ip link set enp4s0 master br0

# Set IP cho bridge
sudo ip addr add ************/24 dev br0

# Enable bridge và interface
sudo ip link set br0 up
sudo ip link set enp4s0 up
```

### Cấu hình persistent (netplan - Ubuntu):
```yaml
# File: /etc/netplan/01-network-manager-all.yaml
network:
  version: 2
  ethernets:
    enp4s0:
      dhcp4: no
  bridges:
    br0:
      interfaces: [enp4s0]
      addresses: [************/24]
      gateway4: ***********
      nameservers:
        addresses: [*******, *******]
```

## 6. Cấu hình iptables cho NAT

### Script iptables:
```bash
#!/bin/bash
# Enable IP forwarding
echo 1 > /proc/sys/net/ipv4/ip_forward

# Clear existing rules
iptables -F
iptables -t nat -F

# Allow forwarding
iptables -P FORWARD ACCEPT

# NAT rule cho enp4s0
iptables -t nat -A POSTROUTING -o enp4s0 -j MASQUERADE

# Allow established connections
iptables -A FORWARD -m state --state RELATED,ESTABLISHED -j ACCEPT

# Allow VM networks to access internet
iptables -A FORWARD -s ************/24 -j ACCEPT  # VirtualBox
iptables -A FORWARD -s *************/24 -j ACCEPT # KVM
```

## 7. Kiểm tra cấu hình

### Kiểm tra interface:
```bash
ip addr show enp4s0
ip route show
```

### Kiểm tra connectivity từ VM:
```bash
# Từ trong VM, ping result server
ping ************

# Kiểm tra port result server
telnet ************ 2042
```

### Kiểm tra packet capture:
```bash
# Test tcpdump trên enp4s0
sudo tcpdump -i enp4s0 -n
```

## 8. Lưu ý quan trọng

1. **Firewall**: Đảm bảo firewall cho phép traffic qua port 2042 và các port cần thiết
2. **SELinux/AppArmor**: Có thể cần disable hoặc cấu hình để cho phép packet capture
3. **Permissions**: User chạy CAPE cần quyền sudo để chạy tcpdump và rooter.py
4. **VM Network**: Đảm bảo VM có thể reach được IP ************
5. **DNS**: Cấu hình DNS server phù hợp cho VM

## 9. Troubleshooting

### Lỗi thường gặp:
- **VM không kết nối được result server**: Kiểm tra IP và routing
- **Không capture được packet**: Kiểm tra interface và permissions
- **NAT không hoạt động**: Kiểm tra iptables rules và IP forwarding

### Log files để kiểm tra:
- `/var/log/cuckoo/cuckoo.log`
- `/var/log/cuckoo/rooter.log`
- `dmesg` cho network interface issues
