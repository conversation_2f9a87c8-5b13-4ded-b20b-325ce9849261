#!/usr/bin/env python3
"""
Check CAPE server connectivity and status
"""

import requests
import json
from config import Config

def check_cape_server():
    """Check if CAPE server is accessible"""
    print("🔍 Checking CAPE server connectivity...")
    print(f"📡 CAPE URL: {Config.CAPE_URL}")
    
    try:
        # Test basic connectivity
        response = requests.get(f"{Config.CAPE_URL}/", timeout=10)
        if response.status_code == 200:
            print("✅ CAPE server is accessible")
        else:
            print(f"⚠️  CAPE server returned status code: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to CAPE server")
        print("   Make sure CAPEv2 is running and accessible")
        return False
    except requests.exceptions.Timeout:
        print("❌ Connection to CAPE server timed out")
        return False
    except Exception as e:
        print(f"❌ Error connecting to CAPE server: {e}")
        return False
    
    # Test API endpoints
    print("\n🧪 Testing CAPE API endpoints...")
    
    # Test tasks endpoint
    try:
        response = requests.get(f"{Config.CAPE_URL}/apiv2/tasks/list/", timeout=10)
        if response.status_code == 200:
            print("✅ Tasks API endpoint is working")
            data = response.json()
            if data.get('data'):
                print(f"   Found {len(data['data'])} existing tasks")
        else:
            print(f"⚠️  Tasks API returned status code: {response.status_code}")
    except Exception as e:
        print(f"❌ Error testing tasks API: {e}")
    
    # Test file upload endpoint (without actually uploading)
    try:
        response = requests.options(f"{Config.CAPE_URL}/apiv2/tasks/create/file/", timeout=10)
        if response.status_code in [200, 405]:  # 405 is expected for OPTIONS
            print("✅ File upload API endpoint is accessible")
        else:
            print(f"⚠️  File upload API returned status code: {response.status_code}")
    except Exception as e:
        print(f"❌ Error testing file upload API: {e}")
    
    return True

def show_config():
    """Show current configuration"""
    print("\n⚙️  Current Configuration:")
    print(f"   CAPE URL: {Config.CAPE_URL}")
    print(f"   Analysis timeout: {Config.CAPE_TIMEOUT}s")
    print(f"   Max wait time: {Config.CAPE_MAX_WAIT}s")
    print(f"   Upload folder: {Config.UPLOAD_FOLDER}")
    print(f"   Max file size: {Config.MAX_CONTENT_LENGTH / 1024 / 1024:.0f}MB")
    
    print("\n📋 Default Analysis Options:")
    for key, value in Config.DEFAULT_OPTIONS.items():
        print(f"   {key} = {value}")
    
    print(f"\n📁 Allowed file extensions: {len(Config.ALLOWED_EXTENSIONS)} types")
    extensions = sorted(Config.ALLOWED_EXTENSIONS)
    for i in range(0, len(extensions), 10):
        print(f"   {', '.join(extensions[i:i+10])}")

def main():
    """Main function"""
    print("🛡️  CAPE Web Service - Server Check")
    print("=" * 50)
    
    show_config()
    print("\n" + "=" * 50)
    
    if check_cape_server():
        print("\n✅ CAPE server check completed successfully!")
        print("🚀 You can now start the web service with: python run.py")
    else:
        print("\n❌ CAPE server check failed!")
        print("🔧 Please check your CAPE server configuration and try again")

if __name__ == '__main__':
    main()
