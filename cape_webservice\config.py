"""
Configuration for CAPE Web Service
"""

import os

class Config:
    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'cape-webservice-secret-key-change-in-production'
    MAX_CONTENT_LENGTH = 500 * 1024 * 1024  # 500MB max file size
    
    # CAPE server settings
    CAPE_URL = os.environ.get('CAPE_URL') or 'http://localhost:8000'
    CAPE_TIMEOUT = int(os.environ.get('CAPE_TIMEOUT', '300'))  # Analysis timeout in seconds
    CAPE_MAX_WAIT = int(os.environ.get('CAPE_MAX_WAIT', '1800'))  # Max wait time in seconds
    
    # Upload settings
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    ALLOWED_EXTENSIONS = {
        'exe', 'dll', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
        'zip', 'rar', '7z', 'tar', 'gz', 'apk', 'jar', 'bat', 'cmd', 'ps1',
        'vbs', 'js', 'html', 'htm', 'php', 'py', 'pl', 'rb', 'sh'
    }
    
    # Default analysis options (from cape_simple_service.py)
    DEFAULT_OPTIONS = {
        'procmemdump': '1',
        'import_reconstruction': '1', 
        'unpacker': '2',
        'norefer': '1',
        'no-iat': '1'
    }
    
    # Available analysis options (from web/submission/views.py)
    AVAILABLE_OPTIONS = {
        'procmemdump': {'name': 'Process Memory Dump', 'type': 'checkbox', 'default': True},
        'import_reconstruction': {'name': 'Import Reconstruction', 'type': 'checkbox', 'default': True},
        'unpacker': {'name': 'Unpacker', 'type': 'select', 'options': {'0': 'Disabled', '1': 'Enabled', '2': 'Force'}, 'default': '2'},
        'norefer': {'name': 'No Referrer', 'type': 'checkbox', 'default': True},
        'no-iat': {'name': 'No IAT', 'type': 'checkbox', 'default': True},
        'syscall': {'name': 'System Calls', 'type': 'checkbox', 'default': False},
        'kernel_analysis': {'name': 'Kernel Analysis', 'type': 'checkbox', 'default': False},
        'tor': {'name': 'Use Tor', 'type': 'checkbox', 'default': False},
        'free': {'name': 'Free Analysis', 'type': 'checkbox', 'default': False},
        'nohuman': {'name': 'No Human Interaction', 'type': 'checkbox', 'default': False},
        'mitmdump': {'name': 'MITM Dump', 'type': 'checkbox', 'default': False},
        'unpack': {'name': 'Unpack', 'type': 'checkbox', 'default': False}
    }

# Create upload folder if it doesn't exist
os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
