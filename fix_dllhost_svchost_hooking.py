#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> khắc phụ<PERSON> cụ thể vấn đề không hook được dllhost.exe và svchost.exe trong CAPEv2
Tác giả: CAPEv2 Enhancement Script
Mục đích: Sửa đổi analyzer.py để cải thiện khả năng hook system processes
"""

import os
import sys
import shutil
import re
from pathlib import Path

def find_cape_path():
    """Tự động tìm đường dẫn CAPEv2"""
    possible_paths = [
        'f:/CAPEv2',  # Current Windows path
        'C:/CAPEv2',
        '/opt/CAPEv2',
        '/mnt/CAPEv2',
        '/data/CAPEv2',
        '/srv/CAPEv2'
    ]
    
    # Thêm các đường dẫn động
    import glob
    possible_paths.extend(glob.glob('/mnt/data/*/CAPEv2'))
    
    for path in possible_paths:
        if os.path.exists(path) and os.path.exists(os.path.join(path, 'cuckoo.py')):
            return path
    
    # <PERSON><PERSON><PERSON> kh<PERSON> tì<PERSON> thấy, sử dụng thư mục hi<PERSON><PERSON> tại
    current_dir = os.getcwd()
    if os.path.exists(os.path.join(current_dir, 'cuckoo.py')):
        return current_dir
    
    return None

def backup_analyzer(analyzer_path):
    """Tạo backup của analyzer.py"""
    backup_path = analyzer_path + '.backup_system_hook'
    if not os.path.exists(backup_path):
        shutil.copy2(analyzer_path, backup_path)
        print(f"✅ Backup created: {backup_path}")
        return True
    else:
        print(f"✅ Backup already exists: {backup_path}")
        return True

def patch_analyzer_for_system_processes(cape_path):
    """Patch analyzer.py để cải thiện system process hooking"""
    analyzer_path = os.path.join(cape_path, 'analyzer', 'windows', 'analyzer.py')
    
    if not os.path.exists(analyzer_path):
        print(f"❌ analyzer.py not found at: {analyzer_path}")
        return False
    
    print(f"🔧 Patching analyzer.py for system process hooking...")
    
    # Tạo backup
    if not backup_analyzer(analyzer_path):
        return False
    
    # Đọc nội dung file
    with open(analyzer_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Kiểm tra xem đã patch chưa
    if 'SYSTEM_PROCESS_HOOK_ENHANCEMENT' in content:
        print("✅ System process hook enhancement already applied")
        return True
    
    # Patch 1: Thêm class SystemProcessHooker
    system_hooker_class = '''
# SYSTEM_PROCESS_HOOK_ENHANCEMENT - Enhanced system process hooking
class SystemProcessHooker:
    """Enhanced hooking for system processes like dllhost.exe, svchost.exe"""
    
    SYSTEM_PROCESSES = {
        "dllhost.exe": {
            "description": "COM+ Application Host",
            "hook_priority": "high",
            "retry_count": 5,
            "retry_delay": 0.2
        },
        "svchost.exe": {
            "description": "Service Host Process", 
            "hook_priority": "high",
            "retry_count": 3,
            "retry_delay": 0.1
        },
        "explorer.exe": {
            "description": "Windows Shell",
            "hook_priority": "medium",
            "retry_count": 2,
            "retry_delay": 0.1
        },
        "services.exe": {
            "description": "Service Control Manager",
            "hook_priority": "medium", 
            "retry_count": 2,
            "retry_delay": 0.1
        }
    }
    
    @classmethod
    def is_system_process(cls, process_name):
        """Check if process is a target system process"""
        return process_name.lower() in cls.SYSTEM_PROCESSES
    
    @classmethod
    def get_process_config(cls, process_name):
        """Get configuration for system process"""
        return cls.SYSTEM_PROCESSES.get(process_name.lower(), {})
    
    @classmethod
    def enhanced_inject(cls, proc, filepath, process_name):
        """Enhanced injection method for system processes"""
        config = cls.get_process_config(process_name)
        retry_count = config.get("retry_count", 3)
        retry_delay = config.get("retry_delay", 0.1)
        
        log.info("🎯 Attempting enhanced injection into %s (%s)", 
                process_name, config.get("description", "System Process"))
        
        for attempt in range(retry_count):
            try:
                # Attempt injection
                success = proc.inject(interest=filepath, nosleepskip=True)
                
                if success:
                    log.info("✅ Successfully injected into %s (attempt %d/%d)", 
                            process_name, attempt + 1, retry_count)
                    return True
                else:
                    log.warning("⚠️  Injection attempt %d/%d failed for %s", 
                               attempt + 1, retry_count, process_name)
                    
            except Exception as e:
                log.warning("❌ Injection attempt %d/%d failed for %s: %s", 
                           attempt + 1, retry_count, process_name, str(e))
            
            # Wait before retry (except last attempt)
            if attempt < retry_count - 1:
                import time
                time.sleep(retry_delay)
        
        log.error("❌ All injection attempts failed for %s", process_name)
        return False

'''
    
    # Tìm vị trí để chèn class (trước class Analyzer)
    analyzer_class_pos = content.find('class Analyzer')
    if analyzer_class_pos == -1:
        print("❌ Could not find 'class Analyzer' in analyzer.py")
        return False
    
    # Chèn SystemProcessHooker class
    content = content[:analyzer_class_pos] + system_hooker_class + content[analyzer_class_pos:]
    
    # Patch 2: Sửa đổi _inject_process method
    inject_method_pattern = r'(proc\.inject\(interest=filepath, nosleepskip=True\))'
    
    enhanced_inject_call = '''# Enhanced injection for system processes
            filename = os.path.basename(filepath)
            if SystemProcessHooker.is_system_process(filename):
                SystemProcessHooker.enhanced_inject(proc, filepath, filename)
            else:
                proc.inject(interest=filepath, nosleepskip=True)'''
    
    # Thay thế lời gọi inject
    content = re.sub(inject_method_pattern, enhanced_inject_call, content)
    
    # Ghi file đã patch
    with open(analyzer_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ analyzer.py patched successfully for system process hooking")
    return True

def create_system_process_test():
    """Tạo script test cho system processes"""
    cape_path = find_cape_path()
    if not cape_path:
        return False
    
    test_script_path = os.path.join(cape_path, 'test_system_process_hook.py')
    
    test_content = '''#!/usr/bin/env python3
"""
Test script for system process hooking
"""
import os
import psutil

def test_system_processes():
    """Test if system processes are running and accessible"""
    target_processes = ["dllhost.exe", "svchost.exe", "explorer.exe", "services.exe"]
    
    print("=== System Process Hook Test ===")
    
    for proc_name in target_processes:
        print(f"\\nTesting {proc_name}:")
        
        pids = []
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if proc.info['name'].lower() == proc_name.lower():
                    pids.append(proc.info['pid'])
            except:
                continue
        
        if pids:
            print(f"  ✅ Found {len(pids)} instances: {pids[:5]}")  # Show max 5
        else:
            print(f"  ❌ No instances found")

if __name__ == "__main__":
    test_system_processes()
'''
    
    with open(test_script_path, 'w') as f:
        f.write(test_content)
    
    print(f"✅ Test script created: {test_script_path}")
    return True

def main():
    """Main function"""
    print("=" * 60)
    print("CAPEv2 System Process Hooking Fix")
    print("Khắc phục vấn đề hook dllhost.exe, svchost.exe, explorer.exe")
    print("=" * 60)
    
    # Tìm CAPEv2 path
    cape_path = find_cape_path()
    if not cape_path:
        print("❌ CAPEv2 installation not found!")
        print("Please run this script from CAPEv2 directory or specify path")
        return 1
    
    print(f"🔍 Found CAPEv2 at: {cape_path}")
    
    # Patch analyzer
    if not patch_analyzer_for_system_processes(cape_path):
        print("❌ Failed to patch analyzer.py")
        return 1
    
    # Tạo test script
    create_system_process_test()
    
    print("\n" + "=" * 60)
    print("✅ PATCH COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print("\nNext steps:")
    print("1. Copy setup_vm_for_hooking.bat to your Windows VM")
    print("2. Run it as Administrator in the VM")
    print("3. Reboot the VM")
    print("4. Test with: python3 test_system_process_hook.py")
    print("5. Run a malware analysis to verify improved hooking")
    print("\n⚠️  Important:")
    print("- This modifies analyzer.py (backup created)")
    print("- Only use in isolated analysis environment")
    print("- VM must have Windows Defender completely disabled")
    print("- CAPEv2 agent must run as SYSTEM")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
