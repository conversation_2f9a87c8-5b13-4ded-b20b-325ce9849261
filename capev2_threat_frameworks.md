# CAPEv2 Threat Intelligence & Classification Frameworks

## 🎯 **Tổng quan**
Ngoài **MITRE ATT&CK Tactics and Techniques**, CAPEv2 hỗ trợ nhiều framework và tiêu chuẩn đánh giá khác để phân tích và phân loại malware.

---

## 🔍 **1. MBCS - Malware Behavior Catalog**

### **<PERSON><PERSON> tả:**
- **MBCS (Malware Behavior Catalog)** - Framework phân loại behavior của malware
- Đư<PERSON>c phát triển bởi MITRE để bổ sung cho ATT&CK
- Focus vào micro-behaviors và objective-based behaviors

### **Trong CAPEv2:**
```python
# Ví dụ từ CAPE signatures
ttps = ["T1027", "T1140"]  # MITRE ATT&CK
mbcs = ["OB0002", "OB0006", "E1027"]  # MBCS Objectives & Behaviors
mbcs += ["OC0004", "C0025"]  # MBCS micro-behaviours
```

### **<PERSON><PERSON><PERSON> trúc MBCS:**
- **Objectives (OB)**: <PERSON><PERSON><PERSON> tiêu chính của malware
- **Behaviors (B/E)**: Hành vi cụ thể
- **Micro-behaviors (OC/C)**: Hành vi chi tiết ở mức API

---

## 🛡️ **2. FLARE CAPA Framework**

### **Mô tả:**
- **CAPA** - Capability Analysis framework của FireEye/Mandiant
- Phân tích capabilities của malware dựa trên static và dynamic analysis
- Mapping tới MITRE ATT&CK và MBCS

### **Trong CAPEv2:**
```ini
# conf/processing.conf
[flare_capa]
enabled = yes
# Analyze both static and dynamic capabilities
static_analysis = yes
dynamic_analysis = yes
```

### **Kết quả CAPA:**
- **ATT&CK Mapping**: Tự động map capabilities tới ATT&CK techniques
- **MBC Mapping**: Map tới Malware Behavior Catalog
- **Capability Rules**: Chi tiết về từng capability được detect

---

## 🔬 **3. YARA Rules Classification**

### **Mô tả:**
- **YARA** - Pattern matching engine cho malware identification
- Phân loại malware families và variants
- Custom rules cho specific threats

### **Trong CAPEv2:**
```ini
# conf/processing.conf
[yara]
enabled = yes
# Multiple rule sources
rules_path = data/yara/
community_rules = yes
custom_rules = yes
```

### **YARA Categories:**
- **Family Detection**: Xác định malware family
- **Packer Detection**: Detect packers/crypters
- **Technique Detection**: Specific techniques/behaviors
- **IOC Matching**: Indicators of Compromise

---

## 🌐 **4. Suricata IDS Rules**

### **Mô tả:**
- **Suricata** - Network-based threat detection
- ET (Emerging Threats) rules
- Custom network signatures

### **Trong CAPEv2:**
```ini
# conf/processing.conf
[suricata]
enabled = yes
# Rule categories
et_rules = yes
custom_rules = yes
malware_rules = yes
```

### **Suricata Classifications:**
- **Malware C&C**: Command & Control communication
- **Exploit Kits**: Web-based exploit detection
- **Data Exfiltration**: Data theft patterns
- **Botnet Traffic**: Botnet communication patterns

---

## 🦠 **5. Malware Family Classification**

### **Mô tả:**
- **Family-based Classification** - Phân loại theo malware families
- Dựa trên behavior patterns và signatures
- Integration với threat intelligence feeds

### **Trong CAPEv2:**
```python
# Family detection sources
families = [
    "Behavior",    # Behavioral signatures
    "YARA",        # YARA rules
    "CAPE",        # Config extraction
    "ClamAV",      # Antivirus detection
    "VirusTotal"   # VT consensus
]
```

### **Family Categories:**
- **RATs**: Remote Access Trojans
- **Banking Trojans**: Financial malware
- **Ransomware**: Encryption malware
- **Botnets**: Bot malware
- **APT Tools**: Advanced Persistent Threat tools

---

## 📊 **6. Threat Intelligence Integration**

### **A. ThreatFox (Abuse.ch)**
```ini
# conf/integrations.conf
[abusech]
threatfox = yes
malwarebazaar = yes
apikey = your_api_key
```

### **B. MISP Integration**
```ini
# conf/reporting.conf
[misp]
enabled = yes
apikey = your_misp_key
url = https://your-misp-instance
upload_iocs = yes
```

### **C. VirusTotal Integration**
```ini
# conf/processing.conf
[virustotal]
enabled = yes
apikey = your_vt_key
timeout = 60
```

---

## 🎯 **7. Custom Classification Frameworks**

### **A. Severity Scoring**
```python
# Custom severity levels
severity_levels = {
    1: "Informational",
    2: "Low", 
    3: "Medium",
    4: "High",
    5: "Critical",
    6: "Malicious"
}
```

### **B. Confidence Scoring**
```python
# Confidence levels
confidence_levels = {
    "low": 0.3,
    "medium": 0.6, 
    "high": 0.9,
    "certain": 1.0
}
```

### **C. Risk Assessment**
```python
# Risk calculation
risk_factors = [
    "network_activity",
    "file_operations", 
    "registry_modifications",
    "process_injection",
    "persistence_mechanisms"
]
```

---

## 🔧 **8. Cấu hình Enable All Frameworks**

### **File: conf/processing.conf**
```ini
[detections]
enabled = yes
# Enable all detection frameworks
behavior = yes
yara = yes
suricata = yes
virustotal = yes
clamav = yes

[flare_capa]
enabled = yes
static_analysis = yes
dynamic_analysis = yes

[mbcs]
enabled = yes
micro_behaviors = yes
objectives = yes

[threat_intelligence]
enabled = yes
threatfox = yes
misp = yes
malwarebazaar = yes
```

### **File: conf/integrations.conf**
```ini
[abusech]
threatfox = yes
malwarebazaar = yes
apikey = your_api_key

[mandiant_intel]
enabled = yes
api_access = your_access_key
api_secret = your_secret_key
```

### **File: conf/reporting.conf**
```ini
[misp]
enabled = yes
apikey = your_misp_key
url = https://your-misp-instance
upload_iocs = yes
extend_context = yes

[callback]
enabled = yes
url = http://your-siem/webhook
```

---

## 📈 **9. Kết quả Analysis Frameworks**

### **A. MITRE ATT&CK Results**
```json
{
  "ATTCK": {
    "DEFENSE EVASION": [
      "Obfuscated Files or Information [T1027]",
      "Process Injection [T1055]"
    ],
    "EXECUTION": [
      "Shared Modules [T1129]"
    ]
  }
}
```

### **B. MBCS Results**
```json
{
  "MBCS": {
    "objectives": ["OB0002", "OB0006"],
    "behaviors": ["E1027", "E1055"],
    "micro_behaviors": ["OC0004", "C0025"]
  }
}
```

### **C. CAPA Results**
```json
{
  "capabilities": {
    "anti-analysis": ["check for sandbox", "detect debugger"],
    "communication": ["HTTP communication", "DNS queries"],
    "persistence": ["registry modification", "service creation"]
  }
}
```

### **D. Family Classification**
```json
{
  "detections": [
    {
      "family": "Emotet",
      "source": "YARA",
      "confidence": 0.95
    },
    {
      "family": "Banking Trojan", 
      "source": "Behavior",
      "confidence": 0.87
    }
  ]
}
```

---

## 🚀 **10. Script Enable All Frameworks**

```bash
#!/bin/bash
# Enable all threat intelligence frameworks

CAPE_DIR="/opt/CAPEv2"
CONF_DIR="$CAPE_DIR/conf"

echo "=== ENABLING ALL THREAT FRAMEWORKS ==="

# 1. Enable detections in processing.conf
sed -i '/^\[detections\]/,/^\[/ s/enabled = no/enabled = yes/' $CONF_DIR/processing.conf
sed -i '/^\[detections\]/,/^\[/ s/behavior = no/behavior = yes/' $CONF_DIR/processing.conf
sed -i '/^\[detections\]/,/^\[/ s/yara = no/yara = yes/' $CONF_DIR/processing.conf
sed -i '/^\[detections\]/,/^\[/ s/suricata = no/suricata = yes/' $CONF_DIR/processing.conf

# 2. Enable FLARE CAPA
sed -i '/^\[flare_capa\]/,/^\[/ s/enabled = no/enabled = yes/' $CONF_DIR/processing.conf

# 3. Enable VirusTotal
sed -i '/^\[virustotal\]/,/^\[/ s/enabled = no/enabled = yes/' $CONF_DIR/processing.conf

# 4. Enable threat intelligence
sed -i '/^\[abusech\]/,/^\[/ s/threatfox = no/threatfox = yes/' $CONF_DIR/integrations.conf
sed -i '/^\[abusech\]/,/^\[/ s/malwarebazaar = no/malwarebazaar = yes/' $CONF_DIR/integrations.conf

# 5. Enable MISP reporting
sed -i '/^\[misp\]/,/^\[/ s/enabled = no/enabled = yes/' $CONF_DIR/reporting.conf

echo "✅ All threat frameworks enabled!"
echo "📝 Don't forget to configure API keys!"
```

---

## ✅ **11. Verification Checklist**

- [ ] **MITRE ATT&CK**: TTPs mapping enabled
- [ ] **MBCS**: Malware Behavior Catalog enabled  
- [ ] **FLARE CAPA**: Capability analysis enabled
- [ ] **YARA**: Pattern matching enabled
- [ ] **Suricata**: Network signatures enabled
- [ ] **Family Classification**: Malware family detection
- [ ] **ThreatFox**: IOC intelligence enabled
- [ ] **MISP**: Threat intelligence platform
- [ ] **VirusTotal**: Multi-engine detection
- [ ] **Custom Scoring**: Severity & confidence

---

**🎯 Với tất cả frameworks này, CAPEv2 cung cấp comprehensive threat analysis từ nhiều góc độ khác nhau!**
