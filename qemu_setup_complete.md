# QEMU Setup hoàn chỉnh cho CAPEv2

## Phân tích file qemu.conf hiện tại

### Những gì đã có:
✅ Path to qemu binary  
✅ Machine name: cape1  
✅ Platform: windows  
✅ IP: *************03  
✅ Memory: 8G  
✅ Tags: win10, x64  

### Những gì còn thiếu:
❌ **image path** - đường dẫn đến VM image  
❌ **arch** - architecture (x64)  
❌ **enable_kvm** - enable KVM acceleration  
❌ **interface** - network interface  
❌ **resultserver_ip** - sai cấu hình  

## BƯỚC 1: Tạo QEMU VM Image

### 1.1. Tạo VM image
```bash
#!/bin/bash
# File: create_qemu_vm.sh

echo "Creating QEMU VM for CAPEv2..."

# Tạo thư mục cho QEMU VMs
sudo mkdir -p /var/lib/libvirt/images/qemu
sudo chown -R $USER:$USER /var/lib/libvirt/images/qemu

# Tạo disk image cho cape1
echo "Creating disk image for cape1..."
qemu-img create -f qcow2 /var/lib/libvirt/images/qemu/cape1.qcow2 50G

# Kiểm tra image đã tạo
echo "Image info:"
qemu-img info /var/lib/libvirt/images/qemu/cape1.qcow2

echo "✅ VM image created: /var/lib/libvirt/images/qemu/cape1.qcow2"
```

### 1.2. Install Windows trong VM
```bash
#!/bin/bash
# File: install_windows_qemu.sh

ISO_PATH="/var/lib/libvirt/images/iso/windows10.iso"
VM_IMAGE="/var/lib/libvirt/images/qemu/cape1.qcow2"

echo "Installing Windows 10 in QEMU VM..."

# Kiểm tra ISO tồn tại
if [ ! -f "$ISO_PATH" ]; then
    echo "❌ Windows ISO not found: $ISO_PATH"
    echo "Please copy Windows 10 ISO to this location"
    exit 1
fi

# Start VM với ISO để install Windows
qemu-system-x86_64 \
    -enable-kvm \
    -m 8G \
    -cpu host \
    -smp 4 \
    -drive file=$VM_IMAGE,format=qcow2,if=virtio \
    -cdrom $ISO_PATH \
    -netdev user,id=net0,hostfwd=tcp::5555-:3389 \
    -device virtio-net,netdev=net0 \
    -vnc :1 \
    -boot d

echo "VM started with VNC on port 5901"
echo "Connect with: vncviewer localhost:5901"
echo "Install Windows 10, then shutdown VM"
```

### 1.3. Cấu hình network cho VM
```bash
#!/bin/bash
# File: setup_qemu_network.sh

echo "Setting up QEMU network..."

# Tạo bridge network cho QEMU
sudo ip link add name qemubr0 type bridge
sudo ip link set qemubr0 up
sudo ip addr add *************/24 dev qemubr0

# Tạo TAP interface
sudo ip tuntap add dev tap0 mode tap user $USER
sudo ip link set tap0 up
sudo ip link set tap0 master qemubr0

# Enable IP forwarding
echo 1 | sudo tee /proc/sys/net/ipv4/ip_forward

# NAT rules cho internet access (nếu cần)
sudo iptables -t nat -A POSTROUTING -s *************/24 -o enp4s0 -j MASQUERADE
sudo iptables -A FORWARD -i qemubr0 -o enp4s0 -j ACCEPT
sudo iptables -A FORWARD -i enp4s0 -o qemubr0 -m state --state RELATED,ESTABLISHED -j ACCEPT

echo "✅ QEMU network setup completed"
echo "Bridge: qemubr0 (*************/24)"
echo "TAP: tap0"
```

## BƯỚC 2: Cấu hình qemu.conf hoàn chỉnh

### 2.1. File qemu.conf đầy đủ
```ini
[qemu]
# Path to qemu binary
path = /usr/bin/qemu-system-x86_64

# List of available machines
machines = cape1

# Default network interface for tcpdump
interface = qemubr0

[cape1]
# VM label
label = cape1

# Path to VM image
image = /var/lib/libvirt/images/qemu/cape1.qcow2

# Snapshot name
snapshot = clean

# Architecture
arch = x64

# CPU type (optional)
cpu = host

# Enable KVM acceleration
enable_kvm = yes

# Memory allocation
memory = 8G

# Operating system platform
platform = windows

# VM IP address
ip = *************03

# Tags for identification
tags = win10,x64

# Network interface for this VM
interface = qemubr0

# Result server IP as seen by VM
resultserver_ip = *************

# Result server port as seen by VM
resultserver_port = 2042

# (Optional) Additional QEMU options
# options = -cpu host -smp 4
```

### 2.2. Script apply cấu hình
```bash
#!/bin/bash
# File: apply_qemu_config.sh

CAPE_DIR="/opt/CAPEv2"
CONF_DIR="$CAPE_DIR/conf"

echo "Applying QEMU configuration..."

# Backup existing config
cp $CONF_DIR/qemu.conf $CONF_DIR/qemu.conf.backup 2>/dev/null || true

# Create complete qemu.conf
cat > $CONF_DIR/qemu.conf << 'EOF'
[qemu]
path = /usr/bin/qemu-system-x86_64
machines = cape1
interface = qemubr0

[cape1]
label = cape1
image = /var/lib/libvirt/images/qemu/cape1.qcow2
snapshot = clean
arch = x64
cpu = host
enable_kvm = yes
memory = 8G
platform = windows
ip = *************03
tags = win10,x64
interface = qemubr0
resultserver_ip = *************
resultserver_port = 2042
EOF

# Update cuckoo.conf để sử dụng QEMU
sed -i 's/machinery = kvm/machinery = qemu/' $CONF_DIR/cuckoo.conf

echo "✅ QEMU configuration applied"
```

## BƯỚC 3: Start VM và cấu hình Windows

### 3.1. Script start VM
```bash
#!/bin/bash
# File: start_qemu_vm.sh

VM_IMAGE="/var/lib/libvirt/images/qemu/cape1.qcow2"

echo "Starting QEMU VM cape1..."

# Start VM với network bridge
qemu-system-x86_64 \
    -enable-kvm \
    -m 8G \
    -cpu host \
    -smp 4 \
    -drive file=$VM_IMAGE,format=qcow2,if=virtio \
    -netdev tap,id=net0,ifname=tap0,script=no,downscript=no \
    -device virtio-net,netdev=net0,mac=52:54:00:12:34:56 \
    -vnc :1 \
    -monitor stdio \
    -name cape1

echo "VM started with VNC on port 5901"
echo "Connect with: vncviewer localhost:5901"
```

### 3.2. Cấu hình IP trong Windows VM
```cmd
REM Trong Windows VM, mở Command Prompt as Administrator:

REM Set static IP
netsh interface ip set address name="Ethernet" static *************03 ************* *************

REM Set DNS
netsh interface ip set dns name="Ethernet" static *******
netsh interface ip add dns name="Ethernet" ******* index=2

REM Test connectivity
ping *************
ping *******

REM Check IP configuration
ipconfig /all
```

## BƯỚC 4: Tạo snapshot

### 4.1. Tạo clean snapshot
```bash
#!/bin/bash
# File: create_qemu_snapshot.sh

VM_IMAGE="/var/lib/libvirt/images/qemu/cape1.qcow2"

echo "Creating clean snapshot for cape1..."

# Shutdown VM trước khi tạo snapshot
echo "Please shutdown the VM first, then press Enter..."
read

# Tạo snapshot
qemu-img snapshot -c clean $VM_IMAGE

# Kiểm tra snapshot
echo "Available snapshots:"
qemu-img snapshot -l $VM_IMAGE

echo "✅ Clean snapshot created"
```

### 4.2. Script restore snapshot
```bash
#!/bin/bash
# File: restore_qemu_snapshot.sh

VM_IMAGE="/var/lib/libvirt/images/qemu/cape1.qcow2"

echo "Restoring clean snapshot for cape1..."

# Restore snapshot
qemu-img snapshot -a clean $VM_IMAGE

echo "✅ Snapshot restored to clean state"
```

## BƯỚC 5: Install CAPE Agent

### 5.1. Tạo agent ISO
```bash
#!/bin/bash
# File: create_qemu_agent_iso.sh

CAPE_DIR="/opt/CAPEv2"

echo "Creating agent ISO for QEMU VM..."

# Tạo thư mục agent
mkdir -p /tmp/qemu-agent
cp $CAPE_DIR/agent/agent.py /tmp/qemu-agent/
cp -r $CAPE_DIR/agent/*.py /tmp/qemu-agent/ 2>/dev/null || true

# Tạo Windows install script
cat > /tmp/qemu-agent/install_agent.bat << 'EOF'
@echo off
echo Installing CAPE Agent for QEMU...

mkdir C:\cape-agent
copy *.py C:\cape-agent\

echo @echo off > C:\cape-agent\start_agent.bat
echo cd C:\cape-agent >> C:\cape-agent\start_agent.bat
echo python agent.py ************* 2042 >> C:\cape-agent\start_agent.bat

copy C:\cape-agent\start_agent.bat "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\"

echo Agent installed! Will start on boot.
pause
EOF

# Tạo ISO
genisoimage -o /var/lib/libvirt/images/iso/qemu-agent.iso -V "QEMU_AGENT" -r -J /tmp/qemu-agent/

echo "✅ Agent ISO created: /var/lib/libvirt/images/iso/qemu-agent.iso"
```

### 5.2. Start VM với agent ISO
```bash
#!/bin/bash
# File: start_vm_with_agent.sh

VM_IMAGE="/var/lib/libvirt/images/qemu/cape1.qcow2"
AGENT_ISO="/var/lib/libvirt/images/iso/qemu-agent.iso"

echo "Starting VM with agent ISO..."

qemu-system-x86_64 \
    -enable-kvm \
    -m 8G \
    -cpu host \
    -smp 4 \
    -drive file=$VM_IMAGE,format=qcow2,if=virtio \
    -cdrom $AGENT_ISO \
    -netdev tap,id=net0,ifname=tap0,script=no,downscript=no \
    -device virtio-net,netdev=net0,mac=52:54:00:12:34:56 \
    -vnc :1 \
    -monitor stdio \
    -name cape1

echo "VM started with agent ISO attached"
echo "In VM: Install agent from CD drive"
```

## BƯỚC 6: Complete setup script

### 6.1. Script setup hoàn chỉnh
```bash
#!/bin/bash
# File: complete_qemu_setup.sh

echo "=== COMPLETE QEMU SETUP FOR CAPEV2 ==="

# 1. Create VM image
echo "Step 1: Creating VM image..."
./create_qemu_vm.sh

# 2. Setup network
echo ""
echo "Step 2: Setting up network..."
./setup_qemu_network.sh

# 3. Apply QEMU configuration
echo ""
echo "Step 3: Applying QEMU configuration..."
./apply_qemu_config.sh

# 4. Create agent ISO
echo ""
echo "Step 4: Creating agent ISO..."
./create_qemu_agent_iso.sh

echo ""
echo "=== SETUP COMPLETED ==="
echo ""
echo "MANUAL STEPS:"
echo "1. Install Windows: ./install_windows_qemu.sh"
echo "2. Configure IP in VM: *************03"
echo "3. Install agent from ISO"
echo "4. Create snapshot: ./create_qemu_snapshot.sh"
echo "5. Test analysis"
echo ""
echo "VM Management:"
echo "- Start VM: ./start_qemu_vm.sh"
echo "- Start with agent: ./start_vm_with_agent.sh"
echo "- Restore snapshot: ./restore_qemu_snapshot.sh"
```

## CÁCH SỬ DỤNG:

```bash
# 1. Setup hoàn chỉnh
chmod +x *.sh
sudo ./complete_qemu_setup.sh

# 2. Install Windows
./install_windows_qemu.sh
# VNC: vncviewer localhost:5901

# 3. Cấu hình IP trong VM: *************03

# 4. Install agent
./start_vm_with_agent.sh
# Trong VM: chạy install_agent.bat

# 5. Tạo snapshot
./create_qemu_snapshot.sh

# 6. Test CAPEv2
cd /opt/CAPEv2
python3 cuckoo.py
```

## Những điểm quan trọng cần bổ sung:

1. **image path**: `/var/lib/libvirt/images/qemu/cape1.qcow2`
2. **arch**: `x64`
3. **enable_kvm**: `yes`
4. **interface**: `qemubr0`
5. **resultserver_ip**: `*************` (không phải 0.0.0.0)
6. **Network setup**: TAP interface và bridge
7. **Snapshot management**: tạo và restore snapshots

File này sẽ setup QEMU hoàn chỉnh cho CAPEv2!
