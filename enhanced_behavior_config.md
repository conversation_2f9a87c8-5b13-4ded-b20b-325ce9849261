# CAPEv2 Enhanced Behavior Analysis Configuration

## 🔧 File cấu hình chính để tăng behavior information:

### 1. **conf/processing.conf** - Cấu hình behavior analysis

```ini
[behavior]
enabled = yes
# Toggle specific modules within the BehaviorAnalysis class
anomaly = yes
processtree = yes
summary = yes
enhanced = yes
encryptedbuffers = yes
# Should the server use a compressed version of behavioural logs?
loop_detection = no
# The number of calls per process to process. 0 switches the limit off.
# Tăng lên để capture nhiều API calls hơn (0 = unlimited)
analysis_call_limit = 0
# Use ram to boost processing speed. You will need more than 20GB of RAM for this feature.
ram_boost = yes
# Enable file activities tracking
file_activities = yes
# Enable pattern replacement for better analysis
replace_patterns = yes

[procmemory]
enabled = yes
strings = yes
# Enable IDA Pro analysis nếu có
idapro = no

[procmon]
# Enable process monitoring
enabled = yes

[memory]
# Enable memory analysis
enabled = yes

[CAPE]
enabled = yes
# Ex targetinfo standalone module
targetinfo = yes
# Ex dropped standalone module
dropped = yes
# Ex procdump standalone module
procdump = yes
# Amount of text to carve from plaintext files (bytes)
buffer = 16384
# Process files not bigger than value below in Mb
max_file_size = 200
# Scan for UserDB.TXT signature matches
userdb_signature = yes
# Enable pattern replacement
replace_patterns = yes

[detections]
enabled = yes
# Signatures
behavior = yes
yara = yes
suricata = yes
virustotal = yes
clamav = yes
```

### 2. **conf/auxiliary.conf** - Cấu hình auxiliary modules

```ini
[auxiliary_modules]
amsi = yes
browser = yes
curtain = yes
digisig = yes
disguise = yes
evtx = yes
human_windows = yes
human_linux = yes
procmon = yes
recentfiles = yes
screenshots_windows = yes
screenshots_linux = yes
sysmon_windows = yes
sysmon_linux = yes
tlsdump = yes
usage = yes
file_pickup = yes
permissions = yes
pre_script = yes
during_script = yes
filecollector = yes
tracee_linux = yes
sslkeylogfile = yes
browsermonitor = yes
wmi_etw = yes
dns_etw = yes

[procmon]
# Enable process monitoring
enabled = yes

[sysmon_windows]
# Enable Sysmon for Windows
enabled = yes

[evtx]
# Enable Windows Event Log analysis
enabled = yes

[amsi]
# Enable AMSI (Antimalware Scan Interface) monitoring
enabled = yes

[wmi_etw]
# Enable WMI ETW monitoring
enabled = yes

[dns_etw]
# Enable DNS ETW monitoring
enabled = yes
```

### 3. **conf/memory.conf** - Cấu hình memory analysis

```ini
[basic]
# Enable memory analysis
enabled = yes
# Guest profile (auto-detect)
guest_profile = 
# Delete memory dump sau khi analyze
delete_memdump = no

[malfind]
enabled = yes
filter = no

[apihooks]
enabled = yes
filter = no

[pslist]
enabled = yes
filter = no

[psxview]
enabled = yes
filter = no

[callbacks]
enabled = yes
filter = no

[idt]
enabled = yes
filter = no

[timers]
enabled = yes
filter = no

[messagehooks]
enabled = yes
filter = no

[getsids]
enabled = yes
filter = no

[privs]
enabled = yes
filter = no

[dlllist]
enabled = yes
filter = no

[handles]
enabled = yes
filter = no

[ldrmodules]
enabled = yes
filter = no

[mutantscan]
enabled = yes
filter = no

[devicetree]
enabled = yes
filter = no

[svcscan]
enabled = yes
filter = no

[modscan]
enabled = yes
filter = no
```

### 4. **conf/cuckoo.conf** - Cấu hình chính

```ini
[cuckoo]
machinery = kvm
tmppath = /tmp
# Enable memory dump cho behavior analysis
memory_dump = yes
# Tăng timeout để có đủ thời gian analyze
analysis_timeout = 600
critical_timeout = 300

[processing]
# Timeout cho processing
analysis_timeout = 600
critical_timeout = 300
# Enable network processing
sort_pcap = yes
resolve_dns = yes

[resultserver]
ip = 0.0.0.0
port = 2042
force_port = no
upload_max_size = 256
```

## 🐍 **Module Python quan trọng để chỉnh sửa:**

### 1. **modules/processing/behavior.py** ⭐ QUAN TRỌNG NHẤT
- File chính xử lý behavior analysis
- Chứa class `BehaviorAnalysis` và `ParseProcessLog`
- **Chỉnh sửa để tăng behavior info:**

```python
# Trong class ParseProcessLog, tăng buffer và tracking:
def __init__(self, log_path, options):
    # Tăng API limit
    self.api_limit = 0  # Unlimited API calls

    # Thêm tracking cho file operations
    self.file_operations = []
    self.registry_operations = []
    self.network_operations = []

    # Tăng buffer size
    self.buffer_size = 65536

# Thêm method để track file activities
def track_file_activity(self, call):
    if call["api"] in ["NtCreateFile", "NtOpenFile", "NtWriteFile", "NtReadFile"]:
        self.file_operations.append({
            "api": call["api"],
            "filename": self.get_argument(call, "FileName"),
            "timestamp": call["timestamp"]
        })
```

### 2. **modules/processing/CAPE.py** ⭐ CAPE Analysis
- Xử lý CAPE payloads và configuration extraction
- **Chỉnh sửa để extract nhiều thông tin:**

```python
# Tăng buffer size cho file analysis
buffer = 32768  # Thay vì 8192
max_file_size = 500  # Thay vì 90MB

# Enable thêm analysis features
userdb_signature = True
replace_patterns = True
```

### 3. **lib/cuckoo/common/cape_utils.py** ⭐ CAPE Utilities
- Utilities cho CAPE analysis
- Chứa các parser cho malware families
- **Có thể thêm custom parsers**

### 4. **modules/signatures/CAPE.py** ⭐ Behavior Signatures
- Signatures cho behavior detection
- **Thêm custom signatures để detect thêm behaviors:**

```python
class CustomBehaviorSignature(Signature):
    name = "custom_behavior"
    description = "Custom behavior detection"
    severity = 2
    categories = ["malware"]

    def on_call(self, call, process):
        # Custom logic để detect specific behaviors
        if call["api"] in ["RegSetValueEx", "RegCreateKey"]:
            # Track registry modifications
            return True
```

### 5. **analyzer/windows/modules/auxiliary/** - Auxiliary Modules
- **wmi_etw.py**: WMI Event Tracing for Windows
- **watchdownloads.py**: Monitor file downloads
- **procmon.py**: Process monitoring
- **sysmon_windows.py**: Sysmon integration

### 6. **modules/processing/memory.py** - Memory Analysis
- Xử lý memory dumps
- Có thể enhance để extract thêm artifacts

### 7. **modules/processing/network.py** - Network Analysis
- Phân tích network traffic
- Có thể tùy chỉnh để capture thêm network behaviors

## 🔍 **Các thay đổi cụ thể để tăng behavior information:**

### A. Trong behavior.py:
```python
# Tăng buffer size cho API calls
self.api_limit = 0  # Unlimited API calls
self.buffer_size = 32768  # Tăng buffer size

# Enable thêm tracking
self.track_file_operations = True
self.track_registry_operations = True
self.track_network_operations = True
```

### B. Trong processing.conf:
```ini
# Tăng các giá trị này:
analysis_call_limit = 0
buffer = 32768
max_file_size = 500
ram_boost = yes
file_activities = yes
```

### C. Enable thêm auxiliary modules:
```ini
procmon = yes
sysmon_windows = yes
evtx = yes
amsi = yes
wmi_etw = yes
```

## 🚀 **Script tự động để apply enhanced behavior config:**

### Script: apply_enhanced_behavior.sh
```bash
#!/bin/bash
# Enhanced Behavior Configuration Script for CAPEv2

CAPE_DIR="/opt/CAPEv2"
CONF_DIR="$CAPE_DIR/conf"

echo "=== APPLYING ENHANCED BEHAVIOR CONFIGURATION ==="

# Backup existing configs
echo "1. Backing up existing configs..."
mkdir -p $CONF_DIR/backup_$(date +%Y%m%d_%H%M%S)
cp $CONF_DIR/*.conf $CONF_DIR/backup_$(date +%Y%m%d_%H%M%S)/ 2>/dev/null || true

# Apply enhanced processing.conf
echo "2. Applying enhanced processing.conf..."
cat > $CONF_DIR/processing.conf << 'EOF'
[behavior]
enabled = yes
anomaly = yes
processtree = yes
summary = yes
enhanced = yes
encryptedbuffers = yes
loop_detection = no
analysis_call_limit = 0
ram_boost = yes
file_activities = yes
replace_patterns = yes

[procmemory]
enabled = yes
strings = yes
idapro = no

[procmon]
enabled = yes

[memory]
enabled = yes

[CAPE]
enabled = yes
targetinfo = yes
dropped = yes
procdump = yes
buffer = 32768
max_file_size = 500
userdb_signature = yes
replace_patterns = yes

[detections]
enabled = yes
behavior = yes
yara = yes
suricata = yes
virustotal = yes
clamav = yes
EOF

# Apply enhanced auxiliary.conf
echo "3. Applying enhanced auxiliary.conf..."
cat > $CONF_DIR/auxiliary.conf << 'EOF'
[auxiliary_modules]
amsi = yes
browser = yes
curtain = yes
digisig = yes
disguise = yes
evtx = yes
human_windows = yes
procmon = yes
recentfiles = yes
screenshots_windows = yes
sysmon_windows = yes
tlsdump = yes
usage = yes
file_pickup = yes
permissions = yes
filecollector = yes
wmi_etw = yes
dns_etw = yes

[procmon]
enabled = yes

[sysmon_windows]
enabled = yes

[evtx]
enabled = yes

[amsi]
enabled = yes

[wmi_etw]
enabled = yes

[dns_etw]
enabled = yes
EOF

echo "4. Enhanced behavior configuration applied successfully!"
echo "5. Restart CAPE services to apply changes:"
echo "   sudo systemctl restart cape cape-processor cape-web"
```

## 📊 **Kết quả mong đợi sau khi apply config:**

### Thông tin behavior sẽ tăng lên bao gồm:
1. **API Calls**: Unlimited API calls tracking
2. **File Operations**: Chi tiết file read/write/delete
3. **Registry Operations**: Đầy đủ registry modifications
4. **Process Tree**: Chi tiết process spawning
5. **Memory Analysis**: Memory dumps và artifacts
6. **Network Activities**: Network connections và traffic
7. **System Events**: Windows Event Logs (EVTX)
8. **WMI Events**: WMI ETW tracing
9. **AMSI Events**: Antimalware Scan Interface logs
10. **Sysmon Events**: Detailed system monitoring

### Performance Impact:
- **RAM Usage**: Tăng đáng kể (cần >20GB RAM cho ram_boost)
- **Analysis Time**: Tăng 2-3 lần
- **Storage**: Tăng 3-5 lần dung lượng reports
- **CPU Usage**: Tăng 50-100% trong quá trình analysis
