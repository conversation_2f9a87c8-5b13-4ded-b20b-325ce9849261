{% extends "base_report.html" %}

{% block title %}Analysis Report - {{ report.info.id }}{% endblock %}

{% block content %}
<!-- Report Header -->
<div class="report-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 mb-2">
                    <i class="fas fa-file-alt me-3"></i>
                    Analysis Report
                </h1>
                <p class="lead mb-0">
                    File: <strong>{{ report.target.file.name }}</strong>
                </p>
                <p class="mb-0">
                    <small>Analysis ID: {{ report.info.id }} | 
                    Started: {{ report.info.started }} | 
                    Duration: {{ report.info.duration }}s</small>
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="status-badge status-{{ report.info.score|score_to_status }}">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Score: {{ report.info.score }}/10
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Overview Section -->
    <section id="overview" class="report-section">
        <h2 class="section-title">
            <i class="fas fa-chart-pie me-2"></i>
            Analysis Overview
        </h2>
        
        <div class="row">
            <!-- Metrics Cards -->
            <div class="col-md-3 col-sm-6">
                <div class="metric-card">
                    <div class="metric-value">{{ report.info.score }}</div>
                    <div class="metric-label">Threat Score</div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="metric-card">
                    <div class="metric-value">{{ report.behavior.processes|length }}</div>
                    <div class="metric-label">Processes</div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="metric-card">
                    <div class="metric-value">{{ report.network.tcp|length + report.network.udp|length }}</div>
                    <div class="metric-label">Network Connections</div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="metric-card">
                    <div class="metric-value">{{ report.dropped|length }}</div>
                    <div class="metric-label">Dropped Files</div>
                </div>
            </div>
        </div>

        <!-- File Information -->
        <div class="row mt-4">
            <div class="col-md-6">
                <h4><i class="fas fa-file me-2"></i>File Information</h4>
                <table class="table table-custom">
                    <tbody>
                        <tr>
                            <td><strong>Name</strong></td>
                            <td>{{ report.target.file.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Size</strong></td>
                            <td>{{ report.target.file.size|filesizeformat }}</td>
                        </tr>
                        <tr>
                            <td><strong>Type</strong></td>
                            <td>{{ report.target.file.type }}</td>
                        </tr>
                        <tr>
                            <td><strong>MD5</strong></td>
                            <td><code>{{ report.target.file.md5 }}</code></td>
                        </tr>
                        <tr>
                            <td><strong>SHA1</strong></td>
                            <td><code>{{ report.target.file.sha1 }}</code></td>
                        </tr>
                        <tr>
                            <td><strong>SHA256</strong></td>
                            <td><code>{{ report.target.file.sha256 }}</code></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="col-md-6">
                <h4><i class="fas fa-cogs me-2"></i>Analysis Environment</h4>
                <table class="table table-custom">
                    <tbody>
                        <tr>
                            <td><strong>Machine</strong></td>
                            <td>{{ report.info.machine.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Platform</strong></td>
                            <td>{{ report.info.platform }}</td>
                        </tr>
                        <tr>
                            <td><strong>Version</strong></td>
                            <td>{{ report.info.version }}</td>
                        </tr>
                        <tr>
                            <td><strong>Started</strong></td>
                            <td>{{ report.info.started }}</td>
                        </tr>
                        <tr>
                            <td><strong>Ended</strong></td>
                            <td>{{ report.info.ended }}</td>
                        </tr>
                        <tr>
                            <td><strong>Duration</strong></td>
                            <td>{{ report.info.duration }}s</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <!-- Signatures Section -->
    {% if report.signatures %}
    <section class="report-section">
        <h2 class="section-title">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Detected Signatures ({{ report.signatures|length }})
        </h2>
        
        <div class="row">
            {% for signature in report.signatures %}
            <div class="col-md-6 mb-3">
                <div class="card border-left-{{ signature.severity|severity_to_color }}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <h5 class="card-title">{{ signature.name }}</h5>
                            <span class="badge bg-{{ signature.severity|severity_to_color }}">
                                {{ signature.severity|upper }}
                            </span>
                        </div>
                        <p class="card-text">{{ signature.description }}</p>
                        {% if signature.marks %}
                        <small class="text-muted">
                            <strong>Indicators:</strong>
                            {% for mark in signature.marks[:3] %}
                                {{ mark.description }}{% if not loop.last %}, {% endif %}
                            {% endfor %}
                            {% if signature.marks|length > 3 %}
                                and {{ signature.marks|length - 3 }} more...
                            {% endif %}
                        </small>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </section>
    {% endif %}

    <!-- Processing Statistics -->
    {% if report.statistics and report.statistics.processing %}
    <section class="report-section">
        <h2 class="section-title">
            <i class="fas fa-chart-bar me-2"></i>
            Processing Statistics
        </h2>
        
        <div class="table-responsive">
            <table class="table table-custom">
                <thead>
                    <tr>
                        <th>Module</th>
                        <th>Time (seconds)</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    {% set total_time = report.statistics.processing | sum(attribute='time') %}
                    {% for module in report.statistics.processing %}
                    <tr>
                        <td><strong>{{ module.name }}</strong></td>
                        <td>{{ "%.3f"|format(module.time) }}</td>
                        <td>
                            {% if total_time > 0 %}
                            <div class="progress progress-custom">
                                <div class="progress-bar progress-bar-custom" 
                                     style="--progress-width: {{ (module.time / total_time * 100)|round(1) }}%; width: var(--progress-width)">
                                    {{ (module.time / total_time * 100)|round(1) }}%
                                </div>
                            </div>
                            {% else %}
                            0%
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </section>
    {% endif %}

    <!-- Network Activity -->
    {% if report.network %}
    <section class="report-section">
        <h2 class="section-title">
            <i class="fas fa-network-wired me-2"></i>
            Network Activity
        </h2>
        
        <!-- Search Box -->
        <div class="mb-3">
            <input type="text" class="form-control search-box" id="networkSearch" 
                   placeholder="Search network connections...">
        </div>
        
        <!-- TCP Connections -->
        {% if report.network.tcp %}
        <h4><i class="fas fa-exchange-alt me-2"></i>TCP Connections ({{ report.network.tcp|length }})</h4>
        <div class="table-responsive mb-4">
            <table class="table table-custom" id="tcpTable">
                <thead>
                    <tr>
                        <th>Source</th>
                        <th>Destination</th>
                        <th>Port</th>
                        <th>Country</th>
                    </tr>
                </thead>
                <tbody>
                    {% for connection in report.network.tcp %}
                    <tr>
                        <td>{{ connection.src }}</td>
                        <td>{{ connection.dst }}</td>
                        <td>{{ connection.dport }}</td>
                        <td>
                            {% if connection.country %}
                            <span class="badge bg-info">{{ connection.country }}</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
        
        <!-- DNS Requests -->
        {% if report.network.dns %}
        <h4><i class="fas fa-globe me-2"></i>DNS Requests ({{ report.network.dns|length }})</h4>
        <div class="table-responsive">
            <table class="table table-custom" id="dnsTable">
                <thead>
                    <tr>
                        <th>Request</th>
                        <th>Type</th>
                        <th>Answer</th>
                    </tr>
                </thead>
                <tbody>
                    {% for dns in report.network.dns %}
                    <tr>
                        <td>{{ dns.request }}</td>
                        <td><span class="badge bg-secondary">{{ dns.type }}</span></td>
                        <td>{{ dns.answers|join(', ') if dns.answers else '-' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
    </section>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Search functionality for network tables
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('networkSearch');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const filter = this.value.toLowerCase();
            const tables = ['tcpTable', 'dnsTable'];
            
            tables.forEach(function(tableId) {
                const table = document.getElementById(tableId);
                if (table) {
                    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
                    Array.from(rows).forEach(function(row) {
                        const text = row.textContent.toLowerCase();
                        row.style.display = text.includes(filter) ? '' : 'none';
                    });
                }
            });
        });
    }
});

// Auto-refresh for pending analyses
var analysisStatus = '{{ report.info.status|escapejs }}';
if (analysisStatus === 'pending' || analysisStatus === 'running') {
    setTimeout(function() {
        location.reload();
    }, 30000);
}
</script>
{% endblock %}
