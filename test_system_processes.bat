@echo off
REM Test script for system process accessibility in Windows VM
REM Run this in the Windows VM to verify system process status

echo ========================================
echo CAPEv2 System Process Accessibility Test
echo ========================================
echo.

echo [+] Checking if running as Administrator...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo     ✅ Running as Administrator
) else (
    echo     ❌ NOT running as Administrator
    echo     Please run this script as Administrator
    pause
    exit /b 1
)
echo.

echo [+] Checking Windows Defender status...
powershell -Command "Get-MpPreference | Select-Object DisableRealtimeMonitoring" 2>nul
if %errorLevel% == 0 (
    echo     ✅ Windows Defender check completed
) else (
    echo     ⚠️  Could not check Windows Defender status
)
echo.

echo [+] Checking UAC status...
reg query "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v EnableLUA 2>nul | findstr "0x0"
if %errorLevel% == 0 (
    echo     ✅ UAC is disabled
) else (
    echo     ❌ UAC is still enabled
)
echo.

echo [+] Checking system processes...
echo.

echo     dllhost.exe processes:
tasklist /fi "imagename eq dllhost.exe" /fo table 2>nul | findstr /v "INFO:"
if %errorLevel% == 0 (
    echo     ✅ dllhost.exe found
) else (
    echo     ❌ dllhost.exe not found
)
echo.

echo     svchost.exe processes (first 10):
tasklist /fi "imagename eq svchost.exe" /fo table 2>nul | findstr /v "INFO:" | head -10
if %errorLevel% == 0 (
    echo     ✅ svchost.exe found
) else (
    echo     ❌ svchost.exe not found
)
echo.

echo     explorer.exe process:
tasklist /fi "imagename eq explorer.exe" /fo table 2>nul | findstr /v "INFO:"
if %errorLevel% == 0 (
    echo     ✅ explorer.exe found
) else (
    echo     ❌ explorer.exe not found
)
echo.

echo     services.exe process:
tasklist /fi "imagename eq services.exe" /fo table 2>nul | findstr /v "INFO:"
if %errorLevel% == 0 (
    echo     ✅ services.exe found
) else (
    echo     ❌ services.exe not found
)
echo.

echo [+] Checking CAPEv2 Agent service...
sc query CAPEv2Agent 2>nul
if %errorLevel% == 0 (
    echo     ✅ CAPEv2Agent service found
    sc qc CAPEv2Agent | findstr "SERVICE_START_NAME"
) else (
    echo     ❌ CAPEv2Agent service not found
)
echo.

echo [+] Checking debug privileges...
reg query "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v LocalAccountTokenFilterPolicy 2>nul | findstr "0x1"
if %errorLevel% == 0 (
    echo     ✅ Debug privileges enabled
) else (
    echo     ❌ Debug privileges not enabled
)
echo.

echo [+] Checking DEP settings...
bcdedit /enum | findstr nx
echo.

echo [+] Checking ASLR settings...
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v MoveImages 2>nul
echo.

echo ========================================
echo Test completed!
echo ========================================
echo.
echo Summary:
echo - If most checks show ✅, your VM is properly configured
echo - If you see ❌, review the setup steps in the guide
echo - dllhost.exe and svchost.exe should be visible
echo - CAPEv2Agent should run as LocalSystem
echo.
echo Next steps:
echo 1. If configuration looks good, run a test analysis
echo 2. Check CAPEv2 logs for injection success messages
echo 3. Monitor behavior.log for system process activity
echo.
pause
