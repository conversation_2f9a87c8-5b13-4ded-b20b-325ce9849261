#!/usr/bin/env python3
"""
Debug script to test the web service and see exactly what's happening
"""

import requests
import json
import time
import sys

BASE_URL = "http://localhost:5000"

def debug_web_service():
    """Debug the web service to see exact responses"""
    print("🔍 Web Service Debug Testing")
    print("=" * 50)
    
    # Submit a file
    print("1️⃣ Submitting test file...")
    test_content = "X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*"
    test_content += "A" * 10000  # Make it larger
    
    files = {'file': ('debug_test.exe', test_content)}
    data = {
        'options': 'timeout=600,enforce_timeout=1',
        'force_reanalyze': 'true'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/analyze", files=files, data=data)
        print(f"Submit response status: {response.status_code}")
        print(f"Submit response: {response.text}")
        
        if response.status_code != 200:
            return
        
        result = response.json()
        if result.get('error'):
            print(f"Submit error: {result}")
            return
        
        analysis_id = result['analysis_id']
        print(f"Analysis ID: {analysis_id}")
        
    except Exception as e:
        print(f"Submit error: {e}")
        return
    
    # Monitor the analysis
    print(f"\n2️⃣ Monitoring analysis {analysis_id}...")
    
    for i in range(120):  # 10 minutes
        try:
            # Check status
            response = requests.get(f"{BASE_URL}/api/status/{analysis_id}")
            print(f"\nCheck #{i+1} - HTTP {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"Status response: {json.dumps(result, indent=2)}")
                
                status = result.get('status')
                if status in ['reported', 'completed', 'failed']:
                    print(f"Final status reached: {status}")
                    break
                elif status == 'processing':
                    print("🎯 PROCESSING STATUS DETECTED!")
                    
            else:
                print(f"Status check failed: {response.text}")
            
            time.sleep(5)
            
        except Exception as e:
            print(f"Status check error: {e}")
            time.sleep(5)
    
    # Try to get the report
    print(f"\n3️⃣ Trying to get report...")
    try:
        response = requests.get(f"{BASE_URL}/api/report/{analysis_id}")
        print(f"Report response status: {response.status_code}")
        print(f"Report response: {response.text[:500]}...")
        
    except Exception as e:
        print(f"Report error: {e}")

def debug_cape_service_directly():
    """Test the CAPE service directly"""
    print("🔍 Testing CAPE Service Directly")
    print("=" * 50)
    
    from cape_service import CAPEAnalysisService
    from config import Config
    
    # Create service
    service = CAPEAnalysisService(
        cape_url=Config.CAPE_URL,
        timeout=30,  # Short timeout to trigger the issue
        max_wait=60,
        verbose=True
    )
    
    # Create a test file
    import tempfile
    import os
    
    test_content = "X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*"
    test_content += "A" * 20000  # 20KB
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.exe', delete=False) as f:
        f.write(test_content)
        temp_file = f.name
    
    try:
        print(f"Testing with file: {temp_file}")
        
        # Analyze file
        result = service.analyze_file(temp_file, 'timeout=600', force_reanalyze=True)
        
        print(f"Analysis result:")
        print(json.dumps(result, indent=2))
        
        # Check specific fields
        if result.get('error'):
            print(f"\n🔍 Error analysis:")
            print(f"  Error: {result.get('error')}")
            print(f"  Message: {result.get('message')}")
            print(f"  Still processing: {result.get('still_processing')}")
            print(f"  Task ID: {result.get('task_id')}")
            
            if result.get('still_processing'):
                print(f"  🎯 STILL_PROCESSING FLAG DETECTED!")
            else:
                print(f"  ❌ Still processing flag NOT set")
        
    except Exception as e:
        print(f"Analysis error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        try:
            os.unlink(temp_file)
        except:
            pass

def test_with_existing_task(task_id):
    """Test with an existing CAPE task ID"""
    print(f"🔍 Testing with existing task: {task_id}")
    print("=" * 50)
    
    from cape_service import CAPEAnalysisService
    from config import Config
    
    service = CAPEAnalysisService(
        cape_url=Config.CAPE_URL,
        timeout=10,
        max_wait=0,
        verbose=True
    )
    
    # Check status
    print("1️⃣ Checking status...")
    status = service.check_status(task_id)
    print(f"Status: {status}")
    
    # Try to get report
    print("\n2️⃣ Trying to get report...")
    report = service.get_report(task_id)
    
    if report is None:
        print("Report is None - this might indicate 'still being analyzed'")
    elif report.get('error'):
        print(f"Report error: {report}")
    else:
        print(f"Report available with keys: {list(report.keys())}")

if __name__ == '__main__':
    if len(sys.argv) > 1:
        if sys.argv[1] == 'cape':
            debug_cape_service_directly()
        elif sys.argv[1] == 'task':
            if len(sys.argv) > 2:
                test_with_existing_task(sys.argv[2])
            else:
                print("Usage: python debug_web_service.py task <task_id>")
        else:
            print("Usage: python debug_web_service.py [cape|task <task_id>]")
    else:
        debug_web_service()
