{% extends "base.html" %}

{% block title %}All Analyses - CAPE Web Service{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-secondary text-white">
                <h3 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>All Analyses
                </h3>
            </div>
            <div class="card-body">
                {% if analyses %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Filename</th>
                                <th>Status</th>
                                <th>Mode</th>
                                <th>Created</th>
                                <th>Duration</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for analysis in analyses %}
                            <tr>
                                <td>
                                    <i class="fas fa-file me-2"></i>
                                    <strong>{{ analysis.filename }}</strong>
                                    <br>
                                    <small class="text-muted">ID: {{ analysis.id[:8] }}...</small>
                                    {% if analysis.was_cached %}
                                    <br><span class="badge bg-success"><i class="fas fa-database me-1"></i>Cached</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if analysis.status == 'pending' %}
                                    <span class="badge bg-warning status-pending">
                                        <i class="fas fa-hourglass-start me-1"></i>Pending
                                    </span>
                                    {% elif analysis.status == 'running' %}
                                    <span class="badge bg-info status-running">
                                        <i class="fas fa-spinner fa-spin me-1"></i>Running
                                    </span>
                                    {% elif analysis.status == 'processing' %}
                                    <span class="badge bg-primary">
                                        <i class="fas fa-cogs fa-spin me-1"></i>Processing
                                    </span>
                                    {% elif analysis.status == 'completed' %}
                                    <span class="badge bg-success status-completed">
                                        <i class="fas fa-check-circle me-1"></i>Completed
                                    </span>
                                    {% elif analysis.status == 'reported' %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-file-alt me-1"></i>Reported
                                    </span>
                                    {% elif analysis.status == 'failed' %}
                                    <span class="badge bg-danger status-failed">
                                        <i class="fas fa-exclamation-triangle me-1"></i>Failed
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ analysis.analysis_mode.title() }}</span>
                                    {% if analysis.force_reanalyze %}
                                    <br><span class="badge bg-warning mt-1"><i class="fas fa-redo me-1"></i>Force</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>{{ analysis.created_at }}</small>
                                </td>
                                <td>
                                    {% if analysis.started_at and analysis.completed_at %}
                                    <small class="text-success">Completed</small>
                                    {% elif analysis.started_at %}
                                    <small class="text-info">Running...</small>
                                    {% else %}
                                    <small class="text-muted">-</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('analysis_status', analysis_id=analysis.id) }}"
                                           class="btn btn-outline-primary" title="View Status">
                                            <i class="fas fa-chart-line"></i>
                                        </a>
                                        {% if analysis.status in ['completed', 'reported'] %}
                                        <a href="{{ url_for('view_report', analysis_id=analysis.id) }}"
                                           class="btn btn-outline-success" title="View Report">
                                            <i class="fas fa-file-alt"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No analyses found</h5>
                    <p class="text-muted">Upload a file to start your first analysis.</p>
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>Upload File
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-refresh page every 30 seconds
setTimeout(function() {
    location.reload();
}, 30000);
</script>
{% endblock %}
