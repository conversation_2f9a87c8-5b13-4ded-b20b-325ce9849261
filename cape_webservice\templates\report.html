{% extends "base.html" %}

{% block title %}Analysis Report{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-file-alt me-2"></i>Analysis Report</h3>
                </div>
                <div class="card-body">
                    <!-- Analysis Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5><i class="fas fa-info-circle me-2"></i>Analysis Information</h5>
                            <p><strong>File:</strong> {{ analysis.filename }}</p>
                            <p><strong>Analysis ID:</strong> <code>{{ analysis.id }}</code></p>
                            {% if analysis.cape_task_id %}
                            <p><strong>CAPE Task ID:</strong> <code>{{ analysis.cape_task_id }}</code></p>
                            {% endif %}
                            {% if analysis.file_hash %}
                            <p><strong>File Hash:</strong> <code class="hash-value">{{ analysis.file_hash }}</code></p>
                            {% endif %}
                            {% if analysis.was_cached %}
                            <p><span class="badge bg-success"><i class="fas fa-database me-1"></i>Cached Result</span></p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-clock me-2"></i>Timing</h5>
                            <p><strong>Created:</strong> {{ analysis.created_at }}</p>
                            {% if analysis.started_at and analysis.completed_at %}
                            <p><strong>Started:</strong> {{ analysis.started_at }}</p>
                            <p><strong>Completed:</strong> {{ analysis.completed_at }}</p>
                            {% endif %}
                            <p><strong>Mode:</strong> <span class="badge bg-primary">{{ analysis.analysis_mode.title() }}</span></p>
                        </div>
                    </div>

                    {% if report %}
                    <!-- Navigation Tabs -->
                    <ul class="nav nav-tabs" id="reportTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                                <i class="fas fa-chart-pie me-1"></i>Quick Overview
                            </button>
                        </li>
                        {% if report.target %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="target-tab" data-bs-toggle="tab" data-bs-target="#target" type="button" role="tab">
                                <i class="fas fa-file me-1"></i>Target File
                            </button>
                        </li>
                        {% endif %}
                        {% if report.behavior %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="behavior-tab" data-bs-toggle="tab" data-bs-target="#behavior" type="button" role="tab">
                                <i class="fas fa-microscope me-1"></i>Behavior
                                <span class="badge bg-info ms-1">{{ report.behavior.processes|length if report.behavior.processes else 0 }}</span>
                            </button>
                        </li>
                        {% endif %}
                        {% if report.signatures %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="signatures-tab" data-bs-toggle="tab" data-bs-target="#signatures" type="button" role="tab">
                                <i class="fas fa-exclamation-triangle me-1"></i>Signatures
                                <span class="badge bg-danger ms-1">{{ report.signatures|length }}</span>
                            </button>
                        </li>
                        {% endif %}
                        {% if report.network %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="network-tab" data-bs-toggle="tab" data-bs-target="#network" type="button" role="tab">
                                <i class="fas fa-network-wired me-1"></i>Network
                                <span class="badge bg-warning ms-1">{{ report.network.hosts|length if report.network.hosts else 0 }}</span>
                            </button>
                        </li>
                        {% endif %}
                        {% if report.CAPE %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="cape-tab" data-bs-toggle="tab" data-bs-target="#cape" type="button" role="tab">
                                <i class="fas fa-bug me-1"></i>CAPE
                                <span class="badge bg-danger ms-1">{{ report.CAPE.payloads|length if report.CAPE.payloads else 0 }}</span>
                            </button>
                        </li>
                        {% endif %}
                        {% if report.dropped %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="dropped-tab" data-bs-toggle="tab" data-bs-target="#dropped" type="button" role="tab">
                                <i class="fas fa-file-code me-1"></i>Dropped Files
                                <span class="badge bg-success ms-1">{{ report.dropped|length }}</span>
                            </button>
                        </li>
                        {% endif %}
                        {% if report.static %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="static-tab" data-bs-toggle="tab" data-bs-target="#static" type="button" role="tab">
                                <i class="fas fa-search-plus me-1"></i>Static Analysis
                            </button>
                        </li>
                        {% endif %}
                        {% if report.capa_summary %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="capa-tab" data-bs-toggle="tab" data-bs-target="#capa" type="button" role="tab">
                                <i class="fas fa-shield-alt me-1"></i>CAPA
                            </button>
                        </li>
                        {% endif %}
                        {% if report.mitre_attck %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="mitre-tab" data-bs-toggle="tab" data-bs-target="#mitre" type="button" role="tab">
                                <i class="fas fa-crosshairs me-1"></i>MITRE ATT&CK
                                <span class="badge bg-warning ms-1">{{ report.mitre_attck|length }}</span>
                            </button>
                        </li>
                        {% endif %}
                        {% if report.ttps %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="ttps-tab" data-bs-toggle="tab" data-bs-target="#ttps" type="button" role="tab">
                                <i class="fas fa-tactics me-1"></i>TTPs
                                <span class="badge bg-info ms-1">{{ report.ttps|length }}</span>
                            </button>
                        </li>
                        {% endif %}
                        {% if report.memory %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="memory-tab" data-bs-toggle="tab" data-bs-target="#memory" type="button" role="tab">
                                <i class="fas fa-memory me-1"></i>Memory
                            </button>
                        </li>
                        {% endif %}
                        {% if report.procmon %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="procmon-tab" data-bs-toggle="tab" data-bs-target="#procmon" type="button" role="tab">
                                <i class="fas fa-tasks me-1"></i>Process Monitor
                                <span class="badge bg-secondary ms-1">{{ report.procmon|length if report.procmon else 0 }}</span>
                            </button>
                        </li>
                        {% endif %}
                        {% if report.procmemory %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="procmemory-tab" data-bs-toggle="tab" data-bs-target="#procmemory" type="button" role="tab">
                                <i class="fas fa-microchip me-1"></i>Process Memory
                                <span class="badge bg-primary ms-1">{{ report.procmemory|length if report.procmemory else 0 }}</span>
                            </button>
                        </li>
                        {% endif %}
                        {% if report.url_analysis %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="url-tab" data-bs-toggle="tab" data-bs-target="#url" type="button" role="tab">
                                <i class="fas fa-link me-1"></i>URL Analysis
                            </button>
                        </li>
                        {% endif %}
                        {% if report.shots %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="shots-tab" data-bs-toggle="tab" data-bs-target="#shots" type="button" role="tab">
                                <i class="fas fa-camera me-1"></i>Screenshots
                                <span class="badge bg-info ms-1">{{ report.shots|length }}</span>
                            </button>
                        </li>
                        {% endif %}
                        {% if report.malscore %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="malscore-tab" data-bs-toggle="tab" data-bs-target="#malscore" type="button" role="tab">
                                <i class="fas fa-calculator me-1"></i>Malware Score
                            </button>
                        </li>
                        {% endif %}
                        {% if report.malstatus %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="malstatus-tab" data-bs-toggle="tab" data-bs-target="#malstatus" type="button" role="tab">
                                <i class="fas fa-flag me-1"></i>Malware Status
                            </button>
                        </li>
                        {% endif %}
                        {% if report.statistics %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="statistics-tab" data-bs-toggle="tab" data-bs-target="#statistics" type="button" role="tab">
                                <i class="fas fa-chart-bar me-1"></i>Statistics
                            </button>
                        </li>
                        {% endif %}
                        {% if report.debug %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="debug-tab" data-bs-toggle="tab" data-bs-target="#debug" type="button" role="tab">
                                <i class="fas fa-bug me-1"></i>Debug
                            </button>
                        </li>
                        {% endif %}
                        {% if report.strings %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="strings-tab" data-bs-toggle="tab" data-bs-target="#strings" type="button" role="tab">
                                <i class="fas fa-font me-1"></i>Strings
                                <span class="badge bg-secondary ms-1">{{ report.strings|length }}</span>
                            </button>
                        </li>
                        {% endif %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="raw-tab" data-bs-toggle="tab" data-bs-target="#raw" type="button" role="tab">
                                <i class="fas fa-code me-1"></i>Raw Data
                            </button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content" id="reportTabsContent">
                        <!-- Quick Overview Tab -->
                        <div class="tab-pane fade show active" id="overview" role="tabpanel">
                            <div class="report-section">
                                <h4><i class="fas fa-chart-pie me-2"></i>Quick Overview</h4>
                                
                                <!-- High-level Summary -->
                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h6>Threat Level</h6>
                                                {% if report.signatures %}
                                                {% set high_sigs = report.signatures | selectattr('severity', 'equalto', 3) | list %}
                                                {% if high_sigs|length > 0 %}
                                                <h4 class="text-danger">HIGH</h4>
                                                <p class="text-danger">{{ high_sigs|length }} critical detections</p>
                                                {% else %}
                                                <h4 class="text-warning">MEDIUM</h4>
                                                <p class="text-warning">{{ report.signatures|length }} detections</p>
                                                {% endif %}
                                                {% else %}
                                                <h4 class="text-success">LOW</h4>
                                                <p class="text-success">No signatures detected</p>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h6>Network Activity</h6>
                                                {% if report.network and report.network.hosts %}
                                                <h4 class="text-warning">{{ report.network.hosts|length }}</h4>
                                                <p>Hosts contacted</p>
                                                {% else %}
                                                <h4 class="text-success">0</h4>
                                                <p>No network activity</p>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h6>Processes</h6>
                                                {% if report.behavior and report.behavior.processes %}
                                                <h4 class="text-info">{{ report.behavior.processes|length }}</h4>
                                                <p>Processes spawned</p>
                                                {% else %}
                                                <h4 class="text-muted">0</h4>
                                                <p>No processes detected</p>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Include all tabs -->
                        {% include 'report_all_tabs.html' %}
                        {% include 'report_additional_tabs.html' %}
                        {% include 'report_final_tabs.html' %}

                        <!-- Raw Data Tab -->
                        <div class="tab-pane fade" id="raw" role="tabpanel">
                            <div class="report-section">
                                <h4><i class="fas fa-code me-2"></i>Raw Report Data</h4>
                                <div class="mb-3">
                                    <button class="btn btn-outline-primary" type="button" data-bs-toggle="collapse" 
                                            data-bs-target="#rawJsonCollapse" aria-expanded="false">
                                        <i class="fas fa-eye me-2"></i>Show/Hide Raw JSON
                                    </button>
                                    <button class="btn btn-outline-secondary ms-2" onclick="downloadJson()">
                                        <i class="fas fa-download me-2"></i>Download JSON
                                    </button>
                                    <button class="btn btn-outline-info ms-2" onclick="copyJson()">
                                        <i class="fas fa-copy me-2"></i>Copy JSON
                                    </button>
                                </div>
                                <div class="collapse" id="rawJsonCollapse">
                                    <div class="json-viewer">
                                        <pre id="rawJsonData">{{ report | tojson(indent=2) }}</pre>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    {% endif %}

                    <!-- Actions -->
                    <div class="text-center mt-4">
                        {% if analysis.status == 'completed' and analysis.id != 'json-upload' %}
                        <a href="{{ url_for('analysis_status', analysis_id=analysis.id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-chart-line me-2"></i>Back to Status
                        </a>
                        {% endif %}
                        <a href="{{ url_for('list_analyses') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-2"></i>All Analyses
                        </a>
                        <a href="{{ url_for('index') }}" class="btn btn-outline-success">
                            <i class="fas fa-upload me-2"></i>Upload Another File
                        </a>
                        <a href="{{ url_for('json_viewer') }}" class="btn btn-outline-info">
                            <i class="fas fa-file-code me-2"></i>JSON Viewer
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Function to copy text to clipboard
function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(function() {
            showToast('Copied to clipboard!', 'success');
        }, function(err) {
            console.error('Could not copy text: ', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        fallbackCopyTextToClipboard(text);
    }
}

function fallbackCopyTextToClipboard(text) {
    var textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
        var successful = document.execCommand('copy');
        if (successful) {
            showToast('Copied to clipboard!', 'success');
        } else {
            showToast('Failed to copy to clipboard', 'error');
        }
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
        showToast('Failed to copy to clipboard', 'error');
    }
    document.body.removeChild(textArea);
}

// Function to show toast notifications
function showToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.innerHTML = message;

    document.body.appendChild(toast);

    setTimeout(() => {
        document.body.removeChild(toast);
    }, 3000);
}

// Add click handlers for hash values
document.addEventListener('DOMContentLoaded', function() {
    const hashElements = document.querySelectorAll('.hash-value');
    hashElements.forEach(function(element) {
        element.style.cursor = 'pointer';
        element.title = 'Click to copy';
        element.addEventListener('click', function() {
            copyToClipboard(this.textContent);
        });
    });

    // String search functionality
    const stringSearch = document.getElementById('stringSearch');
    if (stringSearch) {
        stringSearch.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const stringItems = document.querySelectorAll('.string-item');

            stringItems.forEach(function(item) {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }
});

// Function to download JSON report
function downloadJson() {
    const jsonData = document.getElementById('rawJsonData').textContent;
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '{{ analysis.cape_task_id }}_report.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Function to copy JSON to clipboard
function copyJson() {
    const jsonData = document.getElementById('rawJsonData').textContent;
    copyToClipboard(jsonData);
}

// Auto-refresh functionality for pending analyses
{% if analysis.status in ['pending', 'running', 'processing'] %}
setTimeout(function() {
    location.reload();
}, 30000);
{% endif %}
</script>
{% endblock %}
