# CAPE Web Service

Standalone web service for CAPE malware analysis.

## Features

- Upload files for analysis
- Basic analysis with default options (like cape_simple_service.py)
- Advanced analysis with custom options (like web/submission/views.py)
- Web interface for viewing results
- REST API endpoints

## Installation

```bash
pip install -r requirements.txt
```

## Usage

```bash
python app.py
```

The service will run on http://localhost:5000

## API Endpoints

- `GET /` - Web interface
- `POST /api/analyze` - Submit file for analysis
- `GET /api/status/<task_id>` - Check analysis status
- `GET /api/report/<task_id>` - Get analysis report
- `GET /report/<task_id>` - View report in web interface

## Configuration

Edit `config.py` to configure CAPE server URL and other settings.
