on:
  issues:
    types: [opened, edited]

jobs:
  auto_close_issues:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Automatically close issues that don't follow the issue template
        uses: lucasbento/auto-close-issues@v1.0.2
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          issue-close-message: "@${issue.user.login}: hello! :wave:\n\nThis issue is being automatically closed because it does not follow the issue template.\n\n This is open source project!\n\t So please apreciate our time that we sacrify from other thing that we could enjoy, instead of asking boring things over and over." # optional property
          closed-issues-label: "🙁 Not following issue template"
