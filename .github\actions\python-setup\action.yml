name: 'Python setup steps that can be reused'
description: 'Install dependencies, poetry, requirements'
inputs:
  python-version:
    required: true
    description: The python version

runs:
  using: "composite"
  steps:
    - name: Install dependencies
      if: ${{ runner.os == 'Linux' }}
      shell: bash
      run: |
        sudo apt update && sudo apt-get install -y --no-install-recommends libxml2-dev libxslt-dev python3-dev libgeoip-dev ssdeep libfuzzy-dev 7zip innoextract unrar upx

    - name: Install poetry
      shell: bash
      run: PIP_BREAK_SYSTEM_PACKAGES=1 pip install poetry poetry-plugin-export
    #- name: Python Poetry Action
    #  uses: abatilo/actions-poetry@v3.0.1

    - name: Set up Python ${{ inputs.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ inputs.python-version }}
        cache: 'poetry'

    - name: Install requirements
      shell: bash
      run: |
        PIP_BREAK_SYSTEM_PACKAGES=1 poetry install --no-interaction
