#!/usr/bin/env python3
"""
Test script to verify status flow: pending -> running -> processing -> reported
"""

import requests
import time
import json

BASE_URL = "http://localhost:5000"

def test_status_flow():
    """Test the complete status flow"""
    print("🧪 Testing CAPE Analysis Status Flow")
    print("=" * 50)
    
    # Create a test file
    test_content = "This is a test file for status flow testing\n"
    test_content += "X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*\n"
    
    # Submit analysis
    print("📤 Submitting file for analysis...")
    files = {'file': ('test_status_flow.txt', test_content)}
    data = {'options': 'procmemdump=1,unpacker=2'}
    
    try:
        response = requests.post(f"{BASE_URL}/api/analyze", files=files, data=data)
        if response.status_code != 200:
            print(f"❌ Failed to submit: {response.status_code}")
            return
        
        result = response.json()
        if result.get('error'):
            print(f"❌ Submission error: {result.get('message')}")
            return
        
        analysis_id = result['analysis_id']
        print(f"✅ Submitted successfully! Analysis ID: {analysis_id}")
        
    except Exception as e:
        print(f"❌ Error submitting: {e}")
        return
    
    # Monitor status changes
    print("\n🔍 Monitoring status changes...")
    print("Expected flow: pending -> running -> processing -> reported")
    print("-" * 50)
    
    last_status = None
    status_history = []
    max_checks = 120  # 10 minutes max
    check_count = 0
    
    while check_count < max_checks:
        try:
            response = requests.get(f"{BASE_URL}/api/status/{analysis_id}")
            if response.status_code == 200:
                result = response.json()
                if not result.get('error'):
                    current_status = result['status']
                    
                    if current_status != last_status:
                        timestamp = time.strftime("%H:%M:%S")
                        status_history.append((timestamp, current_status))
                        
                        # Status icons
                        status_icons = {
                            'pending': '⏳',
                            'running': '🔄',
                            'processing': '⚙️',
                            'completed': '✅',
                            'reported': '📄',
                            'failed': '❌'
                        }
                        
                        icon = status_icons.get(current_status, '❓')
                        print(f"{timestamp} - {icon} Status: {current_status}")
                        last_status = current_status
                        
                        # Check if we've reached a final state
                        if current_status in ['reported', 'completed', 'failed']:
                            print(f"\n🎯 Final status reached: {current_status}")
                            break
            
            time.sleep(5)  # Check every 5 seconds
            check_count += 1
            
        except Exception as e:
            print(f"⚠️  Error checking status: {e}")
            time.sleep(5)
            check_count += 1
    
    # Summary
    print("\n📊 Status Flow Summary:")
    print("=" * 30)
    for timestamp, status in status_history:
        print(f"{timestamp} - {status}")
    
    # Validate flow
    print("\n🔍 Flow Validation:")
    statuses = [s[1] for s in status_history]
    
    expected_transitions = [
        ('pending', 'running'),
        ('running', 'processing'),
        ('processing', 'reported'),
        ('processing', 'completed'),
        ('completed', 'reported')
    ]
    
    valid_flow = True
    for i in range(len(statuses) - 1):
        current = statuses[i]
        next_status = statuses[i + 1]
        transition = (current, next_status)
        
        if transition not in expected_transitions:
            print(f"⚠️  Unexpected transition: {current} -> {next_status}")
            valid_flow = False
    
    if valid_flow:
        print("✅ Status flow is valid!")
    else:
        print("❌ Status flow has unexpected transitions")
    
    # Check final result
    if last_status in ['reported', 'completed']:
        print(f"\n📄 Checking report availability...")
        try:
            response = requests.get(f"{BASE_URL}/api/report/{analysis_id}")
            if response.status_code == 200:
                result = response.json()
                if not result.get('error'):
                    print("✅ Report is available!")
                    print(f"   Task ID: {result['result'].get('task_id')}")
                    print(f"   File Hash: {result['result'].get('file_hash')}")
                else:
                    print(f"❌ Report error: {result.get('message')}")
            else:
                print(f"❌ Report not accessible: {response.status_code}")
        except Exception as e:
            print(f"❌ Error accessing report: {e}")
    
    print(f"\n🏁 Test completed! Total checks: {check_count}")

if __name__ == '__main__':
    test_status_flow()
