#!/usr/bin/env python3
"""
Test script specifically for "Task is still being analyzed" scenario
"""

import requests
import time
import json
import sys

BASE_URL = "http://localhost:5000"

def test_still_analyzing():
    """Test the specific 'still being analyzed' scenario"""
    print("🧪 Testing 'Task is still being analyzed' Handling")
    print("=" * 60)
    
    # Create a test file that should trigger longer analysis
    test_content = "This is a test file for 'still being analyzed' scenario\n"
    test_content += "X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*\n"
    # Add more content to make analysis take longer
    test_content += "A" * 50000  # 50KB of padding
    
    # Submit analysis with options that might take longer
    print("📤 Submitting file for analysis...")
    files = {'file': ('test_still_analyzing.exe', test_content)}
    data = {
        'options': 'procmemdump=1,unpacker=2,timeout=600,enforce_timeout=1',  # Long timeout
        'force_reanalyze': 'true'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/analyze", files=files, data=data)
        if response.status_code != 200:
            print(f"❌ Failed to submit: {response.status_code}")
            print(f"Response: {response.text}")
            return
        
        result = response.json()
        if result.get('error'):
            print(f"❌ Submission error: {result.get('message')}")
            return
        
        analysis_id = result['analysis_id']
        print(f"✅ Submitted successfully! Analysis ID: {analysis_id}")
        
    except Exception as e:
        print(f"❌ Error submitting: {e}")
        return
    
    # Monitor the analysis with detailed logging
    print("\n🔍 Monitoring analysis progress...")
    print("Looking for 'processing' status when CAPE returns 'still being analyzed'")
    print("-" * 60)
    
    last_status = None
    status_history = []
    max_checks = 240  # 20 minutes max
    check_count = 0
    processing_detected = False
    still_analyzing_detected = False
    
    while check_count < max_checks:
        try:
            # Check status via API
            response = requests.get(f"{BASE_URL}/api/status/{analysis_id}")
            if response.status_code == 200:
                result = response.json()
                if not result.get('error'):
                    current_status = result['status']
                    
                    if current_status != last_status:
                        timestamp = time.strftime("%H:%M:%S")
                        status_history.append((timestamp, current_status))
                        
                        # Status icons
                        status_icons = {
                            'pending': '⏳',
                            'running': '🔄',
                            'processing': '⚙️',
                            'completed': '✅',
                            'reported': '📄',
                            'failed': '❌'
                        }
                        
                        icon = status_icons.get(current_status, '❓')
                        print(f"{timestamp} - {icon} Status: {current_status}")
                        
                        # Special handling for processing status
                        if current_status == 'processing':
                            processing_detected = True
                            print(f"   🎯 PROCESSING STATUS DETECTED!")
                            print(f"   This means the web service correctly handled 'still being analyzed'")
                            still_analyzing_detected = True
                        
                        last_status = current_status
                        
                        # Check if we've reached a final state
                        if current_status in ['reported', 'completed', 'failed']:
                            print(f"\n🎯 Final status reached: {current_status}")
                            break
                else:
                    print(f"❌ API Error: {result.get('message')}")
            else:
                print(f"❌ HTTP Error {response.status_code}")
            
            time.sleep(5)  # Check every 5 seconds
            check_count += 1
            
            # Show progress for long-running analyses
            if check_count % 12 == 0:  # Every minute
                print(f"   ⏰ Still monitoring... (check #{check_count}/240)")
                if last_status == 'processing':
                    print(f"   ⚙️  Analysis is still processing on CAPE server...")
            
        except Exception as e:
            print(f"⚠️  Error checking status: {e}")
            time.sleep(5)
            check_count += 1
    
    # Detailed analysis of results
    print("\n" + "=" * 60)
    print("📊 DETAILED ANALYSIS RESULTS")
    print("=" * 60)
    
    print("\n📋 Status Flow Summary:")
    for timestamp, status in status_history:
        print(f"  {timestamp} - {status}")
    
    print(f"\n🔍 Key Findings:")
    print(f"  • Total checks performed: {check_count}")
    print(f"  • Processing status detected: {'✅ YES' if processing_detected else '❌ NO'}")
    print(f"  • 'Still analyzing' handled: {'✅ YES' if still_analyzing_detected else '❌ NO'}")
    print(f"  • Final status: {last_status}")
    
    if processing_detected:
        print(f"\n🎉 SUCCESS! The web service correctly handled the scenario:")
        print(f"  ✅ Detected that CAPE server returned 'still being analyzed'")
        print(f"  ✅ Set status to 'processing' instead of 'failed'")
        print(f"  ✅ Allowed user to monitor progress")
        print(f"  ✅ Auto-refresh functionality working")
    else:
        print(f"\n⚠️  ANALYSIS NEEDED:")
        if not status_history:
            print(f"  • No status changes detected - check if analysis started")
        elif len(status_history) == 1:
            print(f"  • Analysis completed too quickly to trigger 'still analyzing'")
            print(f"  • Try with a larger file or more complex analysis options")
        else:
            print(f"  • Processing status not detected - check logic")
    
    # Check final result if available
    if last_status in ['reported', 'completed']:
        print(f"\n📄 Checking final report...")
        try:
            response = requests.get(f"{BASE_URL}/api/report/{analysis_id}")
            if response.status_code == 200:
                result = response.json()
                if not result.get('error'):
                    print("✅ Report is available!")
                    report = result['result'].get('report', {})
                    if report:
                        print(f"   📊 Report summary:")
                        print(f"     • Signatures: {len(report.get('signatures', []))}")
                        print(f"     • Network hosts: {len(report.get('network', {}).get('hosts', []))}")
                        print(f"     • Processes: {len(report.get('behavior', {}).get('processes', []))}")
                        print(f"     • CAPE payloads: {len(report.get('CAPE', {}).get('payloads', []))}")
                    else:
                        print("   ⚠️  Report structure is different than expected")
                else:
                    print(f"❌ Report error: {result.get('message')}")
            else:
                print(f"❌ Report not accessible: {response.status_code}")
        except Exception as e:
            print(f"❌ Error accessing report: {e}")
    
    print(f"\n🏁 Test completed!")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if processing_detected:
        print(f"✅ The system is working correctly!")
        print(f"   • Users will see 'Processing' instead of 'Failed'")
        print(f"   • Auto-refresh will continue until completion")
        print(f"   • No need to re-submit files")
    else:
        print(f"🔧 To trigger the 'still analyzing' scenario:")
        print(f"   • Use larger files (>10MB)")
        print(f"   • Use complex analysis options")
        print(f"   • Set longer timeouts in CAPE")
        print(f"   • Monitor CAPE web interface during analysis")

if __name__ == '__main__':
    test_still_analyzing()
