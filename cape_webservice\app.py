#!/usr/bin/env python3
"""
CAPE Web Service - Standalone Flask Application
"""

import os
import json
import uuid
import time
import threading
from datetime import datetime
from werkzeug.utils import secure_filename
from flask import Flask, request, jsonify, render_template, redirect, url_for, flash, send_from_directory
from flask_cors import CORS

from config import Config
from cape_service import CAPEAnalysisService

app = Flask(__name__)
app.config.from_object(Config)
CORS(app)

# Global storage for analysis results (in production, use database)
analysis_results = {}
analysis_lock = threading.Lock()

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def get_file_info(filepath):
    """Get basic file information"""
    try:
        stat = os.stat(filepath)
        return {
            'size': stat.st_size,
            'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
        }
    except:
        return {'size': 0, 'modified': ''}

def analyze_file_async(analysis_id, filepath, options, machine=None, force_reanalyze=False):
    """Analyze file asynchronously"""
    try:
        # Update status to running
        with analysis_lock:
            analysis_results[analysis_id]['status'] = 'running'
            analysis_results[analysis_id]['started_at'] = datetime.now().isoformat()

        # Create CAPE service instance
        service = CAPEAnalysisService(
            cape_url=app.config['CAPE_URL'],
            timeout=app.config['CAPE_TIMEOUT'],
            max_wait=app.config['CAPE_MAX_WAIT'],
            verbose=True
        )

        # Analyze file
        result = service.analyze_file(filepath, options, machine, force_reanalyze)

        # Update results
        with analysis_lock:
            if result.get('error'):
                # Check if it's a timeout, still processing, or real error
                error_msg = result.get('message', '')
                is_still_processing = (
                    result.get('still_processing') or
                    'still being processed' in error_msg.lower() or
                    'still being analyzed' in error_msg.lower() or
                    'did not complete in time' in error_msg
                )

                if is_still_processing:
                    # Task is still running on CAPE server, keep it as processing
                    analysis_results[analysis_id].update({
                        'status': 'processing',
                        'result': result,
                        'cape_task_id': result.get('task_id'),
                        'file_hash': result.get('file_hash'),
                        'was_cached': result.get('was_cached', False)
                    })
                    print(f"Analysis {analysis_id} set to processing - task {result.get('task_id')} still running on CAPE")
                else:
                    # Real failure
                    analysis_results[analysis_id].update({
                        'status': 'failed',
                        'completed_at': datetime.now().isoformat(),
                        'result': result,
                        'cape_task_id': result.get('task_id'),
                        'file_hash': result.get('file_hash'),
                        'was_cached': result.get('was_cached', False)
                    })
                    print(f"Analysis {analysis_id} failed: {error_msg}")
            else:
                analysis_results[analysis_id].update({
                    'status': 'reported',  # Use 'reported' instead of 'completed'
                    'completed_at': datetime.now().isoformat(),
                    'result': result,
                    'cape_task_id': result.get('task_id'),
                    'file_hash': result.get('file_hash'),
                    'was_cached': result.get('was_cached', False)
                })
                print(f"Analysis {analysis_id} completed successfully")

    except Exception as e:
        with analysis_lock:
            analysis_results[analysis_id].update({
                'status': 'failed',
                'completed_at': datetime.now().isoformat(),
                'result': {'error': True, 'message': str(e)}
            })
    finally:
        # Clean up uploaded file
        try:
            if os.path.exists(filepath):
                os.remove(filepath)
        except:
            pass

@app.route('/')
def index():
    """Main page with upload form"""
    # Get available machines
    try:
        service = CAPEAnalysisService(
            cape_url=app.config['CAPE_URL'],
            timeout=10,
            max_wait=0,
            verbose=False
        )
        machines = service.get_machines()
    except:
        machines = []

    return render_template('index.html',
                         available_options=app.config['AVAILABLE_OPTIONS'],
                         default_options=app.config['DEFAULT_OPTIONS'],
                         machines=machines)

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and start analysis"""
    if 'file' not in request.files:
        flash('No file selected')
        return redirect(request.url)
    
    file = request.files['file']
    if file.filename == '':
        flash('No file selected')
        return redirect(request.url)
    
    if not allowed_file(file.filename):
        flash('File type not allowed')
        return redirect(request.url)
    
    # Save uploaded file
    filename = secure_filename(file.filename)
    analysis_id = str(uuid.uuid4())
    upload_filename = f"{analysis_id}_{filename}"
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], upload_filename)
    file.save(filepath)
    
    # Get analysis options
    analysis_mode = request.form.get('analysis_mode', 'basic')
    options = {}
    
    if analysis_mode == 'advanced':
        # Parse advanced options from form
        for option_key in app.config['AVAILABLE_OPTIONS']:
            if option_key in request.form:
                value = request.form[option_key]
                if value:  # Only add non-empty values
                    options[option_key] = value
    else:
        # Use default options for basic mode
        options = app.config['DEFAULT_OPTIONS'].copy()
    
    # Get other parameters
    machine = request.form.get('machine', 'first_available')
    force_reanalyze = request.form.get('force_reanalyze') == 'on'
    
    # Store analysis info
    file_info = get_file_info(filepath)
    with analysis_lock:
        analysis_results[analysis_id] = {
            'id': analysis_id,
            'filename': filename,
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'started_at': None,
            'completed_at': None,
            'options': options,
            'analysis_mode': analysis_mode,
            'machine': machine,
            'force_reanalyze': force_reanalyze,
            'file_info': file_info,
            'result': None,
            'cape_task_id': None,
            'file_hash': None,
            'was_cached': False
        }
    
    # Start analysis in background thread
    thread = threading.Thread(
        target=analyze_file_async,
        args=(analysis_id, filepath, options, machine, force_reanalyze)
    )
    thread.daemon = True
    thread.start()
    
    flash(f'File uploaded successfully! Analysis ID: {analysis_id}')
    return redirect(url_for('analysis_status', analysis_id=analysis_id))

@app.route('/status/<analysis_id>')
def analysis_status(analysis_id):
    """Show analysis status page"""
    with analysis_lock:
        analysis = analysis_results.get(analysis_id)

    if not analysis:
        flash('Analysis not found')
        return redirect(url_for('index'))

    # If status is processing, try to check if report is now available
    if analysis['status'] == 'processing' and analysis.get('cape_task_id'):
        try:
            service = CAPEAnalysisService(
                cape_url=app.config['CAPE_URL'],
                timeout=10,  # Short timeout for status check
                max_wait=0,  # Don't wait
                verbose=False
            )

            # Check current status
            current_status = service.check_status(analysis['cape_task_id'])
            if current_status == 'reported':
                # Try to get the report
                report = service.get_report(analysis['cape_task_id'])
                if report and not report.get('error'):
                    # Update to completed with report
                    with analysis_lock:
                        analysis_results[analysis_id].update({
                            'status': 'reported',
                            'completed_at': datetime.now().isoformat(),
                            'result': {
                                'error': False,
                                'task_id': analysis['cape_task_id'],
                                'file_hash': analysis.get('file_hash'),
                                'was_cached': False,
                                'report': report
                            }
                        })
                    analysis = analysis_results[analysis_id]  # Get updated analysis
        except Exception as e:
            print(f"Error checking processing status: {e}")

    return render_template('status.html', analysis=analysis)

@app.route('/report/<analysis_id>')
def view_report(analysis_id):
    """View analysis report"""
    with analysis_lock:
        analysis = analysis_results.get(analysis_id)
    
    if not analysis:
        flash('Analysis not found')
        return redirect(url_for('index'))
    
    if analysis['status'] not in ['completed', 'reported'] or not analysis.get('result'):
        flash('Analysis not completed or failed')
        return redirect(url_for('analysis_status', analysis_id=analysis_id))
    
    return render_template('report.html', 
                         analysis=analysis,
                         report=analysis['result'].get('report'))

@app.route('/analyses')
def list_analyses():
    """List all analyses"""
    with analysis_lock:
        analyses = list(analysis_results.values())

    # Sort by creation time, newest first
    analyses.sort(key=lambda x: x['created_at'], reverse=True)

    return render_template('analyses.html', analyses=analyses)

@app.route('/json-viewer')
def json_viewer():
    """JSON report viewer page"""
    return render_template('json_viewer.html')

@app.route('/view-json', methods=['POST'])
def view_json():
    """View JSON report from uploaded file"""
    if 'json_file' not in request.files:
        flash('No JSON file selected')
        return redirect(url_for('json_viewer'))

    file = request.files['json_file']
    if file.filename == '':
        flash('No file selected')
        return redirect(url_for('json_viewer'))

    if not file.filename.lower().endswith('.json'):
        flash('Please upload a JSON file')
        return redirect(url_for('json_viewer'))

    try:
        # Read and parse JSON
        json_content = file.read().decode('utf-8')
        report_data = json.loads(json_content)

        # Create a mock analysis object for the template
        mock_analysis = {
            'id': 'json-upload',
            'filename': file.filename,
            'status': 'completed',
            'created_at': datetime.now().isoformat(),
            'started_at': datetime.now().isoformat(),
            'completed_at': datetime.now().isoformat(),
            'cape_task_id': report_data.get('info', {}).get('id', 'unknown'),
            'file_hash': report_data.get('target', {}).get('file', {}).get('md5', 'unknown'),
            'was_cached': False,
            'analysis_mode': 'json-upload'
        }

        return render_template('report.html', analysis=mock_analysis, report=report_data)

    except json.JSONDecodeError:
        flash('Invalid JSON file format')
        return redirect(url_for('json_viewer'))
    except Exception as e:
        flash(f'Error processing JSON file: {str(e)}')
        return redirect(url_for('json_viewer'))

# API Endpoints

@app.route('/api/analyze', methods=['POST'])
def api_analyze():
    """API endpoint to submit file for analysis"""
    if 'file' not in request.files:
        return jsonify({'error': True, 'message': 'No file provided'})
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': True, 'message': 'No file selected'})
    
    if not allowed_file(file.filename):
        return jsonify({'error': True, 'message': 'File type not allowed'})
    
    # Save uploaded file
    filename = secure_filename(file.filename)
    analysis_id = str(uuid.uuid4())
    upload_filename = f"{analysis_id}_{filename}"
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], upload_filename)
    file.save(filepath)
    
    # Get options from request
    options_str = request.form.get('options', '')
    options = {}
    if options_str:
        try:
            # Parse options string like "procmemdump=1,unpacker=2"
            for option in options_str.split(','):
                if '=' in option:
                    key, value = option.split('=', 1)
                    options[key.strip()] = value.strip()
        except:
            pass
    
    # If no options provided, use defaults
    if not options:
        options = app.config['DEFAULT_OPTIONS'].copy()
    
    machine = request.form.get('machine', 'first_available')
    force_reanalyze = request.form.get('force_reanalyze', '').lower() in ['true', '1', 'yes']
    
    # Store analysis info
    file_info = get_file_info(filepath)
    with analysis_lock:
        analysis_results[analysis_id] = {
            'id': analysis_id,
            'filename': filename,
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'started_at': None,
            'completed_at': None,
            'options': options,
            'analysis_mode': 'api',
            'machine': machine,
            'force_reanalyze': force_reanalyze,
            'file_info': file_info,
            'result': None,
            'cape_task_id': None,
            'file_hash': None,
            'was_cached': False
        }
    
    # Start analysis in background thread
    thread = threading.Thread(
        target=analyze_file_async,
        args=(analysis_id, filepath, options, machine, force_reanalyze)
    )
    thread.daemon = True
    thread.start()
    
    return jsonify({
        'error': False,
        'analysis_id': analysis_id,
        'message': 'File submitted for analysis',
        'status_url': url_for('api_status', analysis_id=analysis_id, _external=True),
        'report_url': url_for('api_report', analysis_id=analysis_id, _external=True)
    })

@app.route('/api/machines')
def api_machines():
    """API endpoint to get available machines"""
    try:
        service = CAPEAnalysisService(
            cape_url=app.config['CAPE_URL'],
            timeout=10,
            max_wait=0,
            verbose=False
        )
        machines = service.get_machines()

        # Format machines for API response
        machine_list = [{'name': 'first_available', 'label': 'First Available'}]
        for machine in machines:
            machine_list.append({
                'name': machine.get('name', ''),
                'label': machine.get('label', machine.get('name', '')),
                'platform': machine.get('platform', ''),
                'status': machine.get('status', ''),
                'locked': machine.get('locked', False)
            })

        return jsonify({
            'error': False,
            'machines': machine_list
        })
    except Exception as e:
        return jsonify({
            'error': True,
            'message': f'Failed to get machines: {str(e)}',
            'machines': [{'name': 'first_available', 'label': 'First Available'}]
        })

@app.route('/api/status/<analysis_id>')
def api_status(analysis_id):
    """API endpoint to check analysis status"""
    with analysis_lock:
        analysis = analysis_results.get(analysis_id)

    if not analysis:
        return jsonify({'error': True, 'message': 'Analysis not found'})

    # If status is processing, try to check if report is now available
    if analysis['status'] == 'processing' and analysis.get('cape_task_id'):
        try:
            service = CAPEAnalysisService(
                cape_url=app.config['CAPE_URL'],
                timeout=10,  # Short timeout for status check
                max_wait=0,  # Don't wait
                verbose=False
            )

            # Check current status
            current_status = service.check_status(analysis['cape_task_id'])
            if current_status == 'reported':
                # Try to get the report
                report = service.get_report(analysis['cape_task_id'])
                if report and not report.get('error'):
                    # Update to completed with report
                    with analysis_lock:
                        analysis_results[analysis_id].update({
                            'status': 'reported',
                            'completed_at': datetime.now().isoformat(),
                            'result': {
                                'error': False,
                                'task_id': analysis['cape_task_id'],
                                'file_hash': analysis.get('file_hash'),
                                'was_cached': False,
                                'report': report
                            }
                        })
                    analysis = analysis_results[analysis_id]  # Get updated analysis
        except Exception as e:
            print(f"Error checking processing status in API: {e}")

    return jsonify({
        'error': False,
        'analysis_id': analysis_id,
        'status': analysis['status'],
        'filename': analysis['filename'],
        'created_at': analysis['created_at'],
        'started_at': analysis['started_at'],
        'completed_at': analysis['completed_at'],
        'cape_task_id': analysis['cape_task_id'],
        'file_hash': analysis['file_hash'],
        'was_cached': analysis['was_cached']
    })

@app.route('/api/report/<analysis_id>')
def api_report(analysis_id):
    """API endpoint to get analysis report"""
    with analysis_lock:
        analysis = analysis_results.get(analysis_id)
    
    if not analysis:
        return jsonify({'error': True, 'message': 'Analysis not found'})
    
    if analysis['status'] not in ['completed', 'reported']:
        return jsonify({'error': True, 'message': 'Analysis not completed'})
    
    return jsonify({
        'error': False,
        'analysis_id': analysis_id,
        'result': analysis['result']
    })

@app.errorhandler(413)
def too_large(e):
    flash('File too large. Maximum size is 500MB.')
    return redirect(url_for('index'))

if __name__ == '__main__':
    print("Starting CAPE Web Service...")
    print(f"CAPE Server: {app.config['CAPE_URL']}")
    print(f"Upload folder: {app.config['UPLOAD_FOLDER']}")
    print("Web interface will be available at: http://localhost:5000")
    print("API endpoints:")
    print("  POST /api/analyze - Submit file for analysis")
    print("  GET /api/status/<analysis_id> - Check status")
    print("  GET /api/report/<analysis_id> - Get report")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
