{% extends "base.html" %}

{% block title %}Upload File - CAPE Web Service{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0">
                    <i class="fas fa-upload me-2"></i>Upload File for Analysis
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('upload_file') }}" enctype="multipart/form-data">
                    <!-- File Upload -->
                    <div class="mb-4">
                        <label for="file" class="form-label">
                            <i class="fas fa-file me-1"></i>Select File
                        </label>
                        <input type="file" class="form-control" id="file" name="file" required>
                        <div class="form-text">
                            Supported formats: exe, dll, pdf, doc, docx, xls, xlsx, ppt, pptx, zip, rar, 7z, apk, jar, bat, cmd, ps1, vbs, js, html, php, py, pl, rb, sh
                        </div>
                    </div>

                    <!-- Analysis Mode -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-cogs me-1"></i>Analysis Mode
                        </label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="analysis_mode" id="basic_mode" value="basic" checked>
                            <label class="form-check-label" for="basic_mode">
                                <strong>Basic Analysis</strong> - Use default options (recommended)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="analysis_mode" id="advanced_mode" value="advanced">
                            <label class="form-check-label" for="advanced_mode">
                                <strong>Advanced Analysis</strong> - Customize options
                            </label>
                        </div>
                    </div>

                    <!-- Default Options Info -->
                    <div id="basic_options_info" class="option-group">
                        <h5><i class="fas fa-info-circle me-2"></i>Default Analysis Options</h5>
                        <div class="row">
                            {% for key, value in default_options.items() %}
                            <div class="col-md-6">
                                <span class="badge bg-secondary me-1">{{ key }}</span>
                                <span class="text-muted">{{ value }}</span>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Advanced Options -->
                    <div id="advanced_options" class="option-group" style="display: none;">
                        <h5><i class="fas fa-sliders-h me-2"></i>Advanced Options</h5>
                        <div class="row">
                            {% for option_key, option_config in available_options.items() %}
                            <div class="col-md-6 mb-3">
                                {% if option_config.type == 'checkbox' %}
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           id="{{ option_key }}" name="{{ option_key }}" value="1"
                                           {% if option_config.default %}checked{% endif %}>
                                    <label class="form-check-label" for="{{ option_key }}">
                                        {{ option_config.name }}
                                    </label>
                                </div>
                                {% elif option_config.type == 'select' %}
                                <label for="{{ option_key }}" class="form-label">{{ option_config.name }}</label>
                                <select class="form-select" id="{{ option_key }}" name="{{ option_key }}">
                                    {% for value, label in option_config.options.items() %}
                                    <option value="{{ value }}" 
                                            {% if value == option_config.default %}selected{% endif %}>
                                        {{ label }}
                                    </option>
                                    {% endfor %}
                                </select>
                                {% endif %}
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Machine Selection -->
                    <div class="mb-4">
                        <h5><i class="fas fa-desktop me-2"></i>Machine Selection</h5>
                        <label for="machine" class="form-label">Analysis Machine</label>
                        <select class="form-select" id="machine" name="machine">
                            <option value="first_available">First Available</option>
                            {% if machines %}
                            {% for machine in machines %}
                            <option value="{{ machine.name }}">
                                {{ machine.label or machine.name }}
                                {% if machine.platform %}({{ machine.platform }}){% endif %}
                                {% if machine.locked %} - LOCKED{% endif %}
                            </option>
                            {% endfor %}
                            {% endif %}
                        </select>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Select a specific machine or use "First Available" to let CAPE choose automatically.
                        </div>
                    </div>

                    <!-- Additional Options -->
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="force_reanalyze" name="force_reanalyze">
                            <label class="form-check-label" for="force_reanalyze">
                                <i class="fas fa-redo me-1"></i>Force Re-analysis (ignore cached results)
                            </label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-play me-2"></i>Start Analysis
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Info Cards -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-shield-virus fa-2x text-primary mb-2"></i>
                        <h6>Malware Detection</h6>
                        <p class="card-text small text-muted">Advanced malware analysis and detection</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-microscope fa-2x text-success mb-2"></i>
                        <h6>Behavioral Analysis</h6>
                        <p class="card-text small text-muted">Dynamic behavior monitoring</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-file-code fa-2x text-info mb-2"></i>
                        <h6>Code Extraction</h6>
                        <p class="card-text small text-muted">Unpacking and code analysis</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const basicMode = document.getElementById('basic_mode');
    const advancedMode = document.getElementById('advanced_mode');
    const basicOptionsInfo = document.getElementById('basic_options_info');
    const advancedOptions = document.getElementById('advanced_options');

    function toggleOptions() {
        if (basicMode.checked) {
            basicOptionsInfo.style.display = 'block';
            advancedOptions.style.display = 'none';
        } else {
            basicOptionsInfo.style.display = 'none';
            advancedOptions.style.display = 'block';
        }
    }

    basicMode.addEventListener('change', toggleOptions);
    advancedMode.addEventListener('change', toggleOptions);

    // Load machines dynamically
    function loadMachines() {
        fetch('/api/machines')
            .then(response => response.json())
            .then(data => {
                if (!data.error && data.machines) {
                    const machineSelect = document.getElementById('machine');
                    machineSelect.innerHTML = '';

                    data.machines.forEach(machine => {
                        const option = document.createElement('option');
                        option.value = machine.name;
                        option.textContent = machine.label;
                        if (machine.platform) {
                            option.textContent += ` (${machine.platform})`;
                        }
                        if (machine.locked) {
                            option.textContent += ' - LOCKED';
                            option.disabled = true;
                        }
                        machineSelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading machines:', error);
            });
    }

    // Initial state
    toggleOptions();
    loadMachines();
});
</script>
{% endblock %}
