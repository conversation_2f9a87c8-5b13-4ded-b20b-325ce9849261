                    <!-- Target File Tab -->
                    {% if report.target %}
                    <div class="tab-pane fade" id="target" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-file me-2"></i>Target File Information</h4>
                            
                            {% if report.target.file %}
                            <div class="mb-4">
                                <h5><i class="fas fa-info-circle me-2"></i>File Details</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <tr><th>File Name</th><td><strong>{{ report.target.file.name }}</strong></td></tr>
                                                <tr><th>File Size</th><td>{{ "{:,}".format(report.target.file.size) }} bytes ({{ "%.2f"|format(report.target.file.size / 1024 / 1024) }} MB)</td></tr>
                                                <tr><th>File Type</th><td>{{ report.target.file.type }}</td></tr>
                                                <tr><th>MIME Type</th><td>{{ report.target.file.mime if report.target.file.mime else 'N/A' }}</td></tr>
                                                <tr><th>CRC32</th><td><code class="hash-value">{{ report.target.file.crc32 if report.target.file.crc32 else 'N/A' }}</code></td></tr>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <tr><th>MD5</th><td><code class="hash-value">{{ report.target.file.md5 }}</code></td></tr>
                                                <tr><th>SHA1</th><td><code class="hash-value">{{ report.target.file.sha1 }}</code></td></tr>
                                                <tr><th>SHA256</th><td><code class="hash-value">{{ report.target.file.sha256 }}</code></td></tr>
                                                <tr><th>SHA512</th><td><code class="hash-value">{{ report.target.file.sha512 if report.target.file.sha512 else 'N/A' }}</code></td></tr>
                                                <tr><th>SSDEEP</th><td><code class="hash-value">{{ report.target.file.ssdeep if report.target.file.ssdeep else 'N/A' }}</code></td></tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}

                            {% if report.target.category %}
                            <div class="mb-4">
                                <h5><i class="fas fa-tag me-2"></i>Category Information</h5>
                                <p><strong>Category:</strong> <span class="badge bg-primary">{{ report.target.category }}</span></p>
                            </div>
                            {% endif %}

                            {% if report.target.url %}
                            <div class="mb-4">
                                <h5><i class="fas fa-link me-2"></i>URL Information</h5>
                                <p><strong>URL:</strong> <a href="{{ report.target.url }}" target="_blank">{{ report.target.url }}</a></p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Behavior Tab -->
                    {% if report.behavior %}
                    <div class="tab-pane fade" id="behavior" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-microscope me-2"></i>Behavior Analysis</h4>
                            
                            <!-- Process Tree -->
                            {% if report.behavior.processes %}
                            <div class="mb-4">
                                <h5><i class="fas fa-sitemap me-2"></i>Process Tree ({{ report.behavior.processes|length }} processes)</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm table-hover">
                                        <thead>
                                            <tr>
                                                <th>PID</th>
                                                <th>Process Name</th>
                                                <th>Command Line</th>
                                                <th>Parent PID</th>
                                                <th>First Seen</th>
                                                <th>Calls</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for process in report.behavior.processes %}
                                            <tr>
                                                <td><code>{{ process.pid }}</code></td>
                                                <td><strong>{{ process.process_name }}</strong></td>
                                                <td><small>{{ process.command_line[:100] if process.command_line else 'N/A' }}{% if process.command_line and process.command_line|length > 100 %}...{% endif %}</small></td>
                                                <td><code>{{ process.ppid if process.ppid else 'N/A' }}</code></td>
                                                <td><small>{{ process.first_seen if process.first_seen else 'N/A' }}</small></td>
                                                <td><span class="badge bg-info">{{ process.calls|length if process.calls else 0 }}</span></td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}

                            <!-- API Calls Summary -->
                            {% if report.behavior.apistats %}
                            <div class="mb-4">
                                <h5><i class="fas fa-code me-2"></i>API Calls Summary ({{ report.behavior.apistats|length }} unique APIs)</h5>
                                <div class="row">
                                    {% for api, count in report.behavior.apistats.items() %}
                                    {% if loop.index <= 50 %}
                                    <div class="col-md-4 col-lg-3 mb-2">
                                        <div class="api-call">
                                            <strong>{{ api }}</strong>
                                            <span class="badge bg-secondary ms-1">{{ count }}</span>
                                        </div>
                                    </div>
                                    {% endif %}
                                    {% endfor %}
                                </div>
                                {% if report.behavior.apistats|length > 50 %}
                                <p class="text-muted">... and {{ report.behavior.apistats|length - 50 }} more API calls</p>
                                {% endif %}
                            </div>
                            {% endif %}

                            <!-- Summary -->
                            {% if report.behavior.summary %}
                            <div class="mb-4">
                                <h5><i class="fas fa-list me-2"></i>Behavior Summary</h5>
                                <div class="row">
                                    {% for key, value in report.behavior.summary.items() %}
                                    <div class="col-md-6 mb-2">
                                        <strong>{{ key.replace('_', ' ').title() }}:</strong> {{ value }}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}

                            <!-- Generic -->
                            {% if report.behavior.generic %}
                            <div class="mb-4">
                                <h5><i class="fas fa-cogs me-2"></i>Generic Behavior</h5>
                                <div class="accordion" id="genericAccordion">
                                    {% for item in report.behavior.generic %}
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="generic{{ loop.index }}">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                    data-bs-target="#genericCollapse{{ loop.index }}">
                                                {{ item.process if item.process else 'Generic Behavior' }} - {{ item.summary if item.summary else 'No summary' }}
                                            </button>
                                        </h2>
                                        <div id="genericCollapse{{ loop.index }}" class="accordion-collapse collapse" 
                                             data-bs-parent="#genericAccordion">
                                            <div class="accordion-body">
                                                <pre class="bg-light p-2"><code>{{ item | tojson(indent=2) }}</code></pre>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Signatures Tab -->
                    {% if report.signatures %}
                    <div class="tab-pane fade" id="signatures" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-exclamation-triangle me-2"></i>Detection Signatures ({{ report.signatures|length }})</h4>
                            
                            <!-- Signature Summary -->
                            <div class="row mb-4">
                                {% set high_sigs = report.signatures | selectattr('severity', 'equalto', 3) | list %}
                                {% set medium_sigs = report.signatures | selectattr('severity', 'equalto', 2) | list %}
                                {% set low_sigs = report.signatures | selectattr('severity', 'equalto', 1) | list %}
                                
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6>High Severity</h6>
                                            <h4 class="text-danger">{{ high_sigs|length }}</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6>Medium Severity</h6>
                                            <h4 class="text-warning">{{ medium_sigs|length }}</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6>Low Severity</h6>
                                            <h4 class="text-info">{{ low_sigs|length }}</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6>Total</h6>
                                            <h4 class="text-primary">{{ report.signatures|length }}</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Signatures List -->
                            <div class="accordion" id="signaturesAccordion">
                                {% for signature in report.signatures %}
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="sig{{ loop.index }}">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                data-bs-target="#collapse{{ loop.index }}">
                                            {% if signature.severity == 3 %}
                                            <span class="badge bg-danger me-2">HIGH</span>
                                            {% elif signature.severity == 2 %}
                                            <span class="badge bg-warning me-2">MEDIUM</span>
                                            {% elif signature.severity == 1 %}
                                            <span class="badge bg-info me-2">LOW</span>
                                            {% else %}
                                            <span class="badge bg-secondary me-2">{{ signature.severity }}</span>
                                            {% endif %}
                                            {{ signature.description }}
                                        </button>
                                    </h2>
                                    <div id="collapse{{ loop.index }}" class="accordion-collapse collapse" 
                                         data-bs-parent="#signaturesAccordion">
                                        <div class="accordion-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p><strong>Name:</strong> {{ signature.name }}</p>
                                                    <p><strong>Severity:</strong> {{ signature.severity }}</p>
                                                    <p><strong>Weight:</strong> {{ signature.weight if signature.weight else 'N/A' }}</p>
                                                    <p><strong>Confidence:</strong> {{ signature.confidence if signature.confidence else 'N/A' }}%</p>
                                                </div>
                                                <div class="col-md-6">
                                                    {% if signature.families %}
                                                    <p><strong>Families:</strong> 
                                                        {% for family in signature.families %}
                                                        <span class="badge bg-danger me-1">{{ family }}</span>
                                                        {% endfor %}
                                                    </p>
                                                    {% endif %}
                                                    {% if signature.categories %}
                                                    <p><strong>Categories:</strong> 
                                                        {% for category in signature.categories %}
                                                        <span class="badge bg-info me-1">{{ category }}</span>
                                                        {% endfor %}
                                                    </p>
                                                    {% endif %}
                                                    {% if signature.references %}
                                                    <p><strong>References:</strong> 
                                                        {% for ref in signature.references %}
                                                        <a href="{{ ref }}" target="_blank" class="badge bg-secondary me-1">{{ ref }}</a>
                                                        {% endfor %}
                                                    </p>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            {% if signature.data %}
                                            <p><strong>Evidence ({{ signature.data|length }} items):</strong></p>
                                            <ul>
                                                {% for item in signature.data %}
                                                <li>
                                                    {% if item.target %}
                                                    {{ item.target }}
                                                    {% else %}
                                                    {{ item }}
                                                    {% endif %}
                                                </li>
                                                {% endfor %}
                                            </ul>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
