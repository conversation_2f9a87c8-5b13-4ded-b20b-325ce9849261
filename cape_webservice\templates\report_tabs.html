                    <!-- Behavior Tab -->
                    {% if report.behavior %}
                    <div class="tab-pane fade" id="behavior" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-microscope me-2"></i>Behavior Analysis</h4>
                            
                            <!-- Process Tree -->
                            {% if report.behavior.processes %}
                            <div class="mb-4">
                                <h5><i class="fas fa-sitemap me-2"></i>Process Tree ({{ report.behavior.processes|length }} processes)</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm table-hover">
                                        <thead>
                                            <tr>
                                                <th>PID</th>
                                                <th>Process Name</th>
                                                <th>Command Line</th>
                                                <th>Parent PID</th>
                                                <th>First Seen</th>
                                                <th>Calls</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for process in report.behavior.processes %}
                                            <tr>
                                                <td><code>{{ process.pid }}</code></td>
                                                <td><strong>{{ process.process_name }}</strong></td>
                                                <td><small>{{ process.command_line[:100] if process.command_line else 'N/A' }}{% if process.command_line and process.command_line|length > 100 %}...{% endif %}</small></td>
                                                <td><code>{{ process.ppid if process.ppid else 'N/A' }}</code></td>
                                                <td><small>{{ process.first_seen if process.first_seen else 'N/A' }}</small></td>
                                                <td><span class="badge bg-info">{{ process.calls|length if process.calls else 0 }}</span></td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}

                            <!-- API Calls Summary -->
                            {% if report.behavior.apistats %}
                            <div class="mb-4">
                                <h5><i class="fas fa-code me-2"></i>API Calls Summary ({{ report.behavior.apistats|length }} unique APIs)</h5>
                                <div class="row">
                                    {% for api, count in report.behavior.apistats.items() %}
                                    {% if loop.index <= 30 %}
                                    <div class="col-md-4 col-lg-3 mb-2">
                                        <div class="api-call">
                                            <strong>{{ api }}</strong>
                                            <span class="badge bg-secondary ms-1">{{ count }}</span>
                                        </div>
                                    </div>
                                    {% endif %}
                                    {% endfor %}
                                </div>
                                {% if report.behavior.apistats|length > 30 %}
                                <p class="text-muted">... and {{ report.behavior.apistats|length - 30 }} more API calls</p>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Signatures Tab -->
                    {% if report.signatures %}
                    <div class="tab-pane fade" id="signatures" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-exclamation-triangle me-2"></i>Detection Signatures ({{ report.signatures|length }})</h4>
                            <div class="accordion" id="signaturesAccordion">
                                {% for signature in report.signatures %}
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="sig{{ loop.index }}">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                data-bs-target="#collapse{{ loop.index }}">
                                            {% if signature.severity == 3 %}
                                            <span class="badge bg-danger me-2">HIGH</span>
                                            {% elif signature.severity == 2 %}
                                            <span class="badge bg-warning me-2">MEDIUM</span>
                                            {% elif signature.severity == 1 %}
                                            <span class="badge bg-info me-2">LOW</span>
                                            {% else %}
                                            <span class="badge bg-secondary me-2">{{ signature.severity }}</span>
                                            {% endif %}
                                            {{ signature.description }}
                                        </button>
                                    </h2>
                                    <div id="collapse{{ loop.index }}" class="accordion-collapse collapse" 
                                         data-bs-parent="#signaturesAccordion">
                                        <div class="accordion-body">
                                            <p><strong>Name:</strong> {{ signature.name }}</p>
                                            <p><strong>Severity:</strong> {{ signature.severity }}</p>
                                            <p><strong>Weight:</strong> {{ signature.weight if signature.weight else 'N/A' }}</p>
                                            <p><strong>Confidence:</strong> {{ signature.confidence if signature.confidence else 'N/A' }}%</p>
                                            {% if signature.families %}
                                            <p><strong>Families:</strong> 
                                                {% for family in signature.families %}
                                                <span class="badge bg-danger me-1">{{ family }}</span>
                                                {% endfor %}
                                            </p>
                                            {% endif %}
                                            {% if signature.categories %}
                                            <p><strong>Categories:</strong> 
                                                {% for category in signature.categories %}
                                                <span class="badge bg-info me-1">{{ category }}</span>
                                                {% endfor %}
                                            </p>
                                            {% endif %}
                                            {% if signature.data %}
                                            <p><strong>Evidence ({{ signature.data|length }} items):</strong></p>
                                            <ul>
                                                {% for item in signature.data %}
                                                <li>
                                                    {% if item.target %}
                                                    {{ item.target }}
                                                    {% else %}
                                                    {{ item }}
                                                    {% endif %}
                                                </li>
                                                {% endfor %}
                                            </ul>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Network Tab -->
                    {% if report.network %}
                    <div class="tab-pane fade" id="network" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-network-wired me-2"></i>Network Activity</h4>
                            
                            <!-- Network Summary -->
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6>Hosts Contacted</h6>
                                            <h4 class="text-info">{{ report.network.hosts|length if report.network.hosts else 0 }}</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6>DNS Requests</h6>
                                            <h4 class="text-warning">{{ report.network.domains|length if report.network.domains else 0 }}</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6>HTTP Requests</h6>
                                            <h4 class="text-success">{{ report.network.http|length if report.network.http else 0 }}</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6>TCP Connections</h6>
                                            <h4 class="text-primary">{{ report.network.tcp|length if report.network.tcp else 0 }}</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Contacted Hosts -->
                            {% if report.network.hosts %}
                            <div class="mb-4">
                                <h5><i class="fas fa-server me-2"></i>Contacted Hosts ({{ report.network.hosts|length }})</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>IP Address</th>
                                                <th>Port</th>
                                                <th>Country</th>
                                                <th>ASN</th>
                                                <th>First Seen</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for host in report.network.hosts %}
                                            <tr>
                                                <td><code class="hash-value">{{ host.ip }}</code></td>
                                                <td>{{ host.port if host.port else 'N/A' }}</td>
                                                <td>{{ host.country_name if host.country_name else 'Unknown' }}</td>
                                                <td>{{ host.asn if host.asn else 'N/A' }}</td>
                                                <td><small>{{ host.first_seen if host.first_seen else 'N/A' }}</small></td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}

                            <!-- DNS Requests -->
                            {% if report.network.domains %}
                            <div class="mb-4">
                                <h5><i class="fas fa-globe me-2"></i>DNS Requests ({{ report.network.domains|length }})</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Domain</th>
                                                <th>IP Address</th>
                                                <th>Type</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for domain in report.network.domains %}
                                            <tr>
                                                <td><strong>{{ domain.domain }}</strong></td>
                                                <td><code class="hash-value">{{ domain.ip if domain.ip else 'N/A' }}</code></td>
                                                <td>{{ domain.type if domain.type else 'A' }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}

                            <!-- HTTP Requests -->
                            {% if report.network.http %}
                            <div class="mb-4">
                                <h5><i class="fas fa-link me-2"></i>HTTP Requests ({{ report.network.http|length }})</h5>
                                <div class="accordion" id="httpAccordion">
                                    {% for http in report.network.http %}
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="http{{ loop.index }}">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                    data-bs-target="#httpCollapse{{ loop.index }}">
                                                <span class="badge bg-info me-2">{{ http.method if http.method else 'GET' }}</span>
                                                {{ http.uri if http.uri else http.url }}
                                            </button>
                                        </h2>
                                        <div id="httpCollapse{{ loop.index }}" class="accordion-collapse collapse" 
                                             data-bs-parent="#httpAccordion">
                                            <div class="accordion-body">
                                                <p><strong>URL:</strong> {{ http.uri if http.uri else http.url }}</p>
                                                <p><strong>Method:</strong> {{ http.method if http.method else 'GET' }}</p>
                                                <p><strong>Host:</strong> {{ http.host if http.host else 'N/A' }}</p>
                                                {% if http.user_agent %}
                                                <p><strong>User Agent:</strong> {{ http.user_agent }}</p>
                                                {% endif %}
                                                {% if http.data %}
                                                <p><strong>POST Data:</strong></p>
                                                <pre class="bg-light p-2"><code>{{ http.data }}</code></pre>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
