name: Update requirements.txt file

on:
  push:
    branches: [ master, staging ]
    paths:
      - "pyproject.toml"
      - "poetry.lock"

jobs:
  update:
    if: ${{ !github.event.act }} # skip during local actions testing
    runs-on: ubuntu-latest
    timeout-minutes: 5
    strategy:
      matrix:
        python-version: ["3.10"]

    steps:
      - name: Check out repository code
        uses: actions/checkout@v4

      - name: Install poetry
        run: pip install poetry poetry-plugin-export --user

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          check-latest: true
          python-version: ${{ matrix.python-version }}
          cache: 'poetry'

      - name: Export requirements.txt
        run: poetry export --format requirements.txt --output requirements.txt

      - name: Commit changes if any
        # Skip this step if being run by nektos/act
        if: ${{ !env.ACT }}
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"
          if output=$(git status --porcelain) && [ ! -z "$output" ]; then
            git pull -f
            git commit -m "ci: Update requirements.txt" -a
            git push
          fi
