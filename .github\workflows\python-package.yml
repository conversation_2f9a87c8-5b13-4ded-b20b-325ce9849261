name: Python package

env:
  COLUMNS: 120

on:
  push:
    branches: [ master, staging ]
  pull_request:
    branches: [ master, staging ]

jobs:
  test:
    runs-on: ubuntu-22.04 # ubuntu-latest
    timeout-minutes: 20
    strategy:
      matrix:
        python-version: ["3.10"]
    steps:
      - name: Check out repository code
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Checkout test files repo
        uses: actions/checkout@v4
        with:
          repository: CAPESandbox/CAPE-TestFiles
          path: tests/data/

      - uses: ./.github/actions/python-setup/
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install pyattck
        run: |
          poetry run pip install git+https://github.com/CAPESandbox/pyattck maco

      - name: Run Ruff
        run: poetry run ruff check . --output-format=github .

      - name: Run unit tests
        run: poetry run python -m pytest --import-mode=append

      # see the mypy configuration in pyproject.toml
      - name: Run mypy
        run: poetry run mypy

  format:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    strategy:
      matrix:
        python-version: ["3.10"]
    if: ${{ github.ref == 'refs/heads/master' }}

    steps:
      - name: Check out repository code
        uses: actions/checkout@v4

      - name: Set up python
        uses: ./.github/actions/python-setup
        with:
          python-version: ${{ matrix.python-version }}

      - name: Commit changes if any
        # Skip this step if being run by nektos/act
        if: ${{ !env.ACT }}
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"
          if output=$(git status --porcelain) && [ ! -z "$output" ]; then
            git pull
            git add .
            git commit -m "style: Automatic code formatting" -a
            git push
          fi
