# Hướng dẫn cấu hình KVM Network cho CAPEv2 với IP ************

## 1. Kiểm tra interface hiện tại

```bash
# Kiểm tra các interface mạng
ip addr show

# Kiểm tra bridge hiện tại
brctl show

# Kiểm tra libvirt networks
virsh net-list --all
```

## 2. Tạo custom bridge network cho dải 172.16.11.x

### Tạo file XML cho network mới:

```bash
sudo nano /tmp/cape-network.xml
```

Nội dung file XML:

```xml
<network>
  <name>cape-network</name>
  <forward mode='nat'>
    <nat>
      <port start='1024' end='65535'/>
    </nat>
  </forward>
  <bridge name='virbr1' stp='on' delay='0'/>
  <ip address='***********' netmask='*************'>
    <dhcp>
      <range start='***********00' end='*************'/>
    </dhcp>
  </ip>
</network>
```

### Tạo và khởi động network:

```bash
# Định nghĩa network
sudo virsh net-define /tmp/cape-network.xml

# Khởi động network
sudo virsh net-start cape-network

# Tự động khởi động cùng hệ thống
sudo virsh net-autostart cape-network

# Kiểm tra
virsh net-list --all
```

## 3. Cấu hình VM để sử dụng network mới

### Chỉnh sửa VM configuration:

```bash
# Liệt kê các VM
virsh list --all

# Chỉnh sửa VM (thay cuckoo1 bằng tên VM của bạn)
sudo virsh edit cuckoo1
```

Tìm phần `<interface>` và thay đổi thành:

```xml
<interface type='network'>
  <mac address='52:54:00:xx:xx:xx'/>
  <source network='cape-network'/>
  <model type='virtio'/>
  <address type='pci' domain='0x0000' bus='0x00' slot='0x03' function='0x0'/>
</interface>
```

## 4. Cấu hình IP tĩnh trong Windows VM

Trong Windows VM, cấu hình IP tĩnh:
- IP: ***********01 (hoặc 102, 103...)
- Subnet mask: *************
- Gateway: ***********
- DNS: *******, *******

## 5. Cấu hình iptables cho routing

```bash
# Cho phép forwarding
echo 1 | sudo tee /proc/sys/net/ipv4/ip_forward

# Thêm vào /etc/sysctl.conf để persistent
echo "net.ipv4.ip_forward=1" | sudo tee -a /etc/sysctl.conf

# Cấu hình iptables (thay eth0 bằng interface thực tế của bạn)
sudo iptables -t nat -A POSTROUTING -s ***********/24 -o eth0 -j MASQUERADE
sudo iptables -A FORWARD -i virbr1 -o eth0 -j ACCEPT
sudo iptables -A FORWARD -i eth0 -o virbr1 -m state --state RELATED,ESTABLISHED -j ACCEPT

# Lưu iptables rules
sudo iptables-save | sudo tee /etc/iptables/rules.v4
```

## 6. Cập nhật file cấu hình CAPEv2

Đã được cấu hình trong các file:
- `conf/cuckoo.conf`: resultserver ip = ************
- `conf/kvm.conf`: ip = ***********01, resultserver_ip = ************
- `conf/routing.conf`: route = internet, internet = eth0

## 7. Kiểm tra kết nối

```bash
# Từ host, ping VM
ping ***********01

# Từ VM, ping host
ping ************

# Từ VM, ping internet
ping *******
```

## 8. Khởi động CAPEv2

```bash
# Khởi động rooter (cần sudo)
sudo python3 utils/rooter.py

# Khởi động CAPE (terminal khác)
python3 cuckoo.py
```

## Lưu ý quan trọng:

1. **Interface name**: Thay `eth0` bằng tên interface thực tế của bạn (kiểm tra bằng `ip addr show`)

2. **Firewall**: Đảm bảo firewall không block các port cần thiết:
   - Port 2042 (result server)
   - Port 8000 (web interface)

3. **SELinux**: Nếu sử dụng SELinux, có thể cần disable hoặc cấu hình thêm

4. **VM Snapshot**: Tạo snapshot VM sau khi cấu hình xong để dễ restore

5. **Network Interface trong KVM config**: Có thể cần thay đổi `interface = virbr1` trong `kvm.conf` nếu sử dụng bridge khác
