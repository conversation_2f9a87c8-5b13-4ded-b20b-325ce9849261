# Bật lại Network Analysis trong CAPEv2

## 🔍 **Kiểm tra trạng thái hiện tại**

### 1. **Kiểm tra file cấu hình:**
```bash
cd /opt/CAPEv2

# Kiểm tra network trong processing.conf
grep -A 10 "\[network\]" conf/processing.conf

# Kiểm tra sniffer trong auxiliary.conf  
grep -A 10 "\[sniffer\]" conf/auxiliary.conf

# Kiểm tra suricata
grep -A 5 "\[suricata\]" conf/processing.conf
```

## 🔧 **Cấu hình để bật lại Network Analysis**

### **File 1: conf/processing.conf**
```ini
[network]
# BẬT LẠI network analysis
enabled = yes
# Sort PCAP files by timestamp
sort_pcap = yes
# DNS whitelisting to ignore domains/IPs
dnswhitelist = yes
dnswhitelist_file = extra/whitelist_domains.txt
# IP whitelisting  
ipwhitelist = yes
ipwhitelist_file = extra/whitelist_ips.txt
# Network passlist
network_passlist = no
network_passlist_file = extra/whitelist_network.txt
# Enable country lookup (requires GeoIP database)
country_lookup = yes

[suricata]
# BẬT LẠI Suricata IDS
enabled = yes
# Suricata binary path
bin = /usr/bin/suricata
# Suricata config file
conf = /etc/suricata/suricata.yaml
# Socket file for communication
socket_file = /tmp/suricata-command.socket

[detections]
enabled = yes
# Enable behavior signatures
behavior = yes
yara = yes
# BẬT LẠI Suricata alerts
suricata = yes
virustotal = yes
clamav = no
```

### **File 2: conf/auxiliary.conf**
```ini
[sniffer]
# BẬT LẠI network packet capture
enabled = yes
# Enable remote tcpdump support
remote = no
# Tcpdump binary path
tcpdump = /usr/bin/tcpdump
# Network interface to monitor
interface = virbr1
# Berkeley packet filter
bpf = not arp

[AzSniffer]
# Disable Azure sniffer
enabled = no
```

### **File 3: conf/cuckoo.conf**
```ini
[processing]
# BẬT LẠI network processing
sort_pcap = yes
# BẬT LẠI DNS resolution
resolve_dns = yes

[resultserver]
# Result server cho network communication
ip = 0.0.0.0
port = 2042
# Increase upload size for network captures
upload_max_size = 256
```

## 🚀 **Script tự động bật lại Network Analysis**

### **File: enable_network.sh**
```bash
#!/bin/bash
# Script để bật lại Network Analysis trong CAPEv2

echo "=== BẬT LẠI NETWORK ANALYSIS TRONG CAPEv2 ==="

CAPE_DIR="/opt/CAPEv2"
CONF_DIR="$CAPE_DIR/conf"

# Kiểm tra thư mục CAPE
if [ ! -d "$CAPE_DIR" ]; then
    echo "❌ Không tìm thấy thư mục CAPEv2 tại $CAPE_DIR"
    exit 1
fi

# Backup configs
echo "1. Backup cấu hình hiện tại..."
mkdir -p $CONF_DIR/backup_$(date +%Y%m%d_%H%M%S)
cp $CONF_DIR/*.conf $CONF_DIR/backup_$(date +%Y%m%d_%H%M%S)/ 2>/dev/null

# Bật network trong processing.conf
echo "2. Bật Network Analysis trong processing.conf..."
if [ -f "$CONF_DIR/processing.conf" ]; then
    # Enable network module
    sed -i '/^\[network\]/,/^\[/ s/enabled = no/enabled = yes/' $CONF_DIR/processing.conf
    sed -i '/^\[network\]/,/^\[/ s/sort_pcap = no/sort_pcap = yes/' $CONF_DIR/processing.conf
    
    # Enable suricata
    sed -i '/^\[suricata\]/,/^\[/ s/enabled = no/enabled = yes/' $CONF_DIR/processing.conf
    
    # Enable suricata trong detections
    sed -i '/^\[detections\]/,/^\[/ s/suricata = no/suricata = yes/' $CONF_DIR/processing.conf
    
    echo "✅ Đã cấu hình processing.conf"
else
    echo "❌ Không tìm thấy processing.conf"
    echo "Tạo từ template..."
    cp $CONF_DIR/default/processing.conf.default $CONF_DIR/processing.conf
fi

# Bật sniffer trong auxiliary.conf
echo "3. Bật Packet Capture trong auxiliary.conf..."
if [ -f "$CONF_DIR/auxiliary.conf" ]; then
    # Enable sniffer
    sed -i '/^\[sniffer\]/,/^\[/ s/enabled = no/enabled = yes/' $CONF_DIR/auxiliary.conf
    
    echo "✅ Đã cấu hình auxiliary.conf"
else
    echo "❌ Không tìm thấy auxiliary.conf"
    echo "Tạo từ template..."
    cp $CONF_DIR/default/auxiliary.conf.default $CONF_DIR/auxiliary.conf
    sed -i '/^\[sniffer\]/,/^\[/ s/enabled = no/enabled = yes/' $CONF_DIR/auxiliary.conf
fi

# Bật network processing trong cuckoo.conf
echo "4. Bật Network Processing trong cuckoo.conf..."
if [ -f "$CONF_DIR/cuckoo.conf" ]; then
    # Enable network processing
    sed -i '/^\[processing\]/,/^\[/ s/sort_pcap = no/sort_pcap = yes/' $CONF_DIR/cuckoo.conf
    sed -i '/^\[processing\]/,/^\[/ s/resolve_dns = no/resolve_dns = yes/' $CONF_DIR/cuckoo.conf
    
    echo "✅ Đã cấu hình cuckoo.conf"
else
    echo "❌ Không tìm thấy cuckoo.conf"
    echo "Tạo từ template..."
    cp $CONF_DIR/default/cuckoo.conf.default $CONF_DIR/cuckoo.conf
fi

# Kiểm tra Suricata
echo "5. Kiểm tra Suricata..."
if command -v suricata &> /dev/null; then
    echo "✅ Suricata đã được cài đặt"
    
    # Test Suricata config
    if sudo suricata -T -c /etc/suricata/suricata.yaml &> /dev/null; then
        echo "✅ Suricata config hợp lệ"
    else
        echo "⚠️  Suricata config có vấn đề, nhưng vẫn tiếp tục..."
    fi
else
    echo "⚠️  Suricata chưa được cài đặt"
    echo "Cài đặt Suricata..."
    sudo apt update
    sudo apt install suricata suricata-update -y
    sudo suricata-update
fi

# Kiểm tra tcpdump
echo "6. Kiểm tra tcpdump..."
if command -v tcpdump &> /dev/null; then
    echo "✅ tcpdump đã được cài đặt"
else
    echo "⚠️  tcpdump chưa được cài đặt"
    sudo apt install tcpdump -y
fi

# Kiểm tra network interface
echo "7. Kiểm tra network interface..."
if ip addr show virbr1 &> /dev/null; then
    echo "✅ Interface virbr1 có sẵn"
elif ip addr show virbr0 &> /dev/null; then
    echo "✅ Interface virbr0 có sẵn"
    echo "Cập nhật config để sử dụng virbr0..."
    sed -i 's/interface = virbr1/interface = virbr0/' $CONF_DIR/auxiliary.conf
else
    echo "⚠️  Không tìm thấy bridge interface"
    echo "Tạo bridge interface..."
    sudo virsh net-start default 2>/dev/null || true
fi

# Restart CAPE services
echo "8. Restart CAPE services..."
sudo systemctl restart cape cape-processor cape-web cape-rooter 2>/dev/null || {
    echo "⚠️  Không thể restart services tự động"
    echo "Vui lòng restart manual:"
    echo "sudo systemctl restart cape cape-processor cape-web"
}

# Verify setup
echo "9. Kiểm tra cấu hình..."
sleep 3

echo ""
echo "=== KIỂM TRA KẾT QUẢ ==="

# Check network config
if grep -q "enabled = yes" $CONF_DIR/processing.conf | grep -A 5 "\[network\]"; then
    echo "✅ Network Analysis: ENABLED"
else
    echo "❌ Network Analysis: DISABLED"
fi

# Check sniffer config
if grep -q "enabled = yes" $CONF_DIR/auxiliary.conf | grep -A 5 "\[sniffer\]"; then
    echo "✅ Packet Capture: ENABLED"
else
    echo "❌ Packet Capture: DISABLED"
fi

# Check suricata config
if grep -q "enabled = yes" $CONF_DIR/processing.conf | grep -A 5 "\[suricata\]"; then
    echo "✅ Suricata IDS: ENABLED"
else
    echo "❌ Suricata IDS: DISABLED"
fi

echo ""
echo "=== HOÀN THÀNH ==="
echo "🎯 Network Analysis đã được bật lại!"
echo ""
echo "📝 Các tính năng đã enable:"
echo "- ✅ Network traffic capture (PCAP)"
echo "- ✅ DNS resolution analysis"
echo "- ✅ HTTP/HTTPS traffic analysis"
echo "- ✅ Suricata IDS alerts"
echo "- ✅ Network connections tracking"
echo "- ✅ Geolocation lookup"
echo ""
echo "🔍 Để kiểm tra:"
echo "1. Submit một sample để analyze"
echo "2. Kiểm tra tab 'Network' trong web interface"
echo "3. Xem PCAP files trong analysis results"
echo ""
echo "📁 Config files đã được backup tại:"
echo "$CONF_DIR/backup_$(date +%Y%m%d)_*/"
```

## ⚡ **Cách sử dụng nhanh:**

```bash
# Tạo và chạy script
cd /opt/CAPEv2
wget -O enable_network.sh https://raw.githubusercontent.com/your-repo/enable_network.sh
chmod +x enable_network.sh
./enable_network.sh
```

## 🔍 **Kiểm tra sau khi enable:**

```bash
# Kiểm tra config
grep -A 5 "\[network\]" conf/processing.conf
grep -A 5 "\[sniffer\]" conf/auxiliary.conf

# Kiểm tra services
sudo systemctl status cape cape-processor cape-web

# Test với sample
# Submit một file để analyze và kiểm tra tab Network
```

## ✅ **Kết quả mong đợi:**
- Network tab xuất hiện trong analysis results
- PCAP files được tạo
- DNS queries được log
- HTTP requests được capture
- Suricata alerts (nếu có)
- Network connections được track
