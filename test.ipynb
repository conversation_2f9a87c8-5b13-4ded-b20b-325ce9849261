{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9d65d87d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> cấp 1:\n", "- statistics\n", "- target\n", "- CAPE\n", "- info\n", "- capa_summary\n", "- behavior\n", "- debug\n", "- memory\n", "- network\n", "- procmon\n", "- url_analysis\n", "- procmemory\n", "- signatures\n", "- malscore\n", "- ttps\n", "- malstatus\n", "- mitre_attck\n", "- shots\n"]}], "source": ["import json\n", "\n", "# Đọc file JSON\n", "with open('20_report.json', 'r', encoding='utf-8') as f:\n", "    data = json.load(f)\n", "\n", "# <PERSON><PERSON><PERSON> thị các trường cấp 1 (top-level keys)\n", "print(\"<PERSON><PERSON><PERSON> trường cấp 1:\")\n", "for key in data.keys():\n", "    print(\"-\", key)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}