#!/usr/bin/env python3
"""
Script tổng hợp để khắc phục hoàn toàn vấn đề system process hooking trong CAPEv2
Bao gồm: cấu hình files, patch analyzer, tạo VM setup scripts, và test scripts

Sử dụng: python3 fix_all_system_process_hooking.py
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header(text):
    """In header với format đẹp"""
    print("\n" + "=" * 60)
    print(f" {text}")
    print("=" * 60)

def print_step(step_num, text):
    """In step với format đẹp"""
    print(f"\n[STEP {step_num}] {text}")
    print("-" * 50)

def find_cape_path():
    """Tìm đường dẫn CAPEv2"""
    possible_paths = [
        'f:/CAPEv2',  # Current Windows path
        'C:/CAPEv2',
        '/opt/CAPEv2',
        '/mnt/CAPEv2',
        '/data/CAPEv2',
        '/srv/CAPEv2'
    ]
    
    for path in possible_paths:
        if os.path.exists(path) and os.path.exists(os.path.join(path, 'cuckoo.py')):
            return path
    
    # Sử dụng thư mục hiện tại nếu có cuckoo.py
    current_dir = os.getcwd()
    if os.path.exists(os.path.join(current_dir, 'cuckoo.py')):
        return current_dir
    
    return None

def run_script(script_name, description):
    """Chạy script Python và trả về kết quả"""
    print(f"🔧 Running {description}...")
    
    if not os.path.exists(script_name):
        print(f"❌ Script not found: {script_name}")
        return False
    
    try:
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            if result.stdout:
                print("Output:", result.stdout[-500:])  # Last 500 chars
            return True
        else:
            print(f"❌ {description} failed")
            if result.stderr:
                print("Error:", result.stderr[-500:])
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} timed out")
        return False
    except Exception as e:
        print(f"❌ Error running {description}: {e}")
        return False

def create_master_vm_setup_script(cape_path):
    """Tạo script setup VM tổng hợp"""
    script_path = os.path.join(cape_path, 'master_vm_setup.bat')
    
    script_content = '''@echo off
REM Master VM Setup Script for CAPEv2 System Process Hooking
REM This script combines all necessary VM configurations
REM Run as Administrator in Windows VM

echo ================================================================
echo CAPEv2 Master VM Setup for System Process Hooking
echo Targeting: dllhost.exe, svchost.exe, explorer.exe
echo ================================================================
echo.

echo [INFO] Checking Administrator privileges...
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] This script must be run as Administrator!
    echo Please right-click and "Run as Administrator"
    pause
    exit /b 1
)
echo [OK] Running as Administrator
echo.

echo [STEP 1] Completely disabling Windows Defender...
powershell -Command "Set-MpPreference -DisableRealtimeMonitoring $true" 2>nul
powershell -Command "Set-MpPreference -DisableBehaviorMonitoring $true" 2>nul
powershell -Command "Set-MpPreference -DisableBlockAtFirstSeen $true" 2>nul
powershell -Command "Set-MpPreference -DisableIOAVProtection $true" 2>nul
powershell -Command "Set-MpPreference -DisablePrivacyMode $true" 2>nul
powershell -Command "Set-MpPreference -DisableIntrusionPreventionSystem $true" 2>nul
powershell -Command "Set-MpPreference -DisableScriptScanning $true" 2>nul
powershell -Command "Set-MpPreference -MAPSReporting 0" 2>nul

REM Stop Windows Defender services
sc stop WinDefend 2>nul
sc config WinDefend start= disabled 2>nul
sc stop WdNisSvc 2>nul
sc config WdNisSvc start= disabled 2>nul
sc stop Sense 2>nul
sc config Sense start= disabled 2>nul
echo [OK] Windows Defender disabled

echo [STEP 2] Disabling security mitigations...
reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel" /v MitigationOptions /t REG_BINARY /d 000000000000000000000000000000000000000000000000 /f 2>nul
reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel" /v MitigationAuditOptions /t REG_BINARY /d 000000000000000000000000000000000000000000000000 /f 2>nul

REM Disable DEP for system processes
bcdedit /set nx OptIn 2>nul
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\AppCompatFlags\\Layers" /v "C:\\Windows\\System32\\dllhost.exe" /t REG_SZ /d "DisableNXShowUI" /f 2>nul
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\AppCompatFlags\\Layers" /v "C:\\Windows\\System32\\svchost.exe" /t REG_SZ /d "DisableNXShowUI" /f 2>nul

REM Disable ASLR
reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management" /v MoveImages /t REG_DWORD /d 0 /f 2>nul
echo [OK] Security mitigations disabled

echo [STEP 3] Disabling UAC completely...
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v EnableLUA /t REG_DWORD /d 0 /f 2>nul
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v ConsentPromptBehaviorAdmin /t REG_DWORD /d 0 /f 2>nul
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v ConsentPromptBehaviorUser /t REG_DWORD /d 0 /f 2>nul
echo [OK] UAC disabled

echo [STEP 4] Enabling debug privileges...
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v LocalAccountTokenFilterPolicy /t REG_DWORD /d 1 /f 2>nul
reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Lsa" /v LimitBlankPasswordUse /t REG_DWORD /d 0 /f 2>nul
echo [OK] Debug privileges enabled

echo [STEP 5] Disabling Windows Error Reporting...
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\Windows Error Reporting" /v Disabled /t REG_DWORD /d 1 /f 2>nul
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\Windows Error Reporting" /v DontSendAdditionalData /t REG_DWORD /d 1 /f 2>nul
echo [OK] Error reporting disabled

echo [STEP 6] Configuring COM+ for dllhost.exe injection...
reg add "HKLM\\SOFTWARE\\Classes\\AppID" /v "DllSurrogate" /t REG_SZ /d "" /f 2>nul
reg add "HKLM\\SOFTWARE\\Classes\\AppID" /v "DllSurrogateExecutable" /t REG_SZ /d "dllhost.exe" /f 2>nul
echo [OK] COM+ configured

echo [STEP 7] Configuring CAPEv2 Agent service...
sc query CAPEv2Agent >nul 2>&1
if %errorLevel% equ 0 (
    sc config CAPEv2Agent start= auto 2>nul
    sc config CAPEv2Agent obj= LocalSystem 2>nul
    sc config CAPEv2Agent type= interact 2>nul
    echo [OK] CAPEv2Agent configured
) else (
    echo [WARNING] CAPEv2Agent service not found - install agent first
)

echo [STEP 8] Additional security configurations...
reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Lsa" /v RestrictAnonymous /t REG_DWORD /d 0 /f 2>nul
reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Lsa" /v RestrictAnonymousSAM /t REG_DWORD /d 0 /f 2>nul
echo [OK] Additional configurations applied

echo.
echo ================================================================
echo SETUP COMPLETED SUCCESSFULLY!
echo ================================================================
echo.
echo IMPORTANT NOTES:
echo - VM must be REBOOTED for all changes to take effect
echo - After reboot, verify system processes are accessible
echo - Test injection with the provided test scripts
echo - Only use this VM for malware analysis in isolated environment
echo.
echo System processes that should be hookable after reboot:
echo - dllhost.exe (COM+ Application Host)
echo - svchost.exe (Service Host Process) 
echo - explorer.exe (Windows Shell)
echo - services.exe (Service Control Manager)
echo.
echo Press any key to continue...
pause
'''
    
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    print(f"✅ Created master VM setup script: {script_path}")
    return True

def create_verification_script(cape_path):
    """Tạo script verification tổng hợp"""
    script_path = os.path.join(cape_path, 'verify_system_process_hooking.py')
    
    script_content = '''#!/usr/bin/env python3
"""
Verification script for system process hooking setup
Kiểm tra tất cả các thành phần đã được cấu hình đúng
"""

import os
import sys
import subprocess

def check_file_exists(filepath, description):
    """Kiểm tra file tồn tại"""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} - NOT FOUND")
        return False

def check_analyzer_patch():
    """Kiểm tra analyzer.py đã được patch"""
    analyzer_path = "analyzer/windows/analyzer.py"
    if os.path.exists(analyzer_path):
        with open(analyzer_path, 'r') as f:
            content = f.read()
        
        if 'SystemProcessHooker' in content or 'SYSTEM_PROCESS_HOOK_ENHANCEMENT' in content:
            print("✅ Analyzer.py has been patched for system process hooking")
            return True
        else:
            print("❌ Analyzer.py has NOT been patched")
            return False
    else:
        print("❌ Analyzer.py not found")
        return False

def main():
    print("=" * 60)
    print("CAPEv2 System Process Hooking Verification")
    print("=" * 60)
    
    checks_passed = 0
    total_checks = 0
    
    # Check configuration files
    config_files = [
        ("conf/analyzer.conf", "Analyzer configuration"),
        ("conf/system_processes.conf", "System processes configuration"),
        ("conf/processing.conf", "Processing configuration"),
        ("conf/auxiliary.conf", "Auxiliary configuration"),
    ]
    
    print("\\n[1] Checking configuration files...")
    for filepath, description in config_files:
        total_checks += 1
        if check_file_exists(filepath, description):
            checks_passed += 1
    
    # Check scripts
    script_files = [
        ("fix_dllhost_svchost_hooking.py", "System process hooking fix script"),
        ("master_vm_setup.bat", "VM setup script"),
        ("test_system_processes.bat", "Windows VM test script"),
    ]
    
    print("\\n[2] Checking scripts...")
    for filepath, description in script_files:
        total_checks += 1
        if check_file_exists(filepath, description):
            checks_passed += 1
    
    # Check analyzer patch
    print("\\n[3] Checking analyzer patch...")
    total_checks += 1
    if check_analyzer_patch():
        checks_passed += 1
    
    # Summary
    print("\\n" + "=" * 60)
    print(f"VERIFICATION SUMMARY: {checks_passed}/{total_checks} checks passed")
    print("=" * 60)
    
    if checks_passed == total_checks:
        print("🎉 ALL CHECKS PASSED!")
        print("\\nYour CAPEv2 setup is ready for system process hooking.")
        print("\\nNext steps:")
        print("1. Copy master_vm_setup.bat to your Windows VM")
        print("2. Run it as Administrator in the VM")
        print("3. Reboot the VM")
        print("4. Run test_system_processes.bat in the VM to verify")
        print("5. Start analyzing malware samples")
    else:
        print(f"⚠️  {total_checks - checks_passed} checks failed.")
        print("\\nPlease run the setup scripts again:")
        print("1. python3 configure_process_hooking.py")
        print("2. python3 fix_dllhost_svchost_hooking.py")
    
    return 0 if checks_passed == total_checks else 1

if __name__ == "__main__":
    sys.exit(main())
'''
    
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    print(f"✅ Created verification script: {script_path}")
    return True

def main():
    """Main function"""
    print_header("CAPEv2 System Process Hooking - Complete Fix")
    print("Khắc phục hoàn toàn vấn đề hook dllhost.exe, svchost.exe, explorer.exe")
    
    # Tìm CAPEv2 path
    cape_path = find_cape_path()
    if not cape_path:
        print("❌ CAPEv2 installation not found!")
        print("Please run this script from CAPEv2 directory")
        return 1
    
    print(f"🔍 Found CAPEv2 at: {cape_path}")
    
    # Change to CAPEv2 directory
    os.chdir(cape_path)
    
    success_count = 0
    total_steps = 5
    
    # Step 1: Configure files
    print_step(1, "Configuring process hooking files")
    if run_script("configure_process_hooking.py", "Configuration setup"):
        success_count += 1
    
    # Step 2: Patch analyzer
    print_step(2, "Patching analyzer for system process hooking")
    if run_script("fix_dllhost_svchost_hooking.py", "Analyzer patching"):
        success_count += 1
    
    # Step 3: Create VM setup scripts
    print_step(3, "Creating VM setup scripts")
    if create_master_vm_setup_script(cape_path):
        success_count += 1
    
    # Step 4: Create verification script
    print_step(4, "Creating verification script")
    if create_verification_script(cape_path):
        success_count += 1
    
    # Step 5: Run verification
    print_step(5, "Running verification")
    if run_script("verify_system_process_hooking.py", "Setup verification"):
        success_count += 1
    
    # Final summary
    print_header(f"SETUP COMPLETED: {success_count}/{total_steps} steps successful")
    
    if success_count == total_steps:
        print("🎉 COMPLETE SUCCESS!")
        print("\nAll components have been set up successfully:")
        print("✅ Configuration files created/updated")
        print("✅ Analyzer patched for system process hooking")
        print("✅ VM setup scripts created")
        print("✅ Verification scripts created")
        print("✅ Setup verification passed")
        
        print("\n📋 FINAL STEPS:")
        print("1. Copy 'master_vm_setup.bat' to your Windows VM")
        print("2. Run it as Administrator in the VM")
        print("3. Reboot the VM")
        print("4. Run 'test_system_processes.bat' in VM to verify")
        print("5. Start analyzing malware - system processes should now be hookable!")
        
        print("\n⚠️  IMPORTANT:")
        print("- Only use the configured VM for malware analysis")
        print("- VM security has been significantly reduced")
        print("- Keep VM isolated from production networks")
        
    else:
        print(f"⚠️  PARTIAL SUCCESS: {success_count}/{total_steps}")
        print("Some steps failed. Please check the errors above and retry.")
        print("\nYou can run individual scripts manually:")
        print("- python3 configure_process_hooking.py")
        print("- python3 fix_dllhost_svchost_hooking.py")
        print("- python3 verify_system_process_hooking.py")
    
    return 0 if success_count == total_steps else 1

if __name__ == "__main__":
    sys.exit(main())
