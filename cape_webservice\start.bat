@echo off
echo ========================================
echo CAPE Web Service Startup
echo ========================================

echo Checking Python installation...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    pause
    exit /b 1
)

echo.
echo Installing dependencies...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Checking CAPE server connectivity...
python check_cape.py
if %errorlevel% neq 0 (
    echo WARNING: CAPE server check failed
    echo You may need to configure the CAPE_URL in config.py
    echo.
)

echo.
echo Starting CAPE Web Service...
echo Web interface will be available at: http://localhost:5000
echo Press Ctrl+C to stop the service
echo.
python run.py

pause
