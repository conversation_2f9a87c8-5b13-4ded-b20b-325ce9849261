#!/usr/bin/env python3
"""
Debug script to test CAPE API directly and understand the "still being analyzed" response
"""

import requests
import json
import time
import sys

# CAPE server configuration
CAPE_URL = "http://*************:8000"  # Update this to your CAPE server

def test_cape_api_directly():
    """Test CAPE API directly to understand responses"""
    print("🔍 CAPE API Direct Testing")
    print("=" * 50)
    print(f"CAPE Server: {CAPE_URL}")
    
    # Test 1: Check CAPE server connectivity
    print("\n1️⃣ Testing CAPE server connectivity...")
    try:
        response = requests.get(f"{CAPE_URL}/apiv2/cuckoo/status/", timeout=10)
        if response.status_code == 200:
            print("✅ CAPE server is accessible")
            data = response.json()
            print(f"   Server status: {data}")
        else:
            print(f"❌ CAPE server returned HTTP {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Cannot connect to CAPE server: {e}")
        return
    
    # Test 2: Submit a test file
    print("\n2️⃣ Submitting test file...")
    test_content = "X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*"
    files = {'file': ('test_debug.exe', test_content)}
    data = {'timeout': 300}
    
    try:
        response = requests.post(f"{CAPE_URL}/apiv2/tasks/create/file/", files=files, data=data)
        if response.status_code == 200:
            result = response.json()
            if not result.get('error'):
                task_id = result['data']['task_ids'][0]
                print(f"✅ File submitted successfully! Task ID: {task_id}")
            else:
                print(f"❌ Submission error: {result}")
                return
        else:
            print(f"❌ Submission failed with HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return
    except Exception as e:
        print(f"❌ Error submitting file: {e}")
        return
    
    # Test 3: Monitor task status
    print(f"\n3️⃣ Monitoring task {task_id} status...")
    for i in range(60):  # Monitor for 5 minutes
        try:
            # Check status
            status_response = requests.get(f"{CAPE_URL}/apiv2/tasks/view/{task_id}/")
            if status_response.status_code == 200:
                status_data = status_response.json()
                if not status_data.get('error') and status_data.get('data'):
                    status = status_data['data'].get('status')
                    print(f"   Status: {status}")
                    
                    if status == 'reported':
                        print("✅ Task completed! Now testing report retrieval...")
                        break
                else:
                    print(f"   Status check error: {status_data}")
            else:
                print(f"   Status check failed: HTTP {status_response.status_code}")
            
            time.sleep(5)
        except Exception as e:
            print(f"   Status check error: {e}")
            time.sleep(5)
    
    # Test 4: Try to get report (this is where "still being analyzed" might appear)
    print(f"\n4️⃣ Testing report retrieval...")
    
    # Try different report formats
    formats = ['json', 'lite', 'htmlsummary']
    
    for format_type in formats:
        print(f"\n   Testing {format_type} format...")
        try:
            report_url = f"{CAPE_URL}/apiv2/tasks/get/report/{task_id}/{format_type}/"
            print(f"   URL: {report_url}")
            
            report_response = requests.get(report_url)
            print(f"   HTTP Status: {report_response.status_code}")
            
            if report_response.status_code == 200:
                if format_type in ['json', 'lite']:
                    try:
                        report_data = report_response.json()
                        print(f"   Response type: JSON")
                        
                        # Check for error in response
                        if report_data.get('error'):
                            error_value = report_data.get('error_value', '')
                            print(f"   ❗ Error in response: {error_value}")
                            
                            if 'still being analyzed' in str(error_value).lower():
                                print(f"   🎯 FOUND IT! 'Still being analyzed' detected!")
                                print(f"   Full response: {json.dumps(report_data, indent=2)}")
                            else:
                                print(f"   Different error: {error_value}")
                        else:
                            print(f"   ✅ Report available! Keys: {list(report_data.keys())}")
                            
                    except json.JSONDecodeError:
                        print(f"   ❌ Invalid JSON response")
                        print(f"   Raw response: {report_response.text[:200]}...")
                else:
                    print(f"   ✅ HTML report available (length: {len(report_response.text)})")
            else:
                print(f"   ❌ HTTP {report_response.status_code}")
                try:
                    error_data = report_response.json()
                    print(f"   Error response: {error_data}")
                    
                    if error_data.get('error') and 'still being analyzed' in str(error_data.get('error_value', '')).lower():
                        print(f"   🎯 FOUND IT! 'Still being analyzed' in HTTP error response!")
                        
                except:
                    print(f"   Raw error response: {report_response.text}")
                    
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    # Test 5: Wait and try again
    print(f"\n5️⃣ Waiting 30 seconds and trying again...")
    time.sleep(30)
    
    try:
        report_url = f"{CAPE_URL}/apiv2/tasks/get/report/{task_id}/json/"
        report_response = requests.get(report_url)
        print(f"   Second attempt - HTTP Status: {report_response.status_code}")
        
        if report_response.status_code == 200:
            report_data = report_response.json()
            if report_data.get('error'):
                error_value = report_data.get('error_value', '')
                print(f"   Still getting error: {error_value}")
                if 'still being analyzed' in str(error_value).lower():
                    print(f"   🎯 'Still being analyzed' persists!")
            else:
                print(f"   ✅ Report now available!")
        else:
            print(f"   Still getting HTTP {report_response.status_code}")
            
    except Exception as e:
        print(f"   Exception on retry: {e}")
    
    print(f"\n🏁 Debug test completed!")
    print(f"\n💡 Key findings:")
    print(f"   • Task ID: {task_id}")
    print(f"   • Check CAPE web interface at: {CAPE_URL}/analysis/{task_id}/")
    print(f"   • If you see 'still being analyzed', the issue is confirmed")

def test_specific_task(task_id):
    """Test a specific task ID"""
    print(f"🔍 Testing specific task: {task_id}")
    print("=" * 50)
    
    # Check status
    try:
        status_response = requests.get(f"{CAPE_URL}/apiv2/tasks/view/{task_id}/")
        if status_response.status_code == 200:
            status_data = status_response.json()
            print(f"Status response: {json.dumps(status_data, indent=2)}")
        else:
            print(f"Status check failed: HTTP {status_response.status_code}")
    except Exception as e:
        print(f"Status check error: {e}")
    
    # Try to get report
    try:
        report_response = requests.get(f"{CAPE_URL}/apiv2/tasks/get/report/{task_id}/json/")
        print(f"Report HTTP Status: {report_response.status_code}")
        
        if report_response.status_code == 200:
            report_data = report_response.json()
            print(f"Report response: {json.dumps(report_data, indent=2)}")
        else:
            print(f"Report response: {report_response.text}")
            
    except Exception as e:
        print(f"Report check error: {e}")

if __name__ == '__main__':
    if len(sys.argv) > 1:
        # Test specific task ID
        task_id = sys.argv[1]
        test_specific_task(task_id)
    else:
        # Run full test
        test_cape_api_directly()
