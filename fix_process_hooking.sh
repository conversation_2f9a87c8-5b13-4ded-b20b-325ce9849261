#!/bin/bash

# Script khắc phục vấn đề hook processes trong CAPEv2
# Sử dụng: ./fix_process_hooking.sh [cape_path]

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

function print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

function print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

function print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

function print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Auto-detect CAPE path
CAPE_PATH="$1"
if [ -z "$CAPE_PATH" ]; then
    POSSIBLE_PATHS=(
        "/opt/CAPEv2"
        "/mnt/CAPEv2"
        "/mnt/data/*/CAPEv2"
        "/data/CAPEv2"
        "/srv/CAPEv2"
    )
    
    for path_pattern in "${POSSIBLE_PATHS[@]}"; do
        for path in $path_pattern; do
            if [ -d "$path" ] && [ -f "$path/cuckoo.py" ]; then
                CAPE_PATH="$path"
                break 2
            fi
        done
    done
fi

if [ -z "$CAPE_PATH" ] || [ ! -d "$CAPE_PATH" ]; then
    print_error "Không tìm thấy CAPEv2. Sử dụng: $0 [cape_path]"
    exit 1
fi

print_info "=== KHẮC PHỤC VẤN ĐỀ PROCESS HOOKING ==="
print_info "CAPE Path: $CAPE_PATH"

print_step "1. Kiểm tra cấu hình analyzer"

ANALYZER_CONF="$CAPE_PATH/conf/analyzer.conf"
if [ -f "$ANALYZER_CONF" ]; then
    print_info "Kiểm tra analyzer.conf..."
    
    # Kiểm tra process injection settings
    if grep -q "inject_processes" "$ANALYZER_CONF"; then
        print_info "✅ inject_processes setting found"
    else
        print_warning "⚠️  inject_processes setting not found, adding..."
        echo "" >> "$ANALYZER_CONF"
        echo "# Process injection settings" >> "$ANALYZER_CONF"
        echo "inject_processes = yes" >> "$ANALYZER_CONF"
    fi
    
    # Kiểm tra protected processes
    if grep -q "protected_processes" "$ANALYZER_CONF"; then
        print_info "✅ protected_processes setting found"
    else
        print_warning "⚠️  protected_processes setting not found, adding..."
        echo "protected_processes = no" >> "$ANALYZER_CONF"
    fi
else
    print_error "❌ analyzer.conf not found"
fi

print_step "2. Cấu hình auxiliary modules"

# Kiểm tra human.py module
HUMAN_PY="$CAPE_PATH/analyzer/windows/modules/auxiliary/human.py"
if [ -f "$HUMAN_PY" ]; then
    print_info "✅ human.py module exists"
    
    # Kiểm tra có enable process injection không
    if grep -q "inject.*process" "$HUMAN_PY"; then
        print_info "✅ Process injection code found in human.py"
    else
        print_warning "⚠️  Process injection may need enhancement"
    fi
else
    print_error "❌ human.py module not found"
fi

print_step "3. Tạo script cải thiện process injection"

cat > "$CAPE_PATH/improve_injection.py" << 'EOF'
#!/usr/bin/env python3
"""
Script cải thiện process injection cho CAPEv2
Khắc phục vấn đề không hook được dllhost.exe, svchost.exe, explorer.exe
"""
import os
import sys
import json
import subprocess
import shutil
from pathlib import Path

def get_cape_path():
    """Auto-detect CAPE path"""
    possible_paths = [
        '/opt/CAPEv2',
        '/mnt/CAPEv2',
        '/data/CAPEv2',
        '/srv/CAPEv2',
        'f:/CAPEv2',  # Windows path
        'C:/CAPEv2'   # Windows path
    ]

    # Add dynamic paths
    import glob
    possible_paths.extend(glob.glob('/mnt/data/*/CAPEv2'))

    for path in possible_paths:
        if os.path.exists(path) and os.path.exists(os.path.join(path, 'cuckoo.py')):
            return path
    return None

def update_analyzer_config(cape_path):
    """Update analyzer configuration for better system process injection"""
    config_path = os.path.join(cape_path, 'analyzer', 'windows', 'analyzer.py')

    if not os.path.exists(config_path):
        print("❌ analyzer.py not found")
        return False

    print("📝 Updating analyzer configuration for system process injection...")

    # Backup original
    backup_path = config_path + '.backup'
    if not os.path.exists(backup_path):
        shutil.copy2(config_path, backup_path)
        print(f"✅ Backup created: {backup_path}")

    # Read current content
    with open(config_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Check if already patched
    if 'SYSTEM_PROCESS_INJECTION_ENHANCED' in content:
        print("✅ System process injection enhancement already applied")
        return True

    # Find PROTECTED_NAMES section and modify it
    protected_names_start = content.find('PROTECTED_NAMES = [')
    if protected_names_start == -1:
        print("❌ PROTECTED_NAMES not found in analyzer.py")
        return False

    # Find the end of PROTECTED_NAMES list
    protected_names_end = content.find(']', protected_names_start)
    if protected_names_end == -1:
        print("❌ Could not find end of PROTECTED_NAMES list")
        return False

    # Extract current protected names
    protected_section = content[protected_names_start:protected_names_end + 1]

    # Add comment to mark our enhancement
    enhancement_marker = '''
# SYSTEM_PROCESS_INJECTION_ENHANCED - Enhanced system process handling
# Note: dllhost.exe, svchost.exe, explorer.exe are NOT in PROTECTED_NAMES
# This allows injection attempts but requires special handling
'''

    # Insert enhancement marker before PROTECTED_NAMES
    content = content[:protected_names_start] + enhancement_marker + content[protected_names_start:]

    # Add enhanced injection methods after the Files class
    files_class_end = content.find('class ProcessList:')
    if files_class_end == -1:
        files_class_end = content.find('def pid_from_service_name')

    if files_class_end != -1:
        enhanced_injection_code = '''

class SystemProcessInjector:
    """Enhanced injection handler for system processes"""

    SYSTEM_PROCESSES = {
        "dllhost.exe": {"priority": "high", "method": "apc"},
        "svchost.exe": {"priority": "high", "method": "manual"},
        "explorer.exe": {"priority": "medium", "method": "standard"},
        "winlogon.exe": {"priority": "low", "method": "skip"},
        "csrss.exe": {"priority": "low", "method": "skip"},
        "lsass.exe": {"priority": "low", "method": "skip"},
        "services.exe": {"priority": "medium", "method": "manual"},
        "smss.exe": {"priority": "low", "method": "skip"},
        "wininit.exe": {"priority": "low", "method": "skip"}
    }

    @classmethod
    def should_inject_system_process(cls, process_name):
        """Check if we should attempt injection into system process"""
        proc_info = cls.SYSTEM_PROCESSES.get(process_name.lower(), {})
        return proc_info.get("method", "skip") != "skip"

    @classmethod
    def get_injection_method(cls, process_name):
        """Get recommended injection method for system process"""
        proc_info = cls.SYSTEM_PROCESSES.get(process_name.lower(), {})
        return proc_info.get("method", "standard")

    @classmethod
    def inject_system_process_with_retry(cls, proc, filepath, retries=3):
        """Enhanced injection with retry for system processes"""
        filename = os.path.basename(filepath).lower()
        method = cls.get_injection_method(filename)

        log.info("Attempting system process injection: %s (method: %s)", filename, method)

        for attempt in range(retries):
            try:
                if method == "apc":
                    # Use APC injection for dllhost.exe
                    success = proc.inject(interest=filepath, nosleepskip=True)
                elif method == "manual":
                    # Manual DLL injection for svchost.exe and services.exe
                    success = proc.inject(interest=filepath, nosleepskip=True)
                else:
                    # Standard injection
                    success = proc.inject(interest=filepath, nosleepskip=True)

                if success:
                    log.info("✅ Successfully injected into %s (attempt %d)", filename, attempt + 1)
                    return True

            except Exception as e:
                log.warning("System process injection attempt %d failed for %s: %s",
                           attempt + 1, filename, str(e))

            if attempt < retries - 1:
                import time
                time.sleep(0.5)  # Brief wait before retry

        log.error("❌ All system process injection attempts failed for %s", filename)
        return False

'''

        content = content[:files_class_end] + enhanced_injection_code + content[files_class_end:]

    # Modify the _inject_process method to use enhanced injection
    inject_process_start = content.find('def _inject_process(self, process_id, thread_id, mode):')
    if inject_process_start != -1:
        # Find the injection call in _inject_process
        inject_call_pattern = 'proc.inject(interest=filepath, nosleepskip=True)'
        inject_call_pos = content.find(inject_call_pattern, inject_process_start)

        if inject_call_pos != -1:
            # Replace with enhanced injection
            enhanced_inject_call = '''# Enhanced injection for system processes
            filename = os.path.basename(filepath)
            if SystemProcessInjector.should_inject_system_process(filename):
                SystemProcessInjector.inject_system_process_with_retry(proc, filepath)
            else:
                proc.inject(interest=filepath, nosleepskip=True)'''

            content = content[:inject_call_pos] + enhanced_inject_call + content[inject_call_pos + len(inject_call_pattern):]

    # Write the modified content
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(content)

    print("✅ Enhanced system process injection code added to analyzer.py")
    return True

def create_vm_setup_script(cape_path):
    """Create enhanced script to setup VM for system process hooking"""
    script_path = os.path.join(cape_path, 'setup_vm_for_hooking.bat')

    script_content = '''@echo off
REM Enhanced VM setup script for CAPEv2 system process hooking
REM Run this inside the Windows VM as Administrator
REM Specifically targets dllhost.exe, svchost.exe, explorer.exe hooking issues

echo === Enhanced CAPEv2 VM Setup for System Process Hooking ===

REM Disable Windows Defender completely
echo [+] Completely disabling Windows Defender...
powershell -Command "Set-MpPreference -DisableRealtimeMonitoring $true"
powershell -Command "Set-MpPreference -DisableBehaviorMonitoring $true"
powershell -Command "Set-MpPreference -DisableBlockAtFirstSeen $true"
powershell -Command "Set-MpPreference -DisableIOAVProtection $true"
powershell -Command "Set-MpPreference -DisablePrivacyMode $true"
powershell -Command "Set-MpPreference -SignatureDisableUpdateOnStartupWithoutEngine $true"
powershell -Command "Set-MpPreference -DisableArchiveScanning $true"
powershell -Command "Set-MpPreference -DisableIntrusionPreventionSystem $true"
powershell -Command "Set-MpPreference -DisableScriptScanning $true"
powershell -Command "Set-MpPreference -SubmitSamplesConsent 2"
powershell -Command "Set-MpPreference -MAPSReporting 0"

REM Disable Windows Defender service completely
echo [+] Stopping Windows Defender services...
sc stop WinDefend
sc config WinDefend start= disabled
sc stop WdNisSvc
sc config WdNisSvc start= disabled
sc stop Sense
sc config Sense start= disabled

REM Disable Control Flow Guard and other mitigations
echo [+] Disabling Control Flow Guard and exploit mitigations...
reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel" /v MitigationOptions /t REG_BINARY /d 000000000000000000000000000000000000000000000000 /f
reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel" /v MitigationAuditOptions /t REG_BINARY /d 000000000000000000000000000000000000000000000000 /f

REM Disable DEP for system processes
echo [+] Configuring DEP settings for system processes...
bcdedit /set nx OptIn
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\AppCompatFlags\\Layers" /v "C:\\Windows\\System32\\dllhost.exe" /t REG_SZ /d "DisableNXShowUI" /f
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\AppCompatFlags\\Layers" /v "C:\\Windows\\System32\\svchost.exe" /t REG_SZ /d "DisableNXShowUI" /f

REM Disable ASLR
echo [+] Disabling ASLR...
reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management" /v MoveImages /t REG_DWORD /d 0 /f

REM Disable UAC completely
echo [+] Disabling UAC...
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v EnableLUA /t REG_DWORD /d 0 /f
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v ConsentPromptBehaviorAdmin /t REG_DWORD /d 0 /f
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v ConsentPromptBehaviorUser /t REG_DWORD /d 0 /f

REM Enable debug privileges and process access
echo [+] Enabling debug privileges and process access...
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v LocalAccountTokenFilterPolicy /t REG_DWORD /d 1 /f
reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Lsa" /v LimitBlankPasswordUse /t REG_DWORD /d 0 /f

REM Disable Windows Error Reporting
echo [+] Disabling Windows Error Reporting...
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\Windows Error Reporting" /v Disabled /t REG_DWORD /d 1 /f
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\Windows Error Reporting" /v DontSendAdditionalData /t REG_DWORD /d 1 /f

REM Configure system process security
echo [+] Configuring system process security for injection...
reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\SubSystems" /v Optional /t REG_MULTI_SZ /d "Posix" /f

REM Disable process protection for COM+ applications (dllhost.exe)
echo [+] Disabling COM+ process protection...
reg add "HKLM\\SOFTWARE\\Classes\\AppID" /v "DllSurrogate" /t REG_SZ /d "" /f
reg add "HKLM\\SOFTWARE\\Classes\\AppID" /v "DllSurrogateExecutable" /t REG_SZ /d "dllhost.exe" /f

REM Set agent to run as SYSTEM with enhanced privileges
echo [+] Configuring CAPEv2 agent service...
sc config CAPEv2Agent start= auto
sc config CAPEv2Agent obj= LocalSystem
sc config CAPEv2Agent type= interact

REM Disable additional security features
echo [+] Disabling additional security features...
reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Lsa" /v RestrictAnonymous /t REG_DWORD /d 0 /f
reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Lsa" /v RestrictAnonymousSAM /t REG_DWORD /d 0 /f

echo === Setup completed! ===
echo IMPORTANT: Please reboot the VM for all changes to take effect
echo After reboot, test with: tasklist /svc to verify system processes
echo Press any key to continue...
pause
'''
    
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    print(f"✅ VM setup script created: {script_path}")
    print("📋 Copy this script to your Windows VM and run as Administrator")

def create_injection_test_script(cape_path):
    """Create enhanced script to test system process injection"""
    script_path = os.path.join(cape_path, 'test_injection.py')

    script_content = '''#!/usr/bin/env python3
"""
Enhanced test script for system process injection
Tests specifically for dllhost.exe, svchost.exe, explorer.exe hooking
"""
import os
import sys
import psutil
import subprocess
import ctypes
from ctypes import wintypes

def check_debug_privileges():
    """Check if current process has debug privileges"""
    try:
        # Try to enable SeDebugPrivilege
        import win32security
        import win32api

        # Get current process token
        token = win32security.OpenProcessToken(
            win32api.GetCurrentProcess(),
            win32security.TOKEN_ADJUST_PRIVILEGES | win32security.TOKEN_QUERY
        )

        # Get the LUID for SeDebugPrivilege
        privilege = win32security.LookupPrivilegeValue(None, "SeDebugPrivilege")

        # Enable the privilege
        win32security.AdjustTokenPrivileges(
            token, 0, [(privilege, win32security.SE_PRIVILEGE_ENABLED)]
        )

        print("✅ Debug privileges enabled")
        return True
    except Exception as e:
        print(f"❌ Failed to enable debug privileges: {e}")
        return False

def test_process_access(pid, proc_name):
    """Test if we can access a process for injection"""
    try:
        # Try to open process with required access rights
        PROCESS_ALL_ACCESS = 0x1F0FFF
        kernel32 = ctypes.windll.kernel32

        handle = kernel32.OpenProcess(PROCESS_ALL_ACCESS, False, pid)
        if handle:
            kernel32.CloseHandle(handle)
            return True, "Full access granted"
        else:
            return False, f"OpenProcess failed: {ctypes.GetLastError()}"

    except Exception as e:
        return False, f"Exception: {e}"

def test_system_process_injection():
    """Test injection capabilities for system processes"""
    print("=== Enhanced System Process Injection Test ===")
    print("Testing specifically for dllhost.exe, svchost.exe, explorer.exe")
    print()

    # Check if running as administrator
    try:
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        print(f"Running as Administrator: {'Yes' if is_admin else 'No'}")
    except:
        print("Could not determine admin status")

    # Check debug privileges
    has_debug = check_debug_privileges()
    print()

    # Target system processes with their characteristics
    target_processes = {
        "explorer.exe": {"critical": True, "expected_count": 1, "description": "Windows Shell"},
        "dllhost.exe": {"critical": False, "expected_count": "multiple", "description": "COM+ Application Host"},
        "svchost.exe": {"critical": True, "expected_count": "multiple", "description": "Service Host Process"},
        "winlogon.exe": {"critical": True, "expected_count": 1, "description": "Windows Logon Process"},
        "services.exe": {"critical": True, "expected_count": 1, "description": "Service Control Manager"}
    }

    results = {}

    for proc_name, info in target_processes.items():
        print(f"\\n[+] Testing {proc_name} ({info['description']})...")

        # Find processes
        pids = []
        for proc in psutil.process_iter(['pid', 'name', 'username', 'status']):
            try:
                if proc.info['name'].lower() == proc_name.lower():
                    pids.append({
                        'pid': proc.info['pid'],
                        'username': proc.info.get('username', 'Unknown'),
                        'status': proc.info.get('status', 'Unknown')
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        if not pids:
            print(f"  ❌ {proc_name} not found")
            results[proc_name] = {"status": "not_found", "accessible": 0, "total": 0}
            continue

        print(f"  Found {len(pids)} instances")
        accessible_count = 0

        # Test each instance (limit to first 3 for svchost.exe)
        test_pids = pids[:3] if proc_name == "svchost.exe" else pids

        for proc_info in test_pids:
            pid = proc_info['pid']
            username = proc_info['username']
            status = proc_info['status']

            print(f"    Testing PID {pid} (User: {username}, Status: {status})")

            # Test process access
            can_access, reason = test_process_access(pid, proc_name)

            if can_access:
                print(f"      ✅ Accessible - {reason}")
                accessible_count += 1
            else:
                print(f"      ❌ Not accessible - {reason}")

        results[proc_name] = {
            "status": "found",
            "accessible": accessible_count,
            "total": len(test_pids),
            "critical": info["critical"]
        }

    # Summary
    print("\\n" + "="*50)
    print("INJECTION TEST SUMMARY")
    print("="*50)

    total_accessible = 0
    total_critical_accessible = 0

    for proc_name, result in results.items():
        if result["status"] == "found":
            accessible = result["accessible"]
            total = result["total"]
            critical = result["critical"]

            status_icon = "✅" if accessible > 0 else "❌"
            critical_text = " (CRITICAL)" if critical else ""

            print(f"{status_icon} {proc_name}: {accessible}/{total} accessible{critical_text}")

            total_accessible += accessible
            if critical and accessible > 0:
                total_critical_accessible += 1
        else:
            print(f"❌ {proc_name}: Not found")

    print(f"\\nTotal accessible processes: {total_accessible}")
    print(f"Critical processes accessible: {total_critical_accessible}")

    if total_critical_accessible >= 2:
        print("\\n🎉 GOOD: Multiple critical system processes are accessible for injection")
    elif total_critical_accessible >= 1:
        print("\\n⚠️  PARTIAL: Some critical system processes are accessible")
    else:
        print("\\n❌ POOR: No critical system processes are accessible")
        print("   Recommendations:")
        print("   - Run as Administrator")
        print("   - Disable Windows Defender")
        print("   - Run the VM setup script")
        print("   - Ensure CAPEv2 agent runs as SYSTEM")

if __name__ == "__main__":
    test_system_process_injection()
'''
    
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    os.chmod(script_path, 0o755)
    print(f"✅ Injection test script created: {script_path}")

def main():
    cape_path = get_cape_path()
    if not cape_path:
        print("❌ CAPEv2 installation not found")
        return 1
    
    print(f"🔍 Found CAPEv2 at: {cape_path}")
    
    # Update analyzer config
    update_analyzer_config(cape_path)
    
    # Create VM setup script
    create_vm_setup_script(cape_path)
    
    # Create injection test script
    create_injection_test_script(cape_path)
    
    print("\n=== NEXT STEPS ===")
    print("1. Copy setup_vm_for_hooking.bat to your Windows VM")
    print("2. Run it as Administrator in the VM")
    print("3. Reboot the VM")
    print("4. Test injection with: python3 test_injection.py")
    print("5. Run a new analysis to test improved hooking")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
EOF

chmod +x "$CAPE_PATH/improve_injection.py"

print_step "4. Tạo cấu hình tối ưu cho VM"

cat > "$CAPE_PATH/vm_optimization_guide.md" << 'EOF'
# VM Optimization Guide for Better Process Hooking

## Windows VM Settings

### 1. Disable Security Features
```batch
# Run as Administrator in VM
powershell -Command "Set-MpPreference -DisableRealtimeMonitoring $true"
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\kernel" /v MitigationOptions /t REG_BINARY /d 000000000000000000000000000000000000000000000000 /f
bcdedit /set nx OptIn
```

### 2. Agent Configuration
- Run agent as SYSTEM user
- Disable Windows Defender completely
- Add monitor.dll to AV exceptions

### 3. Process Injection Settings
```ini
[analyzer]
inject_processes = yes
protected_processes = no
injection_timeout = 30
retry_injection = yes
```

## Common Issues and Solutions

### dllhost.exe not hooked
- Usually COM+ application host
- Requires SYSTEM privileges
- May need manual DLL injection

### svchost.exe not hooked  
- Windows service host
- Protected by Windows
- Try injection with different flags

### explorer.exe not hooked
- Windows shell process
- Critical system process
- May require special handling
EOF

print_step "5. Tạo script khắc phục cụ thể cho system processes"

cat > "$CAPE_PATH/fix_system_process_hooking.py" << 'EOF'
#!/usr/bin/env python3
"""
Script khắc phục cụ thể vấn đề hook system processes trong CAPEv2
Tập trung vào dllhost.exe, svchost.exe, explorer.exe
"""
import os
import sys
import shutil
import re

def patch_process_injection():
    """Patch process injection logic in analyzer.py"""
    cape_path = os.path.dirname(os.path.abspath(__file__))
    analyzer_path = os.path.join(cape_path, 'analyzer', 'windows', 'analyzer.py')

    if not os.path.exists(analyzer_path):
        print("❌ analyzer.py not found")
        return False

    print("🔧 Patching process injection logic...")

    # Backup
    backup_path = analyzer_path + '.original'
    if not os.path.exists(backup_path):
        shutil.copy2(analyzer_path, backup_path)
        print(f"✅ Backup created: {backup_path}")

    with open(analyzer_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Patch 1: Modify PROTECTED_NAMES to exclude system processes we want to hook
    if 'SYSTEM_HOOK_PATCH_APPLIED' not in content:
        # Add our patch marker
        protected_names_pattern = r'(PROTECTED_NAMES = \[.*?\])'

        def add_patch_marker(match):
            return f'# SYSTEM_HOOK_PATCH_APPLIED - Allow hooking of system processes\n    {match.group(1)}'

        content = re.sub(protected_names_pattern, add_patch_marker, content, flags=re.DOTALL)

        # Patch 2: Enhance _inject_process method
        inject_method_pattern = r'(def _inject_process\(self, process_id, thread_id, mode\):.*?proc\.inject\(interest=filepath, nosleepskip=True\))'

        enhanced_inject = '''def _inject_process(self, process_id, thread_id, mode):
        """Helper function for injecting the monitor into a process with enhanced system process support."""
        # We acquire the process lock in order to prevent the analyzer to
        # terminate the analysis while we are operating on the new process.
        self.analyzer.process_lock.acquire()

        # Set the current DLL to the default one provided at submission.
        # dll = self.analyzer.default_dll

        if process_id in (self.analyzer.pid, self.analyzer.ppid):
            if process_id not in self.ignore_list["pid"]:
                log.warning("Received request to inject Cuckoo processes, skipping it")
                self.ignore_list["pid"].append(process_id)
            self.analyzer.process_lock.release()
            return

        # We inject the process only if it's not being monitored already,
        # otherwise we would generated polluted logs (if it wouldn't crash
        # horribly to start with).
        if self.analyzer.process_list.has_pid(process_id):
            # If it's already in our monitored list, check if it's in the
            # list of tracked pids.
            if not self.analyzer.process_list.has_pid(process_id, notrack=False):
                log.debug(
                    "Received request to inject pid=%d. It was already on our notrack list, moving it to the track list", process_id
                )

                self.analyzer.process_list.remove_pid(process_id)
            else:
                log.debug("Received request to inject pid=%d, but it's already being monitored", process_id)
                self.analyzer.process_lock.release()
                return

        # Open the process and inject the monitor
        proc = Process(pid=process_id, thread_id=thread_id)

        filepath = proc.get_filepath()
        filename = os.path.basename(filepath)

        if not self.analyzer.files.is_protected_filename(filename):
            # Add the new process ID to the list of monitored processes.
            self.analyzer.process_list.add_pid(process_id)

            # We're done operating on the processes list,
            # release the lock
            self.analyzer.process_lock.release()

            # Enhanced injection for system processes
            system_processes = ["dllhost.exe", "svchost.exe", "explorer.exe", "services.exe"]
            is_system_process = filename.lower() in [p.lower() for p in system_processes]

            if is_system_process:
                log.info("Attempting enhanced injection into system process: %s (PID: %d)", filename, process_id)

                # Try multiple injection attempts for system processes
                injection_success = False
                for attempt in range(3):
                    try:
                        if attempt == 0:
                            # Standard injection
                            injection_success = proc.inject(interest=filepath, nosleepskip=True)
                        elif attempt == 1:
                            # Retry with slight delay
                            import time
                            time.sleep(0.1)
                            injection_success = proc.inject(interest=filepath, nosleepskip=True)
                        else:
                            # Final attempt with different approach
                            injection_success = proc.inject(interest=filepath, nosleepskip=True)

                        if injection_success:
                            log.info("✅ Successfully injected into system process %s on attempt %d", filename, attempt + 1)
                            break
                    except Exception as e:
                        log.warning("System process injection attempt %d failed for %s: %s", attempt + 1, filename, str(e))

                if not injection_success:
                    log.error("❌ All injection attempts failed for system process %s", filename)
            else:
                # Standard injection for non-system processes
                proc.inject(interest=filepath, nosleepskip=True)

            self.analyzer.LASTINJECT_TIME = timeit.default_timer()
            log.info("Injected into process with pid %s and name %s", proc.pid, filename)'''

        if 'Enhanced injection for system processes' not in content:
            content = re.sub(inject_method_pattern, enhanced_inject, content, flags=re.DOTALL)

        # Write patched content
        with open(analyzer_path, 'w', encoding='utf-8') as f:
            f.write(content)

        print("✅ Process injection logic patched successfully")
        return True
    else:
        print("✅ System hook patch already applied")
        return True

def create_system_process_config():
    """Create configuration for system process handling"""
    cape_path = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(cape_path, 'conf', 'system_processes.conf')

    config_content = '''# System Process Hooking Configuration
# Configuration for enhanced system process injection

[system_processes]
# Enable enhanced system process injection
enabled = yes

# Processes to attempt injection (despite being system processes)
target_processes = dllhost.exe,svchost.exe,explorer.exe,services.exe

# Injection retry attempts for system processes
retry_attempts = 3

# Delay between retry attempts (seconds)
retry_delay = 0.1

# Enable verbose logging for system process injection
verbose_logging = yes

[dllhost]
# COM+ Application Host process
priority = high
method = standard
max_instances = 10

[svchost]
# Service Host process
priority = high
method = standard
max_instances = 20

[explorer]
# Windows Shell process
priority = medium
method = standard
max_instances = 1

[services]
# Service Control Manager
priority = medium
method = standard
max_instances = 1
'''

    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    with open(config_path, 'w') as f:
        f.write(config_content)

    print(f"✅ System process configuration created: {config_path}")

if __name__ == "__main__":
    print("=== CAPEv2 System Process Hooking Fix ===")
    print("Applying patches for dllhost.exe, svchost.exe, explorer.exe hooking")
    print()

    success = patch_process_injection()
    create_system_process_config()

    if success:
        print("\n🎉 System process hooking patches applied successfully!")
        print("\nNext steps:")
        print("1. Copy setup_vm_for_hooking.bat to your Windows VM")
        print("2. Run it as Administrator in the VM")
        print("3. Reboot the VM")
        print("4. Test with: python3 test_injection.py")
        print("5. Run a malware analysis to verify improved hooking")
    else:
        print("\n❌ Failed to apply patches")
        return 1

    return 0
EOF

chmod +x "$CAPE_PATH/fix_system_process_hooking.py"

print_info "=== HOÀN TẤT ==="
print_info ""
print_info "📋 CÁC SCRIPT ĐÃ TẠO:"
print_info "  - $CAPE_PATH/improve_injection.py (Cải thiện injection tổng quát)"
print_info "  - $CAPE_PATH/fix_system_process_hooking.py (Khắc phục cụ thể system processes)"
print_info "  - $CAPE_PATH/setup_vm_for_hooking.bat (Setup VM)"
print_info "  - $CAPE_PATH/test_injection.py (Test injection)"
print_info "  - $CAPE_PATH/vm_optimization_guide.md (Hướng dẫn)"
print_info ""
print_info "🚀 BƯỚC TIẾP THEO (KHUYẾN NGHỊ):"
print_info "1. Chạy script khắc phục cụ thể: cd $CAPE_PATH && python3 fix_system_process_hooking.py"
print_info "2. Copy setup_vm_for_hooking.bat vào Windows VM"
print_info "3. Chạy script trong VM với quyền Administrator"
print_info "4. Reboot VM"
print_info "5. Test với: python3 test_injection.py"
print_info "6. Chạy analysis để kiểm tra"
print_info ""
print_warning "⚠️  LƯU Ý QUAN TRỌNG:"
print_warning "- Script này sửa đổi analyzer.py - đã tạo backup"
print_warning "- VM phải disable hoàn toàn Windows Defender"
print_warning "- Agent phải chạy với SYSTEM privileges"
print_warning "- dllhost.exe và svchost.exe có thể vẫn khó hook do Windows protection"
print_warning "- Chỉ sử dụng trong môi trường isolated analysis"
