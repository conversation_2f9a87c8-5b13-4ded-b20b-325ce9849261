# Hướng dẫn cài đặt CAPEv2 từ đầu - CHI TIẾT

## Tổng quan
Tài liệu này hướng dẫn từng bước cụ thể để cài đặt CAPEv2 từ clone project về đến khi chạy được hoàn chỉnh trên Ubuntu/Debian.

## BƯỚC 1: <PERSON><PERSON><PERSON> bị hệ thống

### 1.1. Update hệ thống
```bash
# Update package list
sudo apt update && sudo apt upgrade -y

# Cài đặt các package cơ bản
sudo apt install -y git curl wget vim nano htop tree
```

### 1.2. Cài đặt Python và dependencies
```bash
# Cài Python 3.8+ và pip
sudo apt install -y python3 python3-pip python3-dev python3-venv

# Kiểm tra version Python
python3 --version

# Cài đặt build tools
sudo apt install -y build-essential libssl-dev libffi-dev libjpeg-dev zlib1g-dev liblcms2-dev libfreetype6-dev libtiff5-dev tk-dev tcl-dev
```

### 1.3. Cài đặt database
```bash
# Cài PostgreSQL (khuyến nghị)
sudo apt install -y postgresql postgresql-contrib libpq-dev

# Hoặc cài MongoDB (tùy chọn)
sudo apt install -y mongodb

# Start và enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

## BƯỚC 2: Clone và setup CAPEv2

### 2.1. Clone repository
```bash
# Clone CAPEv2 từ GitHub
cd /opt
sudo git clone https://github.com/kevoreilly/CAPEv2.git

# Chuyển ownership cho user hiện tại
sudo chown -R $USER:$USER /opt/CAPEv2

# Chuyển vào thư mục CAPEv2
cd /opt/CAPEv2
```

### 2.2. Kiểm tra cấu trúc thư mục
```bash
# Xem cấu trúc thư mục chính
ls -la /opt/CAPEv2

# Các thư mục quan trọng:
# - analyzer/     : Agent chạy trong VM
# - conf/         : File cấu hình
# - data/         : Database và storage
# - lib/          : Core libraries
# - modules/      : Processing modules
# - utils/        : Utility scripts
# - web/          : Web interface
```

## BƯỚC 3: Cài đặt dependencies

### 3.1. Cài đặt system dependencies
```bash
# Cài đặt các package system cần thiết
sudo apt install -y libjpeg8-dev zlib1g-dev python3-dev libffi-dev build-essential python3-setuptools libjpeg-dev zlib1g-dev libfreetype6-dev liblcms2-dev libwebp-dev tcl8.6-dev tk8.6-dev python3-tk libharfbuzz-dev libfribidi-dev libxcb1-dev

# Cài đặt tcpdump cho network capture
sudo apt install -y tcpdump apparmor-utils
sudo aa-disable /usr/sbin/tcpdump

# Cài đặt volatility dependencies
sudo apt install -y python3-distorm3

# Cài đặt ssdeep
sudo apt install -y libfuzzy-dev ssdeep

# Cài đặt YARA
sudo apt install -y libyara-dev yara
```

### 3.2. Cài đặt Python dependencies
```bash
# Chuyển vào thư mục CAPEv2
cd /opt/CAPEv2

# Upgrade pip
python3 -m pip install --upgrade pip

# Cài đặt requirements
pip3 install -r requirements.txt

# Nếu có lỗi, cài từng package quan trọng:
pip3 install psycopg2-binary
pip3 install pillow
pip3 install yara-python
pip3 install pefile
pip3 install pydeep
pip3 install volatility3
```

## BƯỚC 4: Cấu hình Database

### 4.1. Setup PostgreSQL
```bash
# Chuyển sang user postgres
sudo -u postgres psql

# Trong PostgreSQL prompt:
CREATE DATABASE cape;
CREATE USER cape WITH PASSWORD 'cape_password';
GRANT ALL PRIVILEGES ON DATABASE cape TO cape;
\q

# Test kết nối database
psql -h localhost -U cape -d cape -W
# Nhập password: cape_password
# \q để thoát
```

### 4.2. Cấu hình database trong CAPEv2
```bash
# Copy file cấu hình mẫu
cd /opt/CAPEv2/conf
cp cuckoo.conf.default cuckoo.conf

# Edit file cấu hình database
nano cuckoo.conf

# Tìm section [database] và sửa:
# connection = postgresql://cape:cape_password@localhost:5432/cape

# Hoặc dùng sed để thay đổi nhanh:
sed -i 's|connection = .*|connection = postgresql://cape:cape_password@localhost:5432/cape|' cuckoo.conf
```

## BƯỚC 5: Cấu hình các file conf chính

### 5.1. Copy tất cả file conf từ default
```bash
cd /opt/CAPEv2/conf

# Copy tất cả file .default thành file .conf
for file in *.default; do
    cp "$file" "${file%.default}.conf"
done

# Kiểm tra các file đã copy
ls -la *.conf
```

### 5.2. Cấu hình cuckoo.conf
```bash
# Edit file cuckoo.conf
nano cuckoo.conf

# Các thay đổi quan trọng:
# [cuckoo]
# machinery = kvm
# 
# [resultserver]
# ip = 0.0.0.0
# port = 2042
#
# [database]
# connection = postgresql://cape:cape_password@localhost:5432/cape
```

### 5.3. Cấu hình routing.conf
```bash
# Edit routing.conf
nano routing.conf

# Thay đổi:
# [routing]
# route = internet
# internet = enp4s0
# nat = yes
```

### 5.4. Cấu hình auxiliary.conf
```bash
# Edit auxiliary.conf  
nano auxiliary.conf

# Thay đổi:
# [sniffer]
# enabled = yes
# interface = virbr1
```

## BƯỚC 6: Khởi tạo Database

### 6.1. Chạy database migration
```bash
cd /opt/CAPEv2

# Khởi tạo database schema
python3 utils/db_migration/migrate.py

# Nếu có lỗi, thử:
python3 cuckoo.py --init-database

# Kiểm tra database đã tạo tables
psql -h localhost -U cape -d cape -c "\dt"
```

### 6.2. Tạo user admin (nếu sử dụng web interface)
```bash
# Tạo superuser cho web interface
python3 utils/web.py --user admin --password admin123

# Hoặc tạo bằng Django command
cd /opt/CAPEv2/web
python3 manage.py createsuperuser
```

## BƯỚC 7: Cấu hình Signatures và Rules

### 7.1. Download community signatures
```bash
cd /opt/CAPEv2

# Download YARA rules
python3 utils/community.py -waf

# Hoặc download thủ công:
git clone https://github.com/kevoreilly/community-modified.git data/community

# Update signatures
python3 utils/community.py -s
```

### 7.2. Cấu hình processing modules
```bash
# Copy file processing.conf
cp conf/processing.conf.default conf/processing.conf

# Edit processing.conf nếu cần
nano conf/processing.conf

# Các modules quan trọng:
# [analysisinfo] enabled = yes
# [behavior] enabled = yes
# [network] enabled = yes
# [static] enabled = yes
# [strings] enabled = yes
# [virustotal] enabled = no (cần API key)
```

## BƯỚC 8: Setup Rooter (cho network routing)

### 8.1. Cài đặt rooter dependencies
```bash
# Cài đặt iptables và routing tools
sudo apt install -y iptables-persistent iproute2 bridge-utils

# Copy rooter script
sudo cp /opt/CAPEv2/utils/rooter.py /usr/local/bin/
sudo chmod +x /usr/local/bin/rooter.py
```

### 8.2. Tạo systemd service cho rooter
```bash
# Tạo service file
sudo tee /etc/systemd/system/cape-rooter.service << 'EOF'
[Unit]
Description=CAPE Rooter
After=network.target

[Service]
Type=simple
User=root
ExecStart=/usr/bin/python3 /usr/local/bin/rooter.py
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# Enable và start service
sudo systemctl daemon-reload
sudo systemctl enable cape-rooter
sudo systemctl start cape-rooter

# Kiểm tra status
sudo systemctl status cape-rooter
```

## BƯỚC 9: Test cấu hình cơ bản

### 9.1. Test CAPEv2 startup
```bash
cd /opt/CAPEv2

# Test cấu hình
python3 cuckoo.py --debug

# Nếu không có lỗi, Ctrl+C để dừng
```

### 9.2. Kiểm tra các components
```bash
# Kiểm tra database connection
python3 -c "
from lib.cuckoo.core.database import Database
db = Database()
print('Database connection: OK')
"

# Kiểm tra signatures
python3 -c "
from lib.cuckoo.common.utils import get_yara_rules
rules = get_yara_rules('binaries')
print(f'YARA rules loaded: {len(rules) if rules else 0}')
"
```

## BƯỚC 10: Cấu hình Web Interface

### 10.1. Setup Django settings
```bash
cd /opt/CAPEv2/web

# Copy settings file
cp web/settings.py.dist web/settings.py

# Generate secret key
python3 -c "
import secrets
print('SECRET_KEY = \"' + secrets.token_urlsafe(50) + '\"')
" >> web/local_settings.py
```

### 10.2. Migrate web database
```bash
cd /opt/CAPEv2/web

# Run Django migrations
python3 manage.py makemigrations
python3 manage.py migrate

# Collect static files
python3 manage.py collectstatic --noinput
```

### 10.3. Test web interface
```bash
# Start web server (test)
cd /opt/CAPEv2
python3 utils/web.py --host 0.0.0.0 --port 8000

# Truy cập http://YOUR_IP:8000 để test
# Ctrl+C để dừng
```

## BƯỚC 11: Tạo systemd services

### 11.1. Service cho CAPEv2 main
```bash
sudo tee /etc/systemd/system/cape.service << 'EOF'
[Unit]
Description=CAPE Sandbox
After=network.target postgresql.service

[Service]
Type=simple
User=cape
Group=cape
WorkingDirectory=/opt/CAPEv2
ExecStart=/usr/bin/python3 /opt/CAPEv2/cuckoo.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
```

### 11.2. Service cho Web interface
```bash
sudo tee /etc/systemd/system/cape-web.service << 'EOF'
[Unit]
Description=CAPE Web Interface
After=network.target cape.service

[Service]
Type=simple
User=cape
Group=cape
WorkingDirectory=/opt/CAPEv2
ExecStart=/usr/bin/python3 /opt/CAPEv2/utils/web.py --host 0.0.0.0 --port 8000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
```

### 11.3. Tạo user cape và set permissions
```bash
# Tạo user cape
sudo useradd -r -s /bin/bash -d /opt/CAPEv2 cape

# Set ownership
sudo chown -R cape:cape /opt/CAPEv2

# Add cape user to necessary groups
sudo usermod -aG libvirt cape
sudo usermod -aG kvm cape

# Enable services
sudo systemctl daemon-reload
sudo systemctl enable cape
sudo systemctl enable cape-web
```

## BƯỚC 12: Final Test

### 12.1. Start tất cả services
```bash
# Start rooter
sudo systemctl start cape-rooter

# Start CAPE
sudo systemctl start cape

# Start web interface
sudo systemctl start cape-web

# Kiểm tra status
sudo systemctl status cape-rooter
sudo systemctl status cape
sudo systemctl status cape-web
```

### 12.2. Test submit sample
```bash
# Test submit một file
cd /opt/CAPEv2
python3 utils/submit.py /bin/ls

# Kiểm tra logs
tail -f log/cuckoo.log
```

## BƯỚC 13: Troubleshooting

### 13.1. Kiểm tra logs
```bash
# CAPE logs
tail -f /opt/CAPEv2/log/cuckoo.log

# Rooter logs
sudo journalctl -u cape-rooter -f

# Web logs
sudo journalctl -u cape-web -f
```

### 13.2. Lỗi thường gặp
```bash
# Permission denied
sudo chown -R cape:cape /opt/CAPEv2
sudo chmod +x /opt/CAPEv2/cuckoo.py

# Database connection error
psql -h localhost -U cape -d cape -W

# Missing dependencies
pip3 install -r requirements.txt --upgrade
```

## Tóm tắt lệnh chạy CAPEv2

```bash
# Start services
sudo systemctl start cape-rooter cape cape-web

# Stop services
sudo systemctl stop cape cape-web cape-rooter

# Submit sample
python3 utils/submit.py sample.exe

# Web interface
http://YOUR_IP:8000

# Check logs
tail -f /opt/CAPEv2/log/cuckoo.log
```
