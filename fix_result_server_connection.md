# Fix Result Server Connection - Agent Communication

## Vấn đề phát hiện:
1. ✅ Result server listening trên port 2042 
2. ❌ Test sai hướng (test từ host đến VMs thay vì VMs đến host)
3. ❌ Test sai port (8000 thay vì 2042)
4. ❌ Agent trong VMs chưa kết nối được result server

## BƯỚC 1: Test đúng hướng connection

### 1.1. Script test connection đúng
```bash
#!/bin/bash
# File: test_correct_connection.sh

echo "=== TESTING CORRECT CONNECTION DIRECTION ==="

# 1. Confirm result server is listening
echo "1. Result Server Status:"
if netstat -tlnp | grep :2042 >/dev/null; then
    echo "✅ Result server listening on port 2042"
    netstat -tlnp | grep :2042
else
    echo "❌ Result server NOT listening on port 2042"
    exit 1
fi

# 2. Test VMs are reachable from host
echo ""
echo "2. VM Reachability (Host -> VMs):"
for vm_ip in *************01 *************02; do
    if ping -c 1 -W 2 $vm_ip >/dev/null 2>&1; then
        echo "✅ $vm_ip: Reachable from host"
    else
        echo "❌ $vm_ip: NOT reachable from host"
    fi
done

# 3. Check if VMs can reach host (this is what matters for agent)
echo ""
echo "3. Host Reachability (VMs -> Host):"
echo "Testing if host IP ************* is reachable..."

# Check host has IP on virbr1
if ip addr show virbr1 | grep "*************" >/dev/null; then
    echo "✅ Host has IP ************* on virbr1"
else
    echo "❌ Host does NOT have IP ************* on virbr1"
    echo "Current virbr1 IPs:"
    ip addr show virbr1 | grep "inet "
fi

# 4. Test result server accessibility from host perspective
echo ""
echo "4. Result Server Accessibility:"
if timeout 3 bash -c "</dev/tcp/*************/2042" 2>/dev/null; then
    echo "✅ Can connect to result server from localhost"
else
    echo "❌ Cannot connect to result server from localhost"
fi

# 5. Check firewall rules
echo ""
echo "5. Firewall Rules for port 2042:"
if iptables -L INPUT | grep -q "2042"; then
    echo "Firewall rules for port 2042:"
    iptables -L INPUT | grep "2042"
else
    echo "⚠️  No specific firewall rules for port 2042"
fi

echo ""
echo "=== CONNECTION TEST COMPLETED ==="
```

### 1.2. Fix host IP configuration
```bash
#!/bin/bash
# File: fix_host_ip.sh

echo "Fixing host IP configuration for result server..."

# 1. Ensure virbr1 has correct IP
echo "1. Checking virbr1 IP configuration..."
current_ip=$(ip addr show virbr1 | grep "inet " | awk '{print $2}' | cut -d'/' -f1)

if [ "$current_ip" = "*************" ]; then
    echo "✅ virbr1 already has correct IP: $current_ip"
else
    echo "❌ virbr1 has wrong IP: $current_ip"
    echo "Fixing IP configuration..."
    
    # Add correct IP if missing
    sudo ip addr add *************/24 dev virbr1 2>/dev/null || true
    
    # Verify
    if ip addr show virbr1 | grep "*************" >/dev/null; then
        echo "✅ IP ************* added to virbr1"
    else
        echo "❌ Failed to add IP to virbr1"
    fi
fi

# 2. Ensure result server binds correctly
echo ""
echo "2. Checking result server binding..."
if netstat -tlnp | grep "0.0.0.0:2042" >/dev/null; then
    echo "✅ Result server binding to all interfaces (0.0.0.0:2042)"
elif netstat -tlnp | grep "*************:2042" >/dev/null; then
    echo "✅ Result server binding to *************:2042"
else
    echo "❌ Result server binding issue"
    echo "Current bindings:"
    netstat -tlnp | grep :2042
fi

# 3. Allow result server port in firewall
echo ""
echo "3. Configuring firewall for result server..."
sudo iptables -A INPUT -p tcp --dport 2042 -j ACCEPT 2>/dev/null || true
sudo iptables -A INPUT -s *************/24 -p tcp --dport 2042 -j ACCEPT 2>/dev/null || true

echo "✅ Host IP configuration completed"
```

## BƯỚC 2: Create proper agent test

### 2.1. Agent connection test script
```bash
#!/bin/bash
# File: create_agent_connection_test.sh

echo "Creating agent connection test..."

# Create test script for VMs
cat > /tmp/test_agent_connection.py << 'EOF'
#!/usr/bin/env python3
"""
Test script to verify agent can connect to result server
Run this inside the VM to test connection
"""
import socket
import sys
import time

def test_connection(host, port):
    print(f"Testing connection to {host}:{port}")
    
    try:
        # Test basic socket connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        print(f"Attempting to connect...")
        result = sock.connect_ex((host, port))
        
        if result == 0:
            print("✅ SUCCESS: Can connect to result server!")
            
            # Try to send a test message
            try:
                test_msg = b"CAPE_TEST_CONNECTION\n"
                sock.send(test_msg)
                print("✅ SUCCESS: Can send data to result server!")
            except Exception as e:
                print(f"⚠️  Can connect but cannot send data: {e}")
            
        else:
            print(f"❌ FAILED: Cannot connect (error code: {result})")
            
        sock.close()
        
    except Exception as e:
        print(f"❌ FAILED: Connection error: {e}")
    
    return result == 0

def main():
    print("=== CAPE Agent Connection Test ===")
    print(f"Python version: {sys.version}")
    
    # Test connection to result server
    host = "*************"
    port = 2042
    
    success = test_connection(host, port)
    
    if success:
        print("\n✅ RESULT: Agent should be able to connect!")
        print("You can now run the actual agent:")
        print(f"python agent.py {host} {port}")
    else:
        print("\n❌ RESULT: Agent connection will fail!")
        print("Check network configuration and result server")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
EOF

# Create Windows batch file to run the test
cat > /tmp/test_agent_connection.bat << 'EOF'
@echo off
echo === CAPE Agent Connection Test ===
cd C:\cape-agent

echo Testing network connectivity...
ping -n 1 *************

echo.
echo Testing Python...
python --version

echo.
echo Testing agent connection...
python test_agent_connection.py

echo.
echo Test completed. Press any key to continue...
pause
EOF

# Create ISO with test files
mkdir -p /tmp/agent-connection-test
cp /tmp/test_agent_connection.py /tmp/agent-connection-test/
cp /tmp/test_agent_connection.bat /tmp/agent-connection-test/

# Add agent files
cp /opt/CAPEv2/agent/agent.py /tmp/agent-connection-test/ 2>/dev/null || true

genisoimage -o /var/lib/libvirt/images/iso/agent-connection-test.iso -V "AGENT_TEST" -r -J /tmp/agent-connection-test/

echo "✅ Agent connection test ISO created: /var/lib/libvirt/images/iso/agent-connection-test.iso"
echo ""
echo "Usage:"
echo "1. Attach ISO to VMs"
echo "2. Copy files to C:\cape-agent\"
echo "3. Run test_agent_connection.bat"
```

### 2.2. Monitor agent connections
```bash
#!/bin/bash
# File: monitor_agent_connections.sh

echo "Monitoring agent connections..."

# Monitor result server connections in real-time
echo "1. Monitoring result server connections (Ctrl+C to stop):"
echo "Watching for connections to port 2042..."

# Function to show current connections
show_connections() {
    echo "=== $(date) ==="
    echo "Active connections to port 2042:"
    netstat -an | grep :2042
    echo ""
    echo "Recent log entries:"
    tail -5 /opt/CAPEv2/log/cuckoo.log | grep -i -E "(agent|connect|guest|task)"
    echo "----------------------------------------"
}

# Show initial state
show_connections

# Monitor in loop
while true; do
    sleep 5
    show_connections
done
```

## BƯỚC 3: Complete fix script

### 3.1. Complete result server fix
```bash
#!/bin/bash
# File: complete_result_server_fix.sh

echo "=== COMPLETE RESULT SERVER FIX ==="

# 1. Fix host IP configuration
echo "Step 1: Fixing host IP configuration..."
sudo ip addr add *************/24 dev virbr1 2>/dev/null || true

# 2. Ensure result server can bind
echo "Step 2: Configuring firewall..."
sudo iptables -A INPUT -p tcp --dport 2042 -j ACCEPT 2>/dev/null || true
sudo iptables -A INPUT -s *************/24 -j ACCEPT 2>/dev/null || true

# 3. Restart CAPEv2 to ensure proper binding
echo "Step 3: Restarting CAPEv2..."
sudo systemctl restart cape

# Wait for service to start
sleep 10

# 4. Verify result server is running
echo "Step 4: Verifying result server..."
if netstat -tlnp | grep :2042 >/dev/null; then
    echo "✅ Result server is running"
    netstat -tlnp | grep :2042
else
    echo "❌ Result server failed to start"
    echo "Check logs:"
    tail -10 /opt/CAPEv2/log/cuckoo.log
    exit 1
fi

# 5. Test connection
echo ""
echo "Step 5: Testing connection..."
if timeout 3 bash -c "</dev/tcp/*************/2042" 2>/dev/null; then
    echo "✅ Result server is accessible"
else
    echo "❌ Result server is not accessible"
fi

# 6. Create agent test ISO
echo ""
echo "Step 6: Creating agent test ISO..."
./create_agent_connection_test.sh

echo ""
echo "=== FIX COMPLETED ==="
echo ""
echo "Next steps:"
echo "1. Attach test ISO to VMs:"
echo "   sudo virsh attach-disk cape1 /var/lib/libvirt/images/iso/agent-connection-test.iso hdc --type cdrom --mode readonly"
echo "   sudo virsh attach-disk cuckoo1 /var/lib/libvirt/images/iso/agent-connection-test.iso hdc --type cdrom --mode readonly"
echo ""
echo "2. In each VM:"
echo "   - Copy files from CD to C:\cape-agent\"
echo "   - Run test_agent_connection.bat"
echo "   - If test passes, run: python agent.py ************* 2042"
echo ""
echo "3. Monitor connections:"
echo "   ./monitor_agent_connections.sh"
```

## BƯỚC 4: Usage

```bash
# 1. Fix result server configuration
chmod +x *.sh
sudo ./complete_result_server_fix.sh

# 2. Test correct connection
./test_correct_connection.sh

# 3. Attach test ISO to VMs
sudo virsh attach-disk cape1 /var/lib/libvirt/images/iso/agent-connection-test.iso hdc --type cdrom --mode readonly
sudo virsh attach-disk cuckoo1 /var/lib/libvirt/images/iso/agent-connection-test.iso hdc --type cdrom --mode readonly

# 4. In VMs:
# - Copy files from CD to C:\cape-agent\
# - Run test_agent_connection.bat
# - If successful, run agent: python agent.py ************* 2042

# 5. Monitor connections
./monitor_agent_connections.sh

# 6. Submit test analysis
cd /opt/CAPEv2
python3 utils/submit.py --timeout 60 /bin/ls
```

## Kết quả mong đợi:

✅ **Agent connection test trong VM sẽ hiển thị:**
```
✅ SUCCESS: Can connect to result server!
✅ SUCCESS: Can send data to result server!
```

✅ **Monitor sẽ thấy:**
```
Active connections to port 2042:
tcp 0 0 *************:2042 *************01:xxxxx ESTABLISHED
```

✅ **Behavior Analysis sẽ có data:**
- API calls
- File operations  
- Process tree
- Registry changes

Vấn đề chính là **hướng kết nối và cấu hình IP**. Script này sẽ fix đúng!
