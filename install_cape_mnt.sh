#!/bin/bash

# Script cài đặt CAPEv2 tại /mnt với các tối ưu hóa cho storage
# Sử dụng: sudo ./install_cape_mnt.sh [mount_point] [ip_address]

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

function print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

function print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

function print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

function print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# C<PERSON>u hình mặc định
DEFAULT_MOUNT_POINT="/mnt/CAPEv2"
DEFAULT_IP="************"
CAPE_PATH="${1:-$DEFAULT_MOUNT_POINT}"
IFACE_IP="${2:-$DEFAULT_IP}"
USER_NAME=${SUDO_USER:-cape}

print_info "=== Cài đặt CAPEv2 tại $CAPE_PATH ==="
print_info "IP Address: $IFACE_IP"
print_info "User: $USER_NAME"

# Kiểm tra quyền root
if [ "$EUID" -ne 0 ]; then
    print_error "Script này cần chạy với quyền root"
    echo "Sử dụng: sudo $0 [mount_point] [ip_address]"
    echo "Ví dụ: sudo $0 /mnt/CAPEv2 ************"
    exit 1
fi

# Kiểm tra và tạo mount point
print_step "1. Kiểm tra mount point"
if [ ! -d "$CAPE_PATH" ]; then
    print_info "Tạo thư mục: $CAPE_PATH"
    mkdir -p "$CAPE_PATH"
fi

# Kiểm tra dung lượng
AVAILABLE_SPACE=$(df "$CAPE_PATH" | awk 'NR==2 {print $4}')
REQUIRED_SPACE=10485760  # 10GB in KB

if [ "$AVAILABLE_SPACE" -lt "$REQUIRED_SPACE" ]; then
    print_warning "Dung lượng có sẵn: $(($AVAILABLE_SPACE/1024/1024))GB"
    print_warning "Khuyến nghị tối thiểu: 10GB"
    read -p "Bạn có muốn tiếp tục? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Kiểm tra filesystem
FILESYSTEM=$(df -T "$CAPE_PATH" | awk 'NR==2 {print $2}')
print_info "Filesystem: $FILESYSTEM"

if [ "$FILESYSTEM" = "ntfs" ] || [ "$FILESYSTEM" = "vfat" ]; then
    print_warning "Filesystem $FILESYSTEM có thể gây vấn đề với permissions"
    print_warning "Khuyến nghị sử dụng ext4, xfs, hoặc btrfs"
fi

# Tạo user cape nếu chưa có
print_step "2. Cấu hình user"
if ! id "$USER_NAME" &>/dev/null; then
    print_info "Tạo user: $USER_NAME"
    useradd -m -s /bin/bash "$USER_NAME"
    usermod -aG sudo "$USER_NAME"
fi

# Clone CAPEv2
print_step "3. Clone CAPEv2"
if [ ! -d "$CAPE_PATH/.git" ]; then
    print_info "Clone CAPEv2 repository..."
    cd "$(dirname "$CAPE_PATH")"
    sudo -u "$USER_NAME" git clone https://github.com/kevoreilly/CAPEv2/ "$(basename "$CAPE_PATH")"
else
    print_info "CAPEv2 đã tồn tại, cập nhật..."
    cd "$CAPE_PATH"
    sudo -u "$USER_NAME" git pull
fi

# Cập nhật ownership
print_info "Cập nhật ownership..."
chown -R "$USER_NAME:$USER_NAME" "$CAPE_PATH"

# Tạo cấu trúc thư mục tối ưu cho /mnt
print_step "4. Tạo cấu trúc thư mục"
STORAGE_DIRS=(
    "$CAPE_PATH/storage/analyses"
    "$CAPE_PATH/storage/binaries" 
    "$CAPE_PATH/storage/baseline"
    "$CAPE_PATH/storage/guacrecordings"
    "$CAPE_PATH/logs"
    "$CAPE_PATH/db_backup"
)

for dir in "${STORAGE_DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        print_info "Tạo thư mục: $dir"
        mkdir -p "$dir"
        chown "$USER_NAME:$USER_NAME" "$dir"
    fi
done

# Tùy chỉnh installer
print_step "5. Tùy chỉnh installer"
cd "$CAPE_PATH"

# Backup installer gốc
if [ ! -f "installer/cape2.sh.original" ]; then
    cp "installer/cape2.sh" "installer/cape2.sh.original"
fi

# Tạo installer tùy chỉnh
print_info "Tạo installer tùy chỉnh..."
sed "s|/opt/CAPEv2|$CAPE_PATH|g" "installer/cape2.sh.original" > "installer/cape2_mnt.sh"

# Cập nhật IP trong installer
sed -i "s|IFACE_IP=\"***********\"|IFACE_IP=\"$IFACE_IP\"|g" "installer/cape2_mnt.sh"
sed -i "s|DIST_MASTER_IP=\"***********\"|DIST_MASTER_IP=\"$IFACE_IP\"|g" "installer/cape2_mnt.sh"

chmod +x "installer/cape2_mnt.sh"

# Tạo script khởi động tối ưu
print_step "6. Tạo script khởi động"
cat > "$CAPE_PATH/start_cape.sh" << EOF
#!/bin/bash

# Script khởi động CAPEv2 tại $CAPE_PATH
# Tối ưu hóa cho storage /mnt

CAPE_PATH="$CAPE_PATH"
USER_NAME="$USER_NAME"

echo "=== Khởi động CAPEv2 tại \$CAPE_PATH ==="

# Kiểm tra mount point
if [ ! -d "\$CAPE_PATH" ]; then
    echo "ERROR: \$CAPE_PATH không tồn tại hoặc chưa mount"
    exit 1
fi

# Kiểm tra permissions
if [ ! -w "\$CAPE_PATH" ]; then
    echo "ERROR: Không có quyền ghi vào \$CAPE_PATH"
    exit 1
fi

# Chuyển đến thư mục CAPE
cd "\$CAPE_PATH"

# Khởi động các service
echo "[+] Khởi động CAPE services..."
sudo systemctl start cape-rooter
sleep 2
sudo systemctl start cape
sleep 2  
sudo systemctl start cape-processor
sleep 2
sudo systemctl start cape-web

# Kiểm tra status
echo "[+] Kiểm tra status..."
systemctl status cape-rooter --no-pager -l
systemctl status cape --no-pager -l
systemctl status cape-processor --no-pager -l
systemctl status cape-web --no-pager -l

echo "=== CAPEv2 đã khởi động! ==="
echo "Web interface: http://localhost:8000"
echo "Storage path: \$CAPE_PATH/storage"
EOF

chmod +x "$CAPE_PATH/start_cape.sh"

# Tạo script backup
cat > "$CAPE_PATH/backup_cape.sh" << EOF
#!/bin/bash

# Script backup CAPEv2 từ $CAPE_PATH

CAPE_PATH="$CAPE_PATH"
BACKUP_DIR="/tmp/cape_backup_\$(date +%Y%m%d_%H%M%S)"

echo "=== Backup CAPEv2 ==="
echo "Source: \$CAPE_PATH"
echo "Backup: \$BACKUP_DIR"

mkdir -p "\$BACKUP_DIR"

# Backup cấu hình
echo "[+] Backup configuration..."
tar -czf "\$BACKUP_DIR/cape_config.tar.gz" -C "\$CAPE_PATH" conf/

# Backup database (nếu có)
if [ -f "\$CAPE_PATH/db/cuckoo.db" ]; then
    echo "[+] Backup database..."
    cp "\$CAPE_PATH/db/cuckoo.db" "\$BACKUP_DIR/"
fi

# Backup custom scripts
echo "[+] Backup custom files..."
tar -czf "\$BACKUP_DIR/cape_custom.tar.gz" -C "\$CAPE_PATH" \\
    --exclude="storage/analyses" \\
    --exclude="storage/binaries" \\
    --exclude="logs" \\
    .

echo "=== Backup hoàn tất! ==="
echo "Location: \$BACKUP_DIR"
ls -la "\$BACKUP_DIR"
EOF

chmod +x "$CAPE_PATH/backup_cape.sh"

# Tạo systemd override cho /mnt path
print_step "7. Cấu hình systemd"
SYSTEMD_OVERRIDE_DIR="/etc/systemd/system"
mkdir -p "$SYSTEMD_OVERRIDE_DIR"

# Tạo override cho cape.service
cat > "$SYSTEMD_OVERRIDE_DIR/cape.service.d/override.conf" << EOF
[Unit]
# Override for $CAPE_PATH installation

[Service]
WorkingDirectory=$CAPE_PATH
ExecStart=
ExecStart=$CAPE_PATH/.venv/bin/python cuckoo.py

[Install]
WantedBy=multi-user.target
EOF

print_info "=== Chuẩn bị cài đặt ==="
print_info "Đường dẫn: $CAPE_PATH"
print_info "IP: $IFACE_IP"
print_info "User: $USER_NAME"
print_info "Installer: $CAPE_PATH/installer/cape2_mnt.sh"
print_info ""
print_warning "Bước tiếp theo:"
print_warning "cd $CAPE_PATH"
print_warning "sudo bash installer/cape2_mnt.sh all $IFACE_IP"
print_info ""
print_info "Hoặc chạy ngay bây giờ? (y/N)"
read -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_step "8. Chạy installer"
    cd "$CAPE_PATH"
    bash "installer/cape2_mnt.sh" all "$IFACE_IP"
fi

print_info "=== Script hoàn tất! ==="
