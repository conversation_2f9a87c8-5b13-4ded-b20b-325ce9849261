# Hướng dẫn cấu hình các file .conf cho CAPEv2

## Tổng quan
Tài liệu này hướng dẫn cấu hình chi tiết tất cả các file .conf trong thư mục `conf/` để phù hợp với:
- Host IP: ************ (card mạng enp4s0)
- VM Network: *************/24
- cape1: *************01 (Windows 10 x64)
- cuckoo1: *************02 (Windows 7 x86)

## 1. File: conf/cuckoo.conf

### Cấu hình chính
```ini
[cuckoo]
# Sử dụng KVM làm machinery
machinery = kvm

# Thư mục chứa samples
tmppath = /tmp

# Memory limit cho analysis
memory_dump = on

[resultserver]
# Bind tất cả interfaces để VMs có thể kết nối
ip = 0.0.0.0

# Port cho result server
port = 2042

# Force port (không tự động chọn port khác)
force_port = no

# Upload limit (MB)
upload_max_size = 128

[processing]
# Enable tất cả processing modules
analysis_timeout = 200
critical_timeout = 60

# Sort PCAP cho web interface
sort_pcap = on

# Enable DNS lookups
resolve_dns = on

[database]
# Sử dụng PostgreSQL
connection = postgresql://cape:cape_password@localhost:5432/cape

# Timeout cho database operations
timeout = 60

[timeouts]
# Timeout cho các operations
default = 120
critical = 60
vm = 60

[remotecontrol]
# Disable remote control
enabled = no
```

## 2. File: conf/kvm.conf

### Cấu hình KVM machines
```ini
[kvm]
# Danh sách machines
machines = cape1,cuckoo1

# Interface cho packet capture
interface = virbr1

# Connection string cho libvirt
dsn = qemu:///system

[cape1]
# Label name trong libvirt
label = cape1

# Platform
platform = windows

# IP address của VM
ip = *************01

# Architecture
arch = x64

# Tags (bắt buộc cho Windows)
tags = win10,x64

# Snapshot name
snapshot = clean

# Result server IP như VM nhìn thấy
resultserver_ip = *************

# Result server port
resultserver_port = 2042

# Interface override
interface = virbr1

# Không reserve machine
reserved = no

[cuckoo1]
# Label name trong libvirt
label = cuckoo1

# Platform
platform = windows

# IP address của VM
ip = *************02

# Architecture
arch = x86

# Tags (bắt buộc cho Windows)
tags = win7,x86

# Snapshot name
snapshot = clean

# Result server IP như VM nhìn thấy
resultserver_ip = *************

# Result server port
resultserver_port = 2042

# Interface override
interface = virbr1

# Không reserve machine
reserved = no
```

## 3. File: conf/routing.conf

### Cấu hình network routing
```ini
[routing]
# Enable pcap generation
enable_pcap = yes

# Default routing mode - internet access
route = internet

# Interface cho internet access (dirty line)
internet = enp4s0

# Enable NAT masquerading
nat = yes

# Disable local routing bypass
no_local_routing = no

# Routing table
rt_table = main

# Reject segments (có thể thêm các subnet cần bảo vệ)
reject_segments = none

# Reject host ports (có thể block các port nhạy cảm)
reject_hostports = none

# Auto routing table initialization
auto_rt = yes

# Drop route (disable)
drop = no

# Verify interface is up
verify_interface = yes

# Verify routing table exists
verify_rt_table = yes

[inetsim]
# Disable InetSim
enabled = no

[tor]
# Disable Tor
enabled = no

[vpn]
# Disable VPN
enabled = no

[socks5]
# Disable SOCKS5
enabled = no
```

## 4. File: conf/auxiliary.conf

### Cấu hình auxiliary modules
```ini
[auxiliary_modules]
# Enable các modules cần thiết
sniffer = yes
mitm = no
services = yes
curtain = no
human = no
disguise = no
filepickup = no

[sniffer]
# Enable network sniffer
enabled = yes

# Disable remote sniffer
remote = no

# Path to tcpdump
tcpdump = /usr/bin/tcpdump

# Interface cho packet capture
interface = virbr1

# Berkeley packet filter
bpf = not arp

[mitm]
# Disable MITM
enabled = no

[services]
# Enable services monitoring
enabled = yes

[curtain]
# Disable curtain
enabled = no

[human]
# Disable human simulation
enabled = no

[disguise]
# Disable disguise
enabled = no
```

## 5. File: conf/processing.conf

### Cấu hình processing modules
```ini
[analysisinfo]
enabled = yes

[behavior]
enabled = yes

[debug]
enabled = yes

[dropped]
enabled = yes

[network]
enabled = yes

[static]
enabled = yes

[strings]
enabled = yes

[targetinfo]
enabled = yes

[virustotal]
# Disable nếu không có API key
enabled = no
# key = your_virustotal_api_key

[suricata]
# Enable nếu có Suricata
enabled = no

[clamav]
# Enable nếu có ClamAV
enabled = no

[yara]
enabled = yes

[procmemory]
enabled = yes
idapro = no

[procmon]
enabled = yes

[screenshots]
enabled = yes

[memory]
enabled = yes

[misp]
# Disable nếu không có MISP
enabled = no
```

## 6. File: conf/reporting.conf

### Cấu hình reporting modules
```ini
[jsondump]
enabled = yes
indent = 4
calls = yes

[reporthtml]
enabled = no

[mongodb]
# Disable MongoDB reporting
enabled = no

[elasticsearch]
# Disable Elasticsearch
enabled = no

[moloch]
# Disable Moloch
enabled = no

[resubmitexe]
enabled = yes

[mattermost]
# Disable Mattermost notifications
enabled = no

[callback]
# Disable callback
enabled = no
```

## 7. File: conf/memory.conf

### Cấu hình memory analysis
```ini
[basic]
# Enable memory analysis
enabled = yes

# Guest profile (sẽ auto-detect)
guest_profile =

# Delete memory dump sau khi analyze
delete_memdump = no

[malfind]
enabled = yes
filter = yes

[apihooks]
enabled = yes
filter = yes

[pslist]
enabled = yes
filter = no

[psxview]
enabled = yes
filter = no

[callbacks]
enabled = yes
filter = no

[idt]
enabled = yes
filter = no

[timers]
enabled = yes
filter = no

[messagehooks]
enabled = yes
filter = no

[getsids]
enabled = yes
filter = no

[privs]
enabled = yes
filter = no

[dlllist]
enabled = yes
filter = yes

[handles]
enabled = yes
filter = yes

[ldrmodules]
enabled = yes
filter = yes

[mutantscan]
enabled = yes
filter = yes

[devicetree]
enabled = yes
filter = yes

[svcscan]
enabled = yes
filter = yes

[modscan]
enabled = yes
filter = yes
```

## 8. File: conf/web.conf

### Cấu hình web interface
```ini
[web]
# Host và port cho web interface
host = 0.0.0.0
port = 8000

# Enable web interface
enabled = yes

# Cross-frame options
x_frame_options = DENY

# CSRF settings
csrf_cookie_secure = no

# Session settings
session_cookie_secure = no

# File upload settings
max_sample_size = 26214400

# Ratelimit settings
ratelimit_enabled = no

# API settings
api_token =

# Moloch settings
moloch_enabled = no

# Elasticsearch settings
elasticsearch_enabled = no

# Statistics
statistics_enabled = yes

# General settings
[general]
# Enable comments
comments = yes

# Enable tags
tags = yes

# Enable custom fields
custom_fields = yes
```

## 9. File: conf/vpn.conf (nếu sử dụng VPN)

### Cấu hình VPN (tùy chọn)
```ini
[vpn]
# Disable VPN by default
enabled = no

# VPN configurations would go here if needed
# [vpn0]
# name = vpn0
# description = VPN Connection
# interface = tun0
# rt_table = vpn0
```

## 10. Script tự động cấu hình

### Script để apply tất cả cấu hình
```bash
#!/bin/bash
# File: apply_cape_config.sh

CAPE_DIR="/opt/CAPEv2"
CONF_DIR="$CAPE_DIR/conf"

echo "Applying CAPEv2 configuration..."

# Backup existing configs
echo "Backing up existing configs..."
mkdir -p $CONF_DIR/backup
cp $CONF_DIR/*.conf $CONF_DIR/backup/ 2>/dev/null || true

# Apply cuckoo.conf
cat > $CONF_DIR/cuckoo.conf << 'EOF'
[cuckoo]
machinery = kvm
tmppath = /tmp
memory_dump = on

[resultserver]
ip = 0.0.0.0
port = 2042
force_port = no
upload_max_size = 128

[processing]
analysis_timeout = 200
critical_timeout = 60
sort_pcap = on
resolve_dns = on

[database]
connection = postgresql://cape:cape_password@localhost:5432/cape
timeout = 60

[timeouts]
default = 120
critical = 60
vm = 60

[remotecontrol]
enabled = no
EOF

# Apply kvm.conf
cat > $CONF_DIR/kvm.conf << 'EOF'
[kvm]
machines = cape1,cuckoo1
interface = virbr1
dsn = qemu:///system

[cape1]
label = cape1
platform = windows
ip = *************01
arch = x64
tags = win10,x64
snapshot = clean
resultserver_ip = *************
resultserver_port = 2042
interface = virbr1
reserved = no

[cuckoo1]
label = cuckoo1
platform = windows
ip = *************02
arch = x86
tags = win7,x86
snapshot = clean
resultserver_ip = *************
resultserver_port = 2042
interface = virbr1
reserved = no
EOF

# Apply routing.conf
cat > $CONF_DIR/routing.conf << 'EOF'
[routing]
enable_pcap = yes
route = internet
internet = enp4s0
nat = yes
no_local_routing = no
rt_table = main
reject_segments = none
reject_hostports = none
auto_rt = yes
drop = no
verify_interface = yes
verify_rt_table = yes

[inetsim]
enabled = no

[tor]
enabled = no

[vpn]
enabled = no

[socks5]
enabled = no
EOF

# Apply auxiliary.conf
cat > $CONF_DIR/auxiliary.conf << 'EOF'
[sniffer]
enabled = yes
remote = no
tcpdump = /usr/bin/tcpdump
interface = virbr1
bpf = not arp

[mitm]
enabled = no

[services]
enabled = yes
EOF

echo "Configuration applied successfully!"
echo "Please restart CAPEv2 services to apply changes."
```

## 11. Kiểm tra cấu hình

### Script kiểm tra
```bash
#!/bin/bash
# File: check_cape_config.sh

CAPE_DIR="/opt/CAPEv2"

echo "Checking CAPEv2 configuration..."

# Check database connection
echo "Testing database connection..."
cd $CAPE_DIR
python3 -c "
try:
    from lib.cuckoo.core.database import Database
    db = Database()
    print('✓ Database connection: OK')
except Exception as e:
    print('✗ Database connection failed:', e)
"

# Check VMs connectivity
echo "Testing VM connectivity..."
ping -c 1 *************01 >/dev/null 2>&1 && echo "✓ cape1 (*************01): Reachable" || echo "✗ cape1: Not reachable"
ping -c 1 *************02 >/dev/null 2>&1 && echo "✓ cuckoo1 (*************02): Reachable" || echo "✗ cuckoo1: Not reachable"

# Check result server port
echo "Testing result server port..."
netstat -tlnp | grep :2042 >/dev/null && echo "✓ Port 2042: Listening" || echo "✗ Port 2042: Not listening"

# Check tcpdump
echo "Testing tcpdump..."
which tcpdump >/dev/null && echo "✓ tcpdump: Available" || echo "✗ tcpdump: Not found"

# Check interface
echo "Testing network interface..."
ip link show virbr1 >/dev/null 2>&1 && echo "✓ virbr1: Available" || echo "✗ virbr1: Not found"
ip link show enp4s0 >/dev/null 2>&1 && echo "✓ enp4s0: Available" || echo "✗ enp4s0: Not found"

echo "Configuration check completed!"
```

## 12. Lệnh áp dụng cấu hình

```bash
# Tạo và chạy script cấu hình
chmod +x apply_cape_config.sh
sudo ./apply_cape_config.sh

# Kiểm tra cấu hình
chmod +x check_cape_config.sh
./check_cape_config.sh

# Restart services
sudo systemctl restart cape-rooter
sudo systemctl restart cape
sudo systemctl restart cape-web
```
