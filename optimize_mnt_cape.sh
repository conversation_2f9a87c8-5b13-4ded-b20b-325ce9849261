#!/bin/bash

# Script tối ưu hóa CAPEv2 cho /mnt storage
# Sử dụng: sudo ./optimize_mnt_cape.sh /mnt/CAPEv2

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

function print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

function print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

function print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

CAPE_PATH="${1:-/mnt/CAPEv2}"

print_info "=== Tối ưu hóa CAPEv2 tại $CAPE_PATH ==="

# Kiểm tra quyền
if [ "$EUID" -ne 0 ]; then
    echo "Cần quyền root để tối ưu hóa"
    exit 1
fi

# Kiểm tra đường dẫn
if [ ! -d "$CAPE_PATH" ]; then
    echo "Thư mục $CAPE_PATH không tồn tại"
    exit 1
fi

print_step "1. Tối ưu hóa filesystem"

# Kiểm tra filesystem type
FILESYSTEM=$(df -T "$CAPE_PATH" | awk 'NR==2 {print $2}')
DEVICE=$(df "$CAPE_PATH" | awk 'NR==2 {print $1}')

print_info "Filesystem: $FILESYSTEM"
print_info "Device: $DEVICE"

# Tối ưu mount options
case $FILESYSTEM in
    "ext4")
        print_info "Tối ưu hóa ext4..."
        # Thêm noatime để giảm I/O
        if ! mount | grep "$CAPE_PATH" | grep -q "noatime"; then
            print_warning "Khuyến nghị remount với noatime option"
            echo "sudo mount -o remount,noatime $CAPE_PATH"
        fi
        ;;
    "xfs")
        print_info "Tối ưu hóa XFS..."
        if ! mount | grep "$CAPE_PATH" | grep -q "noatime"; then
            print_warning "Khuyến nghị remount với noatime,largeio options"
            echo "sudo mount -o remount,noatime,largeio $CAPE_PATH"
        fi
        ;;
    "btrfs")
        print_info "Tối ưu hóa BTRFS..."
        if ! mount | grep "$CAPE_PATH" | grep -q "noatime"; then
            print_warning "Khuyến nghị remount với noatime,compress=zstd options"
            echo "sudo mount -o remount,noatime,compress=zstd $CAPE_PATH"
        fi
        ;;
esac

print_step "2. Cấu hình I/O scheduler"

# Tìm block device
BLOCK_DEVICE=$(lsblk -no pkname "$DEVICE" | head -1)
if [ -n "$BLOCK_DEVICE" ]; then
    CURRENT_SCHEDULER=$(cat /sys/block/$BLOCK_DEVICE/queue/scheduler | grep -o '\[.*\]' | tr -d '[]')
    print_info "Current I/O scheduler: $CURRENT_SCHEDULER"
    
    # Khuyến nghị scheduler cho SSD vs HDD
    if [ -f "/sys/block/$BLOCK_DEVICE/queue/rotational" ]; then
        ROTATIONAL=$(cat /sys/block/$BLOCK_DEVICE/queue/rotational)
        if [ "$ROTATIONAL" = "0" ]; then
            print_info "SSD detected - khuyến nghị scheduler: mq-deadline hoặc none"
            if [ "$CURRENT_SCHEDULER" != "mq-deadline" ] && [ "$CURRENT_SCHEDULER" != "none" ]; then
                echo "echo mq-deadline > /sys/block/$BLOCK_DEVICE/queue/scheduler"
            fi
        else
            print_info "HDD detected - khuyến nghị scheduler: mq-deadline"
            if [ "$CURRENT_SCHEDULER" != "mq-deadline" ]; then
                echo "echo mq-deadline > /sys/block/$BLOCK_DEVICE/queue/scheduler"
            fi
        fi
    fi
fi

print_step "3. Tối ưu hóa cấu trúc thư mục"

cd "$CAPE_PATH"

# Tạo symbolic links cho các thư mục lớn
STORAGE_OPTIMIZATIONS=(
    "storage/analyses:Phân tích malware"
    "storage/binaries:File malware"
    "logs:Log files"
)

for opt in "${STORAGE_OPTIMIZATIONS[@]}"; do
    DIR=$(echo "$opt" | cut -d: -f1)
    DESC=$(echo "$opt" | cut -d: -f2)
    
    if [ -d "$DIR" ]; then
        SIZE=$(du -sh "$DIR" | cut -f1)
        print_info "$DESC ($DIR): $SIZE"
        
        # Kiểm tra nếu có thể tối ưu
        FILE_COUNT=$(find "$DIR" -type f | wc -l)
        if [ "$FILE_COUNT" -gt 10000 ]; then
            print_warning "$DIR có $FILE_COUNT files - có thể cần partition riêng"
        fi
    fi
done

print_step "4. Cấu hình database tối ưu"

# PostgreSQL tuning cho /mnt
if [ -f "/etc/postgresql/*/main/postgresql.conf" ]; then
    PG_CONF=$(find /etc/postgresql -name "postgresql.conf" | head -1)
    print_info "Tối ưu PostgreSQL: $PG_CONF"
    
    # Backup config
    cp "$PG_CONF" "$PG_CONF.backup.$(date +%Y%m%d)"
    
    # Tối ưu cho storage lớn
    cat >> "$PG_CONF" << EOF

# CAPEv2 optimizations for /mnt storage
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 16MB
maintenance_work_mem = 256MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
EOF
    
    print_info "PostgreSQL config updated - cần restart service"
fi

print_step "5. Cấu hình log rotation"

# Tạo logrotate config cho CAPE
cat > "/etc/logrotate.d/cape-mnt" << EOF
$CAPE_PATH/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 cape cape
    postrotate
        systemctl reload cape-processor 2>/dev/null || true
    endscript
}

$CAPE_PATH/storage/analyses/*/analysis.log {
    weekly
    missingok
    rotate 4
    compress
    delaycompress
    notifempty
    maxage 30
}
EOF

print_step "6. Tạo monitoring script"

cat > "$CAPE_PATH/monitor_storage.sh" << EOF
#!/bin/bash

# Monitor storage usage cho CAPEv2 tại $CAPE_PATH

CAPE_PATH="$CAPE_PATH"
THRESHOLD=80  # Cảnh báo khi dùng > 80%

echo "=== CAPEv2 Storage Monitor ==="
echo "Path: \$CAPE_PATH"
echo "Time: \$(date)"
echo

# Kiểm tra dung lượng tổng
USAGE=\$(df "\$CAPE_PATH" | awk 'NR==2 {print \$5}' | sed 's/%//')
AVAILABLE=\$(df -h "\$CAPE_PATH" | awk 'NR==2 {print \$4}')

echo "Disk Usage: \$USAGE% (Available: \$AVAILABLE)"

if [ "\$USAGE" -gt "\$THRESHOLD" ]; then
    echo "⚠️  WARNING: Disk usage > \$THRESHOLD%"
fi

echo

# Top directories by size
echo "=== Top Storage Consumers ==="
du -sh "\$CAPE_PATH"/* 2>/dev/null | sort -hr | head -10

echo

# Analysis count
if [ -d "\$CAPE_PATH/storage/analyses" ]; then
    ANALYSIS_COUNT=\$(find "\$CAPE_PATH/storage/analyses" -maxdepth 1 -type d | wc -l)
    echo "Total Analyses: \$ANALYSIS_COUNT"
fi

# Binary count  
if [ -d "\$CAPE_PATH/storage/binaries" ]; then
    BINARY_COUNT=\$(find "\$CAPE_PATH/storage/binaries" -type f | wc -l)
    echo "Total Binaries: \$BINARY_COUNT"
fi

echo
echo "=== Recent Activity ==="
if [ -d "\$CAPE_PATH/storage/analyses" ]; then
    echo "Recent analyses:"
    find "\$CAPE_PATH/storage/analyses" -maxdepth 1 -type d -mtime -1 | wc -l | xargs echo "  Last 24h:"
    find "\$CAPE_PATH/storage/analyses" -maxdepth 1 -type d -mtime -7 | wc -l | xargs echo "  Last 7 days:"
fi
EOF

chmod +x "$CAPE_PATH/monitor_storage.sh"

print_step "7. Cấu hình cleanup tự động"

# Tạo cleanup script
cat > "$CAPE_PATH/cleanup_old_analyses.sh" << EOF
#!/bin/bash

# Cleanup old analyses để tiết kiệm dung lượng
# Chạy hàng tuần qua cron

CAPE_PATH="$CAPE_PATH"
DAYS_TO_KEEP=30  # Giữ analyses trong 30 ngày

echo "=== CAPEv2 Cleanup ==="
echo "Cleaning analyses older than \$DAYS_TO_KEEP days..."

if [ -d "\$CAPE_PATH/storage/analyses" ]; then
    # Đếm trước khi xóa
    OLD_COUNT=\$(find "\$CAPE_PATH/storage/analyses" -maxdepth 1 -type d -mtime +\$DAYS_TO_KEEP | wc -l)
    
    if [ "\$OLD_COUNT" -gt 0 ]; then
        echo "Found \$OLD_COUNT old analyses to remove"
        
        # Xóa analyses cũ
        find "\$CAPE_PATH/storage/analyses" -maxdepth 1 -type d -mtime +\$DAYS_TO_KEEP -exec rm -rf {} \;
        
        echo "Cleanup completed"
    else
        echo "No old analyses found"
    fi
fi

# Cleanup logs cũ
if [ -d "\$CAPE_PATH/logs" ]; then
    find "\$CAPE_PATH/logs" -name "*.log.*" -mtime +7 -delete
    echo "Old logs cleaned"
fi

echo "=== Cleanup finished ==="
EOF

chmod +x "$CAPE_PATH/cleanup_old_analyses.sh"

# Thêm vào cron
(crontab -l 2>/dev/null; echo "0 2 * * 0 $CAPE_PATH/cleanup_old_analyses.sh >> $CAPE_PATH/logs/cleanup.log 2>&1") | crontab -

print_info "=== Tối ưu hóa hoàn tất! ==="
print_info ""
print_info "Các script đã tạo:"
print_info "  - $CAPE_PATH/monitor_storage.sh (monitor dung lượng)"
print_info "  - $CAPE_PATH/cleanup_old_analyses.sh (cleanup tự động)"
print_info ""
print_info "Cron jobs đã thêm:"
print_info "  - Weekly cleanup old analyses"
print_info ""
print_info "Khuyến nghị tiếp theo:"
print_info "  1. Restart PostgreSQL: sudo systemctl restart postgresql"
print_info "  2. Chạy monitor: $CAPE_PATH/monitor_storage.sh"
print_info "  3. Kiểm tra mount options cho performance tối ưu"
