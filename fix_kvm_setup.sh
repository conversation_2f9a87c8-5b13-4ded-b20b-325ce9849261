#!/bin/bash

# Script kiểm tra và sửa lỗi KVM cho CAPEv2
# Sử dụng: sudo ./fix_kvm_setup.sh

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

function print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

function print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

function print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

function print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_info "=== KIỂM TRA VÀ SỬA LỖI KVM CHO CAPEv2 ==="

# Kiểm tra quyền root
if [ "$EUID" -ne 0 ]; then
    print_error "Script này cần chạy với quyền root"
    echo "Sử dụng: sudo $0"
    exit 1
fi

print_step "1. Kiểm tra CPU hỗ trợ virtualization"
CPU_VT=$(egrep -c '(vmx|svm)' /proc/cpuinfo || echo "0")
if [ "$CPU_VT" -gt 0 ]; then
    print_info "✅ CPU hỗ trợ virtualization ($CPU_VT cores)"
    if egrep -q 'vmx' /proc/cpuinfo; then
        CPU_TYPE="intel"
        KVM_MODULE="kvm_intel"
        print_info "CPU Type: Intel VT-x"
    else
        CPU_TYPE="amd"
        KVM_MODULE="kvm_amd"
        print_info "CPU Type: AMD-V"
    fi
else
    print_error "❌ CPU không hỗ trợ virtualization"
    print_error "Cần enable VT-x/AMD-V trong BIOS/UEFI"
    exit 1
fi

print_step "2. Kiểm tra KVM modules"
if lsmod | grep -q "^kvm "; then
    print_info "✅ KVM module đã được load"
else
    print_warning "⚠️  KVM module chưa được load"
    print_info "Loading KVM modules..."
    modprobe kvm
    modprobe "$KVM_MODULE"
    
    if lsmod | grep -q "^kvm "; then
        print_info "✅ KVM modules loaded successfully"
    else
        print_error "❌ Không thể load KVM modules"
        print_error "Kiểm tra BIOS settings hoặc kernel support"
        exit 1
    fi
fi

print_step "3. Kiểm tra /dev/kvm"
if [ -c "/dev/kvm" ]; then
    print_info "✅ /dev/kvm exists"
    ls -la /dev/kvm
else
    print_error "❌ /dev/kvm không tồn tại"
    print_error "KVM modules có thể chưa được load đúng cách"
    exit 1
fi

print_step "4. Kiểm tra QEMU/KVM installation"
if command -v qemu-system-x86_64 >/dev/null 2>&1; then
    print_info "✅ QEMU đã được cài đặt"
    qemu-system-x86_64 --version | head -1
else
    print_warning "⚠️  QEMU chưa được cài đặt"
    print_info "Cài đặt QEMU/KVM..."
    
    # Detect OS
    if [ -f /etc/debian_version ]; then
        apt update
        apt install -y qemu-kvm libvirt-daemon-system libvirt-clients bridge-utils virt-manager
    elif [ -f /etc/redhat-release ]; then
        if command -v dnf >/dev/null 2>&1; then
            dnf install -y qemu-kvm libvirt virt-install bridge-utils virt-manager
        else
            yum install -y qemu-kvm libvirt virt-install bridge-utils virt-manager
        fi
    else
        print_error "Không thể detect OS để cài đặt packages"
        print_error "Vui lòng cài đặt manual: qemu-kvm, libvirt, bridge-utils"
        exit 1
    fi
fi

print_step "5. Cấu hình libvirt service"
if systemctl is-active --quiet libvirtd; then
    print_info "✅ libvirtd service đang chạy"
else
    print_info "Khởi động libvirtd service..."
    systemctl enable libvirtd
    systemctl start libvirtd
    sleep 2
    
    if systemctl is-active --quiet libvirtd; then
        print_info "✅ libvirtd service started"
    else
        print_error "❌ Không thể khởi động libvirtd"
        systemctl status libvirtd
        exit 1
    fi
fi

print_step "6. Cấu hình user permissions"
CAPE_USER="cape"
CURRENT_USER=${SUDO_USER:-$USER}

# Tạo user cape nếu chưa có
if ! id "$CAPE_USER" &>/dev/null; then
    print_info "Tạo user: $CAPE_USER"
    useradd -m -s /bin/bash "$CAPE_USER"
fi

# Thêm users vào groups
for user in "$CAPE_USER" "$CURRENT_USER"; do
    if id "$user" &>/dev/null; then
        print_info "Thêm $user vào libvirt và kvm groups"
        usermod -aG libvirt "$user"
        usermod -aG kvm "$user"
    fi
done

print_step "7. Kiểm tra KVM functionality"
if sudo -u "$CAPE_USER" virsh list --all >/dev/null 2>&1; then
    print_info "✅ User $CAPE_USER có thể truy cập libvirt"
else
    print_warning "⚠️  User $CAPE_USER chưa thể truy cập libvirt"
    print_info "User cần logout/login lại để áp dụng group changes"
fi

print_step "8. Tạo default network"
if virsh net-list --all | grep -q "default"; then
    print_info "✅ Default network đã tồn tại"
    if ! virsh net-list | grep -q "default.*active"; then
        print_info "Khởi động default network..."
        virsh net-start default
        virsh net-autostart default
    fi
else
    print_info "Tạo default network..."
    virsh net-define /usr/share/libvirt/networks/default.xml
    virsh net-start default
    virsh net-autostart default
fi

print_step "9. Kiểm tra bridge network"
if ip link show virbr0 >/dev/null 2>&1; then
    print_info "✅ Bridge virbr0 đã tồn tại"
    ip addr show virbr0 | grep inet
else
    print_warning "⚠️  Bridge virbr0 chưa tồn tại"
    print_info "Restart libvirtd để tạo bridge..."
    systemctl restart libvirtd
    sleep 3
    
    if ip link show virbr0 >/dev/null 2>&1; then
        print_info "✅ Bridge virbr0 created"
    else
        print_error "❌ Không thể tạo bridge virbr0"
    fi
fi

print_step "10. Auto-load KVM modules"
print_info "Cấu hình auto-load KVM modules..."
echo 'kvm' >> /etc/modules
echo "$KVM_MODULE" >> /etc/modules

# Remove duplicates
sort -u /etc/modules -o /etc/modules

print_step "11. Test KVM functionality"
print_info "Test tạo VM đơn giản..."
if sudo -u "$CAPE_USER" qemu-system-x86_64 -machine accel=kvm -m 64 -nographic -serial none -monitor none -display none -cpu host -smp 1 -kernel /dev/null 2>/dev/null &
then
    TEST_PID=$!
    sleep 2
    if kill -0 $TEST_PID 2>/dev/null; then
        kill $TEST_PID 2>/dev/null
        print_info "✅ KVM acceleration hoạt động"
    fi
else
    print_warning "⚠️  KVM test không thành công"
fi

print_info "=== KIỂM TRA HOÀN TẤT ==="
print_info ""
print_info "📋 TỔNG KẾT:"
print_info "✅ CPU hỗ trợ virtualization: $CPU_TYPE"
print_info "✅ KVM modules: $(lsmod | grep kvm | wc -l) loaded"
print_info "✅ QEMU version: $(qemu-system-x86_64 --version | head -1 | cut -d' ' -f4)"
print_info "✅ libvirtd service: $(systemctl is-active libvirtd)"
print_info "✅ Default network: $(virsh net-list | grep default | awk '{print $2}')"
print_info ""
print_warning "⚠️  QUAN TRỌNG:"
print_warning "User $CAPE_USER và $CURRENT_USER cần LOGOUT/LOGIN lại"
print_warning "để áp dụng group permissions (libvirt, kvm)"
print_info ""
print_info "🔧 KIỂM TRA CUỐI CÙNG:"
print_info "Sau khi logout/login, chạy:"
print_info "  virsh list --all"
print_info "  groups \$USER  # phải thấy libvirt và kvm"
print_info ""
print_info "🚀 TIẾP THEO:"
print_info "Bây giờ có thể cài đặt CAPEv2 với KVM support"
