#!/usr/bin/env python3
"""
Script tự động cấu hình các file conf cho process hooking trong CAPEv2
Tạo và cập nhật các file cấu hình cần thiết để cải thiện system process hooking
"""

import os
import sys
import shutil
from pathlib import Path

def find_cape_path():
    """Tìm đường dẫn CAPEv2"""
    possible_paths = [
        'f:/CAPEv2',  # Current Windows path
        'C:/CAPEv2',
        '/opt/CAPEv2',
        '/mnt/CAPEv2',
        '/data/CAPEv2',
        '/srv/CAPEv2'
    ]
    
    for path in possible_paths:
        if os.path.exists(path) and os.path.exists(os.path.join(path, 'cuckoo.py')):
            return path
    
    # Sử dụng thư mục hiện tại nếu có cuckoo.py
    current_dir = os.getcwd()
    if os.path.exists(os.path.join(current_dir, 'cuckoo.py')):
        return current_dir
    
    return None

def backup_config(config_path):
    """Tạo backup của file config"""
    if os.path.exists(config_path):
        backup_path = config_path + '.backup'
        if not os.path.exists(backup_path):
            shutil.copy2(config_path, backup_path)
            print(f"✅ Backup created: {backup_path}")
        return True
    return False

def create_analyzer_conf(cape_path):
    """Tạo file analyzer.conf cho process injection settings"""
    conf_path = os.path.join(cape_path, 'conf', 'analyzer.conf')
    
    analyzer_conf_content = '''# CAPEv2 Analyzer Configuration
# Process injection and monitoring settings

[analyzer]
# Enable process injection
inject_processes = yes
# Allow injection into protected processes
protected_processes = no
# Injection timeout in seconds
injection_timeout = 30
# Enable retry mechanism for failed injections
retry_injection = yes
# Enable enhanced system process injection
system_process_injection = yes

[injection]
# Enhanced injection settings for system processes
dllhost_injection = yes
svchost_injection = yes
explorer_injection = yes
services_injection = yes
# Number of retry attempts for system processes
retry_count = 5
# Delay between retry attempts (seconds)
retry_delay = 0.2
# Enable verbose logging for injection attempts
verbose_logging = yes

[monitoring]
# Monitor DLL settings
monitor_dll = capemon.dll
monitor_dll_64 = capemon_x64.dll
# Default injection method
injection_method = standard
# Enable enhanced monitoring for system processes
enhanced_system_monitoring = yes

[system_processes]
# List of system processes to attempt injection
target_processes = dllhost.exe,svchost.exe,explorer.exe,services.exe,winlogon.exe
# Maximum instances to inject per process type
max_instances_dllhost = 10
max_instances_svchost = 20
max_instances_explorer = 1
max_instances_services = 1
'''
    
    backup_config(conf_path)
    
    with open(conf_path, 'w') as f:
        f.write(analyzer_conf_content)
    
    print(f"✅ Created analyzer.conf: {conf_path}")
    return True

def create_system_processes_conf(cape_path):
    """Tạo file system_processes.conf"""
    conf_path = os.path.join(cape_path, 'conf', 'system_processes.conf')
    
    system_processes_conf_content = '''# System Process Hooking Configuration
# Enhanced configuration for system process injection

[system_processes]
# Enable enhanced system process injection
enabled = yes

# Processes to attempt injection (despite being system processes)
target_processes = dllhost.exe,svchost.exe,explorer.exe,services.exe

# Injection retry attempts for system processes
retry_attempts = 5

# Delay between retry attempts (seconds)
retry_delay = 0.2

# Enable verbose logging for system process injection
verbose_logging = yes

# Enable special handling for COM+ processes
com_plus_handling = yes

[dllhost]
# COM+ Application Host process
description = COM+ Application Host
priority = high
method = enhanced
max_instances = 10
# Special flags for dllhost injection
injection_flags = PROCESS_ALL_ACCESS
retry_on_failure = yes

[svchost]
# Service Host process
description = Service Host Process
priority = high  
method = enhanced
max_instances = 20
# Target specific svchost instances (avoid critical ones)
target_services = BITS,Themes,AudioSrv,Dhcp,EventLog
injection_flags = PROCESS_ALL_ACCESS
retry_on_failure = yes

[explorer]
# Windows Shell process
description = Windows Shell
priority = medium
method = standard
max_instances = 1
# Explorer is critical, use careful injection
injection_flags = PROCESS_ALL_ACCESS
retry_on_failure = yes

[services]
# Service Control Manager
description = Service Control Manager
priority = medium
method = standard
max_instances = 1
# Services.exe is very critical
injection_flags = PROCESS_ALL_ACCESS
retry_on_failure = no

[winlogon]
# Windows Logon process
description = Windows Logon Process
priority = low
method = skip
max_instances = 1
# Usually skip winlogon due to high protection
injection_flags = NONE
retry_on_failure = no
'''
    
    backup_config(conf_path)
    
    with open(conf_path, 'w') as f:
        f.write(system_processes_conf_content)
    
    print(f"✅ Created system_processes.conf: {conf_path}")
    return True

def update_processing_conf(cape_path):
    """Cập nhật processing.conf để enable behavior analysis"""
    conf_path = os.path.join(cape_path, 'conf', 'processing.conf')
    
    # Nếu không có file, copy từ default
    if not os.path.exists(conf_path):
        default_path = os.path.join(cape_path, 'conf', 'default', 'processing.conf.default')
        if os.path.exists(default_path):
            shutil.copy2(default_path, conf_path)
            print(f"✅ Copied default processing.conf")
    
    backup_config(conf_path)
    
    # Đọc file hiện tại
    if os.path.exists(conf_path):
        with open(conf_path, 'r') as f:
            content = f.read()
        
        # Cập nhật behavior section
        if '[behavior]' in content:
            # Ensure behavior is enabled
            content = content.replace('enabled = no', 'enabled = yes')
            content = content.replace('enhanced = no', 'enhanced = yes')
            
            # Add system process specific settings if not present
            if 'system_process_analysis' not in content:
                behavior_section = content.find('[behavior]')
                if behavior_section != -1:
                    next_section = content.find('\n[', behavior_section + 1)
                    if next_section == -1:
                        next_section = len(content)
                    
                    additional_settings = '''
# Enhanced system process analysis
system_process_analysis = yes
system_process_logging = yes
injection_monitoring = yes
'''
                    content = content[:next_section] + additional_settings + content[next_section:]
        
        with open(conf_path, 'w') as f:
            f.write(content)
        
        print(f"✅ Updated processing.conf: {conf_path}")
        return True
    
    return False

def update_auxiliary_conf(cape_path):
    """Cập nhật auxiliary.conf để enable monitoring modules"""
    conf_path = os.path.join(cape_path, 'conf', 'auxiliary.conf')
    
    # Nếu không có file, copy từ default
    if not os.path.exists(conf_path):
        default_path = os.path.join(cape_path, 'conf', 'default', 'auxiliary.conf.default')
        if os.path.exists(default_path):
            shutil.copy2(default_path, conf_path)
            print(f"✅ Copied default auxiliary.conf")
    
    backup_config(conf_path)
    
    # Đọc và cập nhật file
    if os.path.exists(conf_path):
        with open(conf_path, 'r') as f:
            content = f.read()
        
        # Enable các modules quan trọng cho system process monitoring
        important_modules = {
            'procmon': 'yes',
            'sysmon_windows': 'yes', 
            'evtx': 'yes',
            'wmi_etw': 'yes',
            'permissions': 'yes'
        }
        
        for module, value in important_modules.items():
            pattern = f'{module} = no'
            replacement = f'{module} = {value}'
            content = content.replace(pattern, replacement)
        
        with open(conf_path, 'w') as f:
            f.write(content)
        
        print(f"✅ Updated auxiliary.conf: {conf_path}")
        return True
    
    return False

def create_injection_test_conf(cape_path):
    """Tạo file test configuration cho injection"""
    conf_path = os.path.join(cape_path, 'conf', 'injection_test.conf')
    
    test_conf_content = '''# Injection Test Configuration
# Configuration for testing system process injection

[test_settings]
# Enable test mode
test_mode = yes
# Log all injection attempts
log_all_attempts = yes
# Test timeout in seconds
test_timeout = 60

[target_processes]
# Processes to test injection
dllhost = yes
svchost = yes
explorer = yes
services = yes
winlogon = no

[test_results]
# Where to store test results
results_file = injection_test_results.log
# Enable detailed logging
detailed_logging = yes
# Include process memory info
include_memory_info = yes
'''
    
    with open(conf_path, 'w') as f:
        f.write(test_conf_content)
    
    print(f"✅ Created injection_test.conf: {conf_path}")
    return True

def main():
    """Main function"""
    print("=" * 60)
    print("CAPEv2 Process Hooking Configuration Setup")
    print("Tạo và cấu hình các file conf cho system process hooking")
    print("=" * 60)
    
    # Tìm CAPEv2 path
    cape_path = find_cape_path()
    if not cape_path:
        print("❌ CAPEv2 installation not found!")
        print("Please run this script from CAPEv2 directory")
        return 1
    
    print(f"🔍 Found CAPEv2 at: {cape_path}")
    
    # Tạo thư mục conf nếu chưa có
    conf_dir = os.path.join(cape_path, 'conf')
    os.makedirs(conf_dir, exist_ok=True)
    
    success_count = 0
    total_tasks = 5
    
    # Tạo các file cấu hình
    if create_analyzer_conf(cape_path):
        success_count += 1
    
    if create_system_processes_conf(cape_path):
        success_count += 1
    
    if update_processing_conf(cape_path):
        success_count += 1
    
    if update_auxiliary_conf(cape_path):
        success_count += 1
    
    if create_injection_test_conf(cape_path):
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"CONFIGURATION SETUP COMPLETED: {success_count}/{total_tasks}")
    print("=" * 60)
    
    if success_count == total_tasks:
        print("✅ All configuration files created/updated successfully!")
        print("\nFiles created/updated:")
        print("- conf/analyzer.conf")
        print("- conf/system_processes.conf") 
        print("- conf/processing.conf")
        print("- conf/auxiliary.conf")
        print("- conf/injection_test.conf")
        
        print("\nNext steps:")
        print("1. Run: python3 fix_dllhost_svchost_hooking.py")
        print("2. Copy setup_vm_for_hooking.bat to Windows VM")
        print("3. Run VM setup script as Administrator")
        print("4. Restart CAPEv2 services")
        print("5. Test with: python3 test_injection.py")
    else:
        print(f"⚠️  Some configuration tasks failed ({success_count}/{total_tasks})")
        print("Please check the errors above and try again")
    
    return 0 if success_count == total_tasks else 1

if __name__ == "__main__":
    sys.exit(main())
