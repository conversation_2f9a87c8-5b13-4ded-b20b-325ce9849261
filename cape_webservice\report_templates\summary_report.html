{% extends "base_report.html" %}

{% block title %}Summary Report - {{ report.info.id }}{% endblock %}

{% block content %}
<!-- Report Header -->
<div class="report-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 mb-2">
                    <i class="fas fa-clipboard-list me-3"></i>
                    Executive Summary
                </h1>
                <p class="lead mb-0">
                    Analysis summary for: <strong>{{ report.target.file.name }}</strong>
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="status-badge status-{{ report.info.score|score_to_status }}">
                    <i class="fas fa-shield-alt me-2"></i>
                    {{ report.info.score|score_to_verdict }}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Executive Summary -->
    <section class="report-section">
        <h2 class="section-title">
            <i class="fas fa-chart-line me-2"></i>
            Executive Summary
        </h2>
        
        <div class="row">
            <div class="col-md-8">
                <div class="alert alert-{{ report.info.score|score_to_status }} border-0 shadow-sm">
                    <h4 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Analysis Verdict: {{ report.info.score|score_to_verdict }}
                    </h4>
                    <p class="mb-3">
                        The analyzed file <strong>{{ report.target.file.name }}</strong> 
                        received a threat score of <strong>{{ report.info.score }}/10</strong>.
                        {% if report.info.score >= 7 %}
                        This indicates a high probability of malicious behavior.
                        {% elif report.info.score >= 4 %}
                        This indicates suspicious behavior that requires further investigation.
                        {% else %}
                        This indicates the file appears to be clean or has minimal suspicious indicators.
                        {% endif %}
                    </p>
                    <hr>
                    <p class="mb-0">
                        <strong>Key Findings:</strong>
                        {% if report.signatures %}
                        {{ report.signatures|length }} behavioral signatures detected.
                        {% endif %}
                        {% if report.behavior.processes %}
                        {{ report.behavior.processes|length }} processes monitored.
                        {% endif %}
                        {% if report.network %}
                        Network activity observed.
                        {% endif %}
                    </p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Quick Stats
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Threat Score:</span>
                            <strong class="text-{{ report.info.score|score_to_status }}">
                                {{ report.info.score }}/10
                            </strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Signatures:</span>
                            <strong>{{ report.signatures|length if report.signatures else 0 }}</strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Processes:</span>
                            <strong>{{ report.behavior.processes|length if report.behavior.processes else 0 }}</strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Network Connections:</span>
                            <strong>{{ (report.network.tcp|length + report.network.udp|length) if report.network else 0 }}</strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Analysis Time:</span>
                            <strong>{{ report.info.duration }}s</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Top Threats -->
    {% if report.signatures %}
    <section class="report-section">
        <h2 class="section-title">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Top Security Threats
        </h2>
        
        <div class="row">
            {% for signature in report.signatures[:6] %}
            <div class="col-md-6 mb-3">
                <div class="card h-100 border-left-{{ signature.severity|severity_to_color }}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title mb-0">{{ signature.name }}</h5>
                            <span class="badge bg-{{ signature.severity|severity_to_color }}">
                                {{ signature.severity|upper }}
                            </span>
                        </div>
                        <p class="card-text text-muted">{{ signature.description|truncate(100) }}</p>
                        {% if signature.marks %}
                        <small class="text-muted">
                            <i class="fas fa-flag me-1"></i>
                            {{ signature.marks|length }} indicators
                        </small>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        {% if report.signatures|length > 6 %}
        <div class="text-center">
            <p class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                {{ report.signatures|length - 6 }} additional signatures detected.
                <a href="#" class="text-decoration-none">View full report for details.</a>
            </p>
        </div>
        {% endif %}
    </section>
    {% endif %}

    <!-- File Analysis -->
    <section class="report-section">
        <h2 class="section-title">
            <i class="fas fa-file-alt me-2"></i>
            File Analysis
        </h2>
        
        <div class="row">
            <div class="col-md-6">
                <h4><i class="fas fa-fingerprint me-2"></i>File Hashes</h4>
                <div class="table-responsive">
                    <table class="table table-borderless">
                        <tbody>
                            <tr>
                                <td><strong>MD5:</strong></td>
                                <td><code class="text-break">{{ report.target.file.md5 }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>SHA1:</strong></td>
                                <td><code class="text-break">{{ report.target.file.sha1 }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>SHA256:</strong></td>
                                <td><code class="text-break">{{ report.target.file.sha256 }}</code></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div class="col-md-6">
                <h4><i class="fas fa-info me-2"></i>File Properties</h4>
                <div class="table-responsive">
                    <table class="table table-borderless">
                        <tbody>
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td>{{ report.target.file.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Size:</strong></td>
                                <td>{{ report.target.file.size|filesizeformat }}</td>
                            </tr>
                            <tr>
                                <td><strong>Type:</strong></td>
                                <td><span class="badge bg-info">{{ report.target.file.type }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Analysis ID:</strong></td>
                                <td>{{ report.info.id }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>

    <!-- Network Summary -->
    {% if report.network %}
    <section class="report-section">
        <h2 class="section-title">
            <i class="fas fa-network-wired me-2"></i>
            Network Activity Summary
        </h2>
        
        <div class="row">
            <div class="col-md-4">
                <div class="metric-card">
                    <div class="metric-value">{{ report.network.tcp|length if report.network.tcp else 0 }}</div>
                    <div class="metric-label">TCP Connections</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="metric-card">
                    <div class="metric-value">{{ report.network.udp|length if report.network.udp else 0 }}</div>
                    <div class="metric-label">UDP Connections</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="metric-card">
                    <div class="metric-value">{{ report.network.dns|length if report.network.dns else 0 }}</div>
                    <div class="metric-label">DNS Requests</div>
                </div>
            </div>
        </div>
        
        <!-- Top Destinations -->
        {% if report.network.tcp %}
        <h4><i class="fas fa-globe me-2"></i>Top Destinations</h4>
        <div class="row">
            {% set destinations = report.network.tcp | map(attribute='dst') | list | unique %}
            {% for dest in destinations[:6] %}
            <div class="col-md-4 col-sm-6 mb-2">
                <div class="p-2 bg-light rounded">
                    <code>{{ dest }}</code>
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}
    </section>
    {% endif %}

    <!-- Recommendations -->
    <section class="report-section">
        <h2 class="section-title">
            <i class="fas fa-lightbulb me-2"></i>
            Recommendations
        </h2>
        
        <div class="row">
            <div class="col-md-12">
                {% if report.info.score >= 7 %}
                <div class="alert alert-danger border-0 shadow-sm">
                    <h5 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        High Risk - Immediate Action Required
                    </h5>
                    <ul class="mb-0">
                        <li>Quarantine the file immediately</li>
                        <li>Scan all systems that may have been exposed</li>
                        <li>Review network logs for suspicious activity</li>
                        <li>Consider incident response procedures</li>
                        <li>Update security signatures and rules</li>
                    </ul>
                </div>
                {% elif report.info.score >= 4 %}
                <div class="alert alert-warning border-0 shadow-sm">
                    <h5 class="alert-heading">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Medium Risk - Investigation Recommended
                    </h5>
                    <ul class="mb-0">
                        <li>Conduct additional analysis if possible</li>
                        <li>Monitor for similar files in the environment</li>
                        <li>Review the source and context of the file</li>
                        <li>Consider sandboxed execution for further testing</li>
                        <li>Update detection rules based on findings</li>
                    </ul>
                </div>
                {% else %}
                <div class="alert alert-success border-0 shadow-sm">
                    <h5 class="alert-heading">
                        <i class="fas fa-check-circle me-2"></i>
                        Low Risk - Standard Monitoring
                    </h5>
                    <ul class="mb-0">
                        <li>File appears to be clean or low risk</li>
                        <li>Continue standard security monitoring</li>
                        <li>Archive analysis results for future reference</li>
                        <li>Consider whitelisting if appropriate</li>
                    </ul>
                </div>
                {% endif %}
            </div>
        </div>
    </section>

    <!-- Analysis Timeline -->
    <section class="report-section">
        <h2 class="section-title">
            <i class="fas fa-clock me-2"></i>
            Analysis Timeline
        </h2>
        
        <div class="timeline">
            <div class="timeline-item">
                <h5><i class="fas fa-upload me-2"></i>File Submitted</h5>
                <p class="mb-1">{{ report.info.started }}</p>
                <small class="text-muted">Analysis initiated for {{ report.target.file.name }}</small>
            </div>
            
            <div class="timeline-item">
                <h5><i class="fas fa-play me-2"></i>Dynamic Analysis</h5>
                <p class="mb-1">Duration: {{ report.info.duration }}s</p>
                <small class="text-muted">File executed in controlled environment</small>
            </div>
            
            <div class="timeline-item">
                <h5><i class="fas fa-check me-2"></i>Analysis Complete</h5>
                <p class="mb-1">{{ report.info.ended }}</p>
                <small class="text-muted">Report generated with threat score: {{ report.info.score }}/10</small>
            </div>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth animations to metric cards
    const metricCards = document.querySelectorAll('.metric-card');
    metricCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.5s ease';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        }, index * 100);
    });
    
    // Add click handlers for expandable sections
    document.querySelectorAll('[data-bs-toggle="collapse"]').forEach(button => {
        button.addEventListener('click', function() {
            const icon = this.querySelector('i');
            if (icon) {
                setTimeout(() => {
                    if (this.getAttribute('aria-expanded') === 'true') {
                        icon.classList.remove('fa-plus');
                        icon.classList.add('fa-minus');
                    } else {
                        icon.classList.remove('fa-minus');
                        icon.classList.add('fa-plus');
                    }
                }, 150);
            }
        });
    });
});
</script>
{% endblock %}
