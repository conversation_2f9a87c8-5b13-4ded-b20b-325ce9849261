#!/usr/bin/env python3
"""
Test script for CAPE Web Service API
"""

import requests
import time
import json
import os

# Configuration
BASE_URL = "http://localhost:5000"
TEST_FILE = "test_sample.txt"  # Create a test file

def create_test_file():
    """Create a simple test file"""
    with open(TEST_FILE, 'w') as f:
        f.write("This is a test file for CAPE Web Service\n")
        f.write("X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*\n")
    print(f"✓ Created test file: {TEST_FILE}")

def test_upload():
    """Test file upload via API"""
    print("\n🧪 Testing API upload...")
    
    if not os.path.exists(TEST_FILE):
        create_test_file()
    
    url = f"{BASE_URL}/api/analyze"
    
    with open(TEST_FILE, 'rb') as f:
        files = {'file': (TEST_FILE, f)}
        data = {
            'options': 'procmemdump=1,unpacker=2',
            'force_reanalyze': 'true'
        }
        
        try:
            response = requests.post(url, files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                if not result.get('error'):
                    analysis_id = result['analysis_id']
                    print(f"✓ Upload successful! Analysis ID: {analysis_id}")
                    return analysis_id
                else:
                    print(f"✗ Upload failed: {result.get('message')}")
            else:
                print(f"✗ HTTP Error {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"✗ Error: {e}")
    
    return None

def test_status(analysis_id):
    """Test status check via API"""
    print(f"\n🔍 Testing status check for {analysis_id}...")
    
    url = f"{BASE_URL}/api/status/{analysis_id}"
    
    try:
        response = requests.get(url)
        
        if response.status_code == 200:
            result = response.json()
            if not result.get('error'):
                status = result['status']
                print(f"✓ Status: {status}")
                return status
            else:
                print(f"✗ Status check failed: {result.get('message')}")
        else:
            print(f"✗ HTTP Error {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"✗ Error: {e}")
    
    return None

def test_report(analysis_id):
    """Test report retrieval via API"""
    print(f"\n📄 Testing report retrieval for {analysis_id}...")
    
    url = f"{BASE_URL}/api/report/{analysis_id}"
    
    try:
        response = requests.get(url)
        
        if response.status_code == 200:
            result = response.json()
            if not result.get('error'):
                print("✓ Report retrieved successfully!")
                print(f"   Analysis ID: {result['analysis_id']}")
                if result.get('result'):
                    print(f"   Task ID: {result['result'].get('task_id')}")
                    print(f"   File Hash: {result['result'].get('file_hash')}")
                    print(f"   Was Cached: {result['result'].get('was_cached')}")
                return True
            else:
                print(f"✗ Report retrieval failed: {result.get('message')}")
        else:
            print(f"✗ HTTP Error {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"✗ Error: {e}")
    
    return False

def main():
    """Main test function"""
    print("🧪 CAPE Web Service API Test")
    print("=" * 40)
    
    # Test upload
    analysis_id = test_upload()
    if not analysis_id:
        print("❌ Upload test failed, stopping...")
        return
    
    # Test status check
    status = test_status(analysis_id)
    if not status:
        print("❌ Status test failed, stopping...")
        return
    
    # Wait a bit if analysis is running
    if status in ['pending', 'running']:
        print("⏳ Analysis is running, waiting 30 seconds...")
        time.sleep(30)
        status = test_status(analysis_id)
    
    # Test report retrieval
    if status == 'completed':
        test_report(analysis_id)
    else:
        print(f"⚠️  Analysis status is '{status}', skipping report test")
    
    # Cleanup
    if os.path.exists(TEST_FILE):
        os.remove(TEST_FILE)
        print(f"🧹 Cleaned up test file: {TEST_FILE}")
    
    print("\n✅ API tests completed!")

if __name__ == '__main__':
    main()
