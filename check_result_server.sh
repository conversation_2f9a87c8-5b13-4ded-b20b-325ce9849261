#!/bin/bash
echo "=== CHECKING RESULT SERVER ==="

# Check port 2042
echo "1. Result Server Port:"
netstat -tlnp | grep :2042
if [ $? -eq 0 ]; then
    echo "✓ Result server listening on port 2042"
else
    echo "❌ Result server NOT listening on port 2042"
fi

# Check from VM perspective
echo ""
echo "2. Testing from VM IPs:"
for vm_ip in *************** ***************; do
    echo "Testing connection TO $vm_ip FROM host:"
    timeout 3 bash -c "echo test > /dev/tcp/$vm_ip/8000" 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "✓ Can connect to $vm_ip:8000"
    else
        echo "❌ Cannot connect to $vm_ip:8000"
    fi
done

# Check cuckoo.py process
echo ""
echo "3. Cuckoo Process:"
ps aux | grep cuckoo.py | grep -v grep