# Fix Bridge Already In Use Error

## Lỗi gặp phải:
```
error: Failed to define network from /tmp/cape-nat.xml
error: internal error: bridge name 'virbr1' already in use.
```

## Nguyên nhân:
Bridge interface `virbr1` đã được sử dụng bởi network khác (thường là `default` network của libvirt).

## GIẢI PHÁP 1: Xóa networks cũ và tạo lại (🖥️ CHẠY TRÊN HOST)

### Bước 1: Kiểm tra networks hiện tại
```bash
# Xem tất cả networks
sudo virsh net-list --all

# Xem chi tiết network nào đang dùng virbr1
sudo virsh net-dumpxml default 2>/dev/null | grep virbr1 || echo "default not using virbr1"
sudo virsh net-dumpxml cape-hostonly 2>/dev/null | grep virbr1 || echo "cape-hostonly not using virbr1"
sudo virsh net-dumpxml cape-network 2>/dev/null | grep virbr1 || echo "cape-network not using virbr1"
```

### Bước 2: Stop và xóa tất cả networks cũ
```bash
# Stop tất cả networks
sudo virsh net-destroy default 2>/dev/null || true
sudo virsh net-destroy cape-hostonly 2>/dev/null || true
sudo virsh net-destroy cape-network 2>/dev/null || true

# Undefine (xóa) tất cả networks
sudo virsh net-undefine default 2>/dev/null || true
sudo virsh net-undefine cape-hostonly 2>/dev/null || true
sudo virsh net-undefine cape-network 2>/dev/null || true

# Verify không còn network nào
sudo virsh net-list --all
```

### Bước 3: Xóa bridge interface thủ công (nếu cần)
```bash
# Check bridge hiện tại
brctl show

# Xóa virbr1 nếu còn tồn tại
sudo ip link set virbr1 down 2>/dev/null || true
sudo brctl delbr virbr1 2>/dev/null || true

# Verify bridge đã bị xóa
brctl show | grep virbr1 || echo "virbr1 removed successfully"
```

### Bước 4: Tạo NAT network mới
```bash
# Tạo network XML
cat > /tmp/cape-nat.xml << 'EOF'
<network>
  <name>cape-nat</name>
  <forward mode='nat'>
    <nat>
      <port start='1024' end='65535'/>
    </nat>
  </forward>
  <bridge name='virbr1' stp='on' delay='0'/>
  <ip address='*************' netmask='*************'>
    <dhcp>
      <range start='**************' end='***************'/>
    </dhcp>
  </ip>
</network>
EOF

# Define network
sudo virsh net-define /tmp/cape-nat.xml

# Start network
sudo virsh net-start cape-nat

# Enable autostart
sudo virsh net-autostart cape-nat

# Verify network created
sudo virsh net-list --all
sudo virsh net-dumpxml cape-nat
```

## GIẢI PHÁP 2: Sử dụng bridge name khác (🖥️ CHẠY TRÊN HOST)

### Nếu không muốn xóa networks cũ, dùng bridge khác:
```bash
# Tạo network với bridge virbr2
cat > /tmp/cape-nat.xml << 'EOF'
<network>
  <name>cape-nat</name>
  <forward mode='nat'>
    <nat>
      <port start='1024' end='65535'/>
    </nat>
  </forward>
  <bridge name='virbr2' stp='on' delay='0'/>
  <ip address='*************' netmask='*************'>
    <dhcp>
      <range start='**************' end='***************'/>
    </dhcp>
  </ip>
</network>
EOF

# Define và start
sudo virsh net-define /tmp/cape-nat.xml
sudo virsh net-start cape-nat
sudo virsh net-autostart cape-nat

# Verify
sudo virsh net-list --all
```

## BƯỚC TIẾP THEO: Update VM Networks (🖥️ CHẠY TRÊN HOST)

### Sau khi tạo network thành công:
```bash
# Shutdown VMs
sudo virsh shutdown cape1
sudo virsh shutdown cuckoo1
sleep 15

# Detach old network interfaces
sudo virsh detach-interface cape1 network --config 2>/dev/null || true
sudo virsh detach-interface cuckoo1 network --config 2>/dev/null || true

# Attach new NAT network
sudo virsh attach-interface cape1 network cape-nat --model virtio --config
sudo virsh attach-interface cuckoo1 network cape-nat --model virtio --config

# Start VMs
sudo virsh start cape1
sudo virsh start cuckoo1

# Wait for VMs to boot
sleep 60

# Verify VMs are running
sudo virsh list
```

## BƯỚC TIẾP THEO: Setup NAT Rules (🖥️ CHẠY TRÊN HOST)

```bash
# Enable IP forwarding
echo 1 | sudo tee /proc/sys/net/ipv4/ip_forward
echo 'net.ipv4.ip_forward=1' | sudo tee -a /etc/sysctl.conf

# Remove old blocking rules
sudo iptables -D OUTPUT -p udp --dport 53 -j DROP 2>/dev/null || true
sudo iptables -D OUTPUT -p tcp --dport 53 -j DROP 2>/dev/null || true
sudo iptables -D OUTPUT -p tcp --dport 80 -j DROP 2>/dev/null || true
sudo iptables -D OUTPUT -p tcp --dport 443 -j DROP 2>/dev/null || true
sudo iptables -D OUTPUT -j DROP 2>/dev/null || true

# Add NAT rules
sudo iptables -t nat -A POSTROUTING -s *************/24 -o enp4s0 -j MASQUERADE
sudo iptables -A FORWARD -s *************/24 -o enp4s0 -j ACCEPT
sudo iptables -A FORWARD -d *************/24 -i enp4s0 -m state --state RELATED,ESTABLISHED -j ACCEPT

# Allow VM communication
sudo iptables -A INPUT -s *************/24 -j ACCEPT
sudo iptables -A OUTPUT -d *************/24 -j ACCEPT

# Save rules
sudo iptables-save | sudo tee /etc/iptables/rules.v4

# Verify NAT rules
echo "NAT rules:"
sudo iptables -t nat -L -n | grep 192.168.100
echo "FORWARD rules:"
sudo iptables -L FORWARD -n | grep 192.168.100
```

## BƯỚC TIẾP THEO: Configure VMs

### 💻 Trong cape1 (Windows 10):
```cmd
REM Command Prompt as Administrator
netsh interface ip set address name="Ethernet" static *************01 ************* *************
netsh interface ip set dns name="Ethernet" static *******
netsh interface ip add dns name="Ethernet" ******* index=2
ipconfig /flushdns
ping -n 1 *************
ping -n 1 *******
ping -n 1 google.com
```

### 💻 Trong cuckoo1 (Windows 7):
```cmd
REM Command Prompt as Administrator
netsh interface ip set address name="Local Area Connection" static *************02 ************* *************
netsh interface ip set dns name="Local Area Connection" static *******
netsh interface ip add dns name="Local Area Connection" ******* index=2
ipconfig /flushdns
ping -n 1 *************
ping -n 1 *******
ping -n 1 google.com
```

## TROUBLESHOOTING

### Nếu vẫn gặp lỗi bridge:
```bash
# Force remove all bridges
sudo systemctl stop libvirtd
sudo ip link delete virbr1 2>/dev/null || true
sudo ip link delete virbr0 2>/dev/null || true
sudo systemctl start libvirtd

# Recreate network
sudo virsh net-define /tmp/cape-nat.xml
sudo virsh net-start cape-nat
```

### Nếu VMs không boot:
```bash
# Check VM status
sudo virsh list --all

# Force start VMs
sudo virsh start cape1 --force-boot
sudo virsh start cuckoo1 --force-boot

# Check VM console
sudo virsh console cape1
```

### Nếu không ping được:
```bash
# Check bridge status
brctl show
ip addr show virbr1

# Check iptables
sudo iptables -L -n
sudo iptables -t nat -L -n

# Restart network
sudo virsh net-destroy cape-nat
sudo virsh net-start cape-nat
```

## SCRIPT HOÀN CHỈNH FIX LỖI:

```bash
#!/bin/bash
# Complete fix for bridge already in use

echo "=== FIXING BRIDGE ALREADY IN USE ==="

# 1. Stop all networks
echo "1. Stopping all networks..."
sudo virsh net-destroy default 2>/dev/null || true
sudo virsh net-destroy cape-hostonly 2>/dev/null || true
sudo virsh net-destroy cape-network 2>/dev/null || true

# 2. Undefine all networks
echo "2. Removing all networks..."
sudo virsh net-undefine default 2>/dev/null || true
sudo virsh net-undefine cape-hostonly 2>/dev/null || true
sudo virsh net-undefine cape-network 2>/dev/null || true

# 3. Remove bridge
echo "3. Removing bridge..."
sudo ip link set virbr1 down 2>/dev/null || true
sudo brctl delbr virbr1 2>/dev/null || true

# 4. Create new network
echo "4. Creating new NAT network..."
cat > /tmp/cape-nat.xml << 'EOF'
<network>
  <name>cape-nat</name>
  <forward mode='nat'>
    <nat>
      <port start='1024' end='65535'/>
    </nat>
  </forward>
  <bridge name='virbr1' stp='on' delay='0'/>
  <ip address='*************' netmask='*************'>
    <dhcp>
      <range start='**************' end='***************'/>
    </dhcp>
  </ip>
</network>
EOF

sudo virsh net-define /tmp/cape-nat.xml
sudo virsh net-start cape-nat
sudo virsh net-autostart cape-nat

echo "✅ Bridge fix completed!"
echo "Continue with VM network update..."
```

## KẾT QUẢ MONG ĐỢI:

✅ **Sau khi fix:**
- Network cape-nat được tạo thành công
- Bridge virbr1 hoạt động bình thường
- VMs có thể attach vào network mới
- Internet access được restore

❌ **Trước khi fix:**
- Lỗi "bridge name already in use"
- Không thể tạo network mới
- VMs không có internet

**Lỗi bridge sẽ được fix hoàn toàn!** 🚀
