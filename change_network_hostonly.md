# Hướng dẫn chuyển network từ NAT sang Host-Only

## Tổng quan
Tài liệu này hướng dẫn chuyển network virbr1 từ NAT (có internet) sang Host-Only (không internet, chỉ ping được từ host).

## BƯỚC 1: <PERSON><PERSON><PERSON> tra cấu hình hiện tại

### 1.1. Xem network hiện tại
```bash
# Xem danh sách networks
sudo virsh net-list --all

# Xem cấu hình cape-network hiện tại
sudo virsh net-dumpxml cape-network

# Kiểm tra bridge interface
ip addr show virbr1
brctl show virbr1
```

### 1.2. Test connectivity hiện tại
```bash
# Ping từ host tới VMs
ping -c 3 ***************  # cape1
ping -c 3 ***************  # cuckoo1

# Trong VMs, test internet (sẽ thành công)
# ping *******
```

## BƯỚC 2: Tạo Host-Only Network mới

### 2.1. Tạo file XML cho host-only network
```bash
# Tạo file network XML mới
cat > /tmp/cape-hostonly.xml << 'EOF'
<network>
  <name>cape-hostonly</name>
  <bridge name='virbr1' stp='on' delay='0'/>
  <ip address='*************' netmask='*************'>
    <dhcp>
      <range start='**************' end='***************'/>
    </dhcp>
  </ip>
</network>
EOF

# Lưu ý: Không có thẻ <forward> nghĩa là host-only
```

### 2.2. Backup và thay thế network
```bash
# Backup cấu hình network cũ
sudo virsh net-dumpxml cape-network > /tmp/cape-network-backup.xml

# Stop network cũ
sudo virsh net-destroy cape-network

# Undefine network cũ
sudo virsh net-undefine cape-network

# Define network mới
sudo virsh net-define /tmp/cape-hostonly.xml

# Start network mới
sudo virsh net-start cape-hostonly

# Auto-start network
sudo virsh net-autostart cape-hostonly

# Kiểm tra network mới
sudo virsh net-list --all
sudo virsh net-dumpxml cape-hostonly
```

## BƯỚC 3: Cập nhật cấu hình VMs

### 3.1. Cập nhật network interface của VMs
```bash
# Shutdown VMs trước khi thay đổi
sudo virsh shutdown cape1
sudo virsh shutdown cuckoo1

# Đợi VMs shutdown hoàn toàn
sleep 15

# Edit cape1 network
sudo virsh edit cape1
# Tìm section <interface type='network'>
# Thay đổi: <source network='cape-network'/> 
# Thành: <source network='cape-hostonly'/>

# Edit cuckoo1 network
sudo virsh edit cuckoo1
# Thực hiện thay đổi tương tự
```

### 3.2. Hoặc sử dụng lệnh attach/detach
```bash
# Detach network interface cũ
sudo virsh detach-interface cape1 network --config
sudo virsh detach-interface cuckoo1 network --config

# Attach network interface mới
sudo virsh attach-interface cape1 network cape-hostonly --model virtio --config
sudo virsh attach-interface cuckoo1 network cape-hostonly --model virtio --config
```

## BƯỚC 4: Xóa iptables rules NAT

### 4.1. Xóa NAT rules
```bash
# Xem iptables rules hiện tại
sudo iptables -t nat -L -n -v

# Xóa NAT rules cho subnet *************/24
sudo iptables -t nat -D POSTROUTING -s *************/24 -o enp4s0 -j MASQUERADE

# Xóa forward rules
sudo iptables -D FORWARD -s *************/24 -o enp4s0 -j ACCEPT
sudo iptables -D FORWARD -d *************/24 -i enp4s0 -m state --state RELATED,ESTABLISHED -j ACCEPT

# Lưu iptables rules
sudo iptables-save | sudo tee /etc/iptables/rules.v4
```

### 4.2. Script xóa NAT rules
```bash
#!/bin/bash
# File: remove_nat_rules.sh

echo "Removing NAT rules for *************/24..."

# Remove NAT rules
iptables -t nat -D POSTROUTING -s *************/24 -o enp4s0 -j MASQUERADE 2>/dev/null || true

# Remove forward rules
iptables -D FORWARD -s *************/24 -o enp4s0 -j ACCEPT 2>/dev/null || true
iptables -D FORWARD -d *************/24 -i enp4s0 -m state --state RELATED,ESTABLISHED -j ACCEPT 2>/dev/null || true

# Allow traffic between host and VMs
iptables -A INPUT -s *************/24 -j ACCEPT
iptables -A OUTPUT -d *************/24 -j ACCEPT

# Save rules
iptables-save > /etc/iptables/rules.v4

echo "NAT rules removed. VMs now in host-only mode."
```

## BƯỚC 5: Start VMs và test

### 5.1. Start VMs
```bash
# Start VMs
sudo virsh start cape1
sudo virsh start cuckoo1

# Đợi VMs boot xong
sleep 120

# Kiểm tra VMs đã start
sudo virsh list
```

### 5.2. Test connectivity
```bash
# Test ping từ host tới VMs (phải thành công)
ping -c 3 ***************  # cape1
ping -c 3 ***************  # cuckoo1

# Kiểm tra interface trên host
ip addr show virbr1

# Kiểm tra routing table
ip route show | grep 192.168.100
```

### 5.3. Test trong VMs
```bash
# Trong VMs (qua VNC hoặc console):
# Test ping tới host (phải thành công)
ping *************

# Test ping tới internet (phải thất bại)
ping *******

# Test DNS (phải thất bại)
nslookup google.com
```

## BƯỚC 6: Cập nhật cấu hình CAPEv2

### 6.1. Cập nhật routing.conf
```bash
# Edit routing.conf
sudo nano /opt/CAPEv2/conf/routing.conf

# Thay đổi:
[routing]
# Disable internet routing
route = none

# Comment out internet interface
# internet = enp4s0

# Disable NAT
nat = no

# Enable drop route để chặn hoàn toàn
drop = yes
```

### 6.2. Cập nhật auxiliary.conf (nếu cần)
```bash
# auxiliary.conf vẫn giữ nguyên
[sniffer]
enabled = yes
interface = virbr1
tcpdump = /usr/bin/tcpdump
bpf = not arp
```

## BƯỚC 7: Script tự động chuyển đổi

### 7.1. Script chuyển sang host-only
```bash
#!/bin/bash
# File: switch_to_hostonly.sh

echo "Switching CAPE network to host-only mode..."

# Stop VMs
echo "Stopping VMs..."
sudo virsh shutdown cape1 cuckoo1
sleep 15

# Backup current network
echo "Backing up current network..."
sudo virsh net-dumpxml cape-network > /tmp/cape-network-backup.xml 2>/dev/null || true

# Stop and remove old network
echo "Removing old network..."
sudo virsh net-destroy cape-network 2>/dev/null || true
sudo virsh net-undefine cape-network 2>/dev/null || true

# Create host-only network
echo "Creating host-only network..."
cat > /tmp/cape-hostonly.xml << 'EOF'
<network>
  <name>cape-hostonly</name>
  <bridge name='virbr1' stp='on' delay='0'/>
  <ip address='*************' netmask='*************'>
    <dhcp>
      <range start='**************' end='***************'/>
    </dhcp>
  </ip>
</network>
EOF

sudo virsh net-define /tmp/cape-hostonly.xml
sudo virsh net-start cape-hostonly
sudo virsh net-autostart cape-hostonly

# Update VM network interfaces
echo "Updating VM network interfaces..."
for vm in cape1 cuckoo1; do
    sudo virsh detach-interface $vm network --config 2>/dev/null || true
    sudo virsh attach-interface $vm network cape-hostonly --model virtio --config
done

# Remove NAT rules
echo "Removing NAT rules..."
sudo iptables -t nat -D POSTROUTING -s *************/24 -o enp4s0 -j MASQUERADE 2>/dev/null || true
sudo iptables -D FORWARD -s *************/24 -o enp4s0 -j ACCEPT 2>/dev/null || true
sudo iptables -D FORWARD -d *************/24 -i enp4s0 -m state --state RELATED,ESTABLISHED -j ACCEPT 2>/dev/null || true

# Allow host-VM communication
sudo iptables -A INPUT -s *************/24 -j ACCEPT 2>/dev/null || true
sudo iptables -A OUTPUT -d *************/24 -j ACCEPT 2>/dev/null || true

# Save iptables
sudo iptables-save | sudo tee /etc/iptables/rules.v4 >/dev/null

# Start VMs
echo "Starting VMs..."
sudo virsh start cape1 cuckoo1

echo "Network switched to host-only mode successfully!"
echo "VMs can now only communicate with host, no internet access."
```

### 7.2. Script test connectivity
```bash
#!/bin/bash
# File: test_hostonly.sh

echo "Testing host-only network connectivity..."
echo "========================================"

# Test host to VMs
echo "Testing host to VMs:"
for ip in *************** ***************; do
    if ping -c 1 -W 2 $ip >/dev/null 2>&1; then
        echo "✓ Host -> $ip: SUCCESS"
    else
        echo "✗ Host -> $ip: FAILED"
    fi
done

# Check network interface
echo ""
echo "Network interface virbr1:"
ip addr show virbr1 | grep "inet " || echo "✗ virbr1 not found"

# Check network
echo ""
echo "Network status:"
sudo virsh net-list | grep hostonly || echo "✗ cape-hostonly network not found"

# Check iptables
echo ""
echo "NAT rules check:"
if sudo iptables -t nat -L | grep -q "192.168.100"; then
    echo "⚠ NAT rules still exist"
else
    echo "✓ NAT rules removed"
fi

echo ""
echo "Test completed!"
echo "VMs should NOT have internet access now."
```

## BƯỚC 8: Sử dụng

```bash
# Chạy script chuyển đổi
chmod +x switch_to_hostonly.sh
sudo ./switch_to_hostonly.sh

# Test connectivity
chmod +x test_hostonly.sh
./test_hostonly.sh

# Kiểm tra thủ công
ping ***************
ping ***************

# Trong VMs, test không có internet:
# ping ******* (phải thất bại)
```

## Khôi phục NAT (nếu cần)

```bash
# Restore network từ backup
sudo virsh net-define /tmp/cape-network-backup.xml
sudo virsh net-start cape-network

# Restore NAT rules
sudo iptables -t nat -A POSTROUTING -s *************/24 -o enp4s0 -j MASQUERADE
sudo iptables -A FORWARD -s *************/24 -o enp4s0 -j ACCEPT
```
