# Setup Agent trong VMs - Result Server đã sẵn sàng

## Tình trạng hiện tại:
✅ Host IP: ************* có trên virbr1  
✅ Result server: listening trên 0.0.0.0:2042  
✅ Network: VMs có thể ping host  
❌ **Agent chưa chạy trong VMs**

## BƯỚC 1: Tạo Agent Setup ISO hoàn chỉnh

### 1.1. Script tạo agent setup
```bash
#!/bin/bash
# File: create_complete_agent_setup.sh

CAPE_DIR="/opt/CAPEv2"

echo "Creating complete agent setup for VMs..."

# Tạo thư mục setup
rm -rf /tmp/cape-agent-complete
mkdir -p /tmp/cape-agent-complete

# Copy agent files
echo "Copying agent files..."
cp $CAPE_DIR/agent/agent.py /tmp/cape-agent-complete/
cp -r $CAPE_DIR/agent/*.py /tmp/cape-agent-complete/ 2>/dev/null || true

# Tạo connection test script
cat > /tmp/cape-agent-complete/test_connection.py << 'EOF'
#!/usr/bin/env python3
import socket
import sys
import time

def test_connection():
    print("=== CAPE Agent Connection Test ===")
    print(f"Python version: {sys.version}")
    
    host = "*************"
    port = 2042
    
    print(f"Testing connection to {host}:{port}...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        result = sock.connect_ex((host, port))
        
        if result == 0:
            print("✅ SUCCESS: Can connect to result server!")
            
            # Test sending data
            try:
                test_msg = b"CAPE_TEST\n"
                sock.send(test_msg)
                print("✅ SUCCESS: Can send data!")
                return True
            except Exception as e:
                print(f"⚠️  Connected but cannot send: {e}")
                return False
        else:
            print(f"❌ FAILED: Cannot connect (error: {result})")
            return False
            
    except Exception as e:
        print(f"❌ FAILED: {e}")
        return False
    finally:
        try:
            sock.close()
        except:
            pass

if __name__ == "__main__":
    success = test_connection()
    input("Press Enter to continue...")
    sys.exit(0 if success else 1)
EOF

# Tạo Windows install script
cat > /tmp/cape-agent-complete/install_agent.bat << 'EOF'
@echo off
echo === CAPE Agent Installation ===

REM Check if running as Administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Please run as Administrator!
    pause
    exit /b 1
)

REM Check Python
echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found!
    echo Please install Python 3.8+ from python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo ✅ Python found

REM Create agent directory
echo Creating agent directory...
if not exist "C:\cape-agent" mkdir C:\cape-agent

REM Copy files
echo Copying agent files...
copy *.py C:\cape-agent\
if %errorlevel% neq 0 (
    echo ERROR: Failed to copy files
    pause
    exit /b 1
)

echo ✅ Files copied

REM Test connection
echo Testing connection to result server...
cd C:\cape-agent
python test_connection.py

REM Create startup script
echo Creating startup script...
echo @echo off > C:\cape-agent\start_agent.bat
echo cd C:\cape-agent >> C:\cape-agent\start_agent.bat
echo echo Starting CAPE Agent... >> C:\cape-agent\start_agent.bat
echo python agent.py ************* 2042 >> C:\cape-agent\start_agent.bat

REM Create debug startup script
echo @echo off > C:\cape-agent\start_agent_debug.bat
echo cd C:\cape-agent >> C:\cape-agent\start_agent_debug.bat
echo echo Starting CAPE Agent with debug... >> C:\cape-agent\start_agent_debug.bat
echo python agent.py ************* 2042 >> C:\cape-agent\start_agent_debug.bat
echo echo Agent stopped. Press any key... >> C:\cape-agent\start_agent_debug.bat
echo pause >> C:\cape-agent\start_agent_debug.bat

REM Add to startup folder
echo Adding to Windows startup...
copy C:\cape-agent\start_agent.bat "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\" >nul 2>&1

echo.
echo === Installation Complete ===
echo.
echo Agent installed to: C:\cape-agent
echo Startup script: C:\cape-agent\start_agent.bat
echo Debug script: C:\cape-agent\start_agent_debug.bat
echo.
echo Agent will start automatically on next boot.
echo To start now, run: C:\cape-agent\start_agent_debug.bat
echo.
pause
EOF

# Tạo manual start script
cat > /tmp/cape-agent-complete/start_agent_now.bat << 'EOF'
@echo off
echo === Starting CAPE Agent Now ===
cd C:\cape-agent

echo Testing connection first...
python test_connection.py

echo.
echo Starting agent (Ctrl+C to stop)...
python agent.py ************* 2042
EOF

# Tạo README
cat > /tmp/cape-agent-complete/README.txt << 'EOF'
CAPE Agent Setup Instructions
============================

1. INSTALL:
   - Run install_agent.bat as Administrator
   - This will install agent and add to Windows startup

2. TEST CONNECTION:
   - Run test_connection.py to verify connectivity
   - Should show "SUCCESS: Can connect to result server!"

3. START AGENT:
   - Manual: Run start_agent_now.bat
   - Debug: Run start_agent_debug.bat (shows output)
   - Auto: Agent starts automatically on boot

4. VERIFY:
   - Agent should connect to *************:2042
   - Check host logs for agent connection

Files:
- agent.py: Main agent program
- test_connection.py: Connection test
- install_agent.bat: Installation script
- start_agent_now.bat: Start agent manually
- start_agent_debug.bat: Start with debug output

Troubleshooting:
- Ensure Python is installed and in PATH
- Ensure Windows Firewall allows Python
- Ensure VM can ping *************
- Check host result server is running on port 2042
EOF

# Tạo ISO
echo "Creating ISO..."
genisoimage -o /var/lib/libvirt/images/iso/cape-agent-complete.iso -V "CAPE_AGENT" -r -J /tmp/cape-agent-complete/

echo "✅ Complete agent setup ISO created: /var/lib/libvirt/images/iso/cape-agent-complete.iso"
echo ""
echo "Next steps:"
echo "1. Attach ISO to VMs"
echo "2. Install agent in each VM"
echo "3. Test and start agent"
```

## BƯỚC 2: Monitor agent connections

### 2.1. Real-time connection monitor
```bash
#!/bin/bash
# File: monitor_agent_realtime.sh

echo "=== MONITORING AGENT CONNECTIONS ==="
echo "Watching for agent connections to result server..."
echo "Press Ctrl+C to stop"
echo ""

# Function to show current status
show_status() {
    local timestamp=$(date '+%H:%M:%S')
    echo "[$timestamp] Status:"
    
    # Show active connections to port 2042
    local connections=$(netstat -an | grep :2042 | grep ESTABLISHED)
    if [ -n "$connections" ]; then
        echo "✅ Active agent connections:"
        echo "$connections" | while read line; do
            echo "  $line"
        done
    else
        echo "❌ No agent connections"
    fi
    
    # Show recent log entries
    echo "Recent logs:"
    tail -3 /opt/CAPEv2/log/cuckoo.log 2>/dev/null | grep -v "^$" | sed 's/^/  /'
    
    echo "----------------------------------------"
}

# Show initial status
show_status

# Monitor in loop
while true; do
    sleep 5
    show_status
done
```

### 2.2. Test analysis with agent monitoring
```bash
#!/bin/bash
# File: test_analysis_with_monitoring.sh

CAPE_DIR="/opt/CAPEv2"

echo "=== TESTING ANALYSIS WITH AGENT MONITORING ==="

# 1. Check current agent connections
echo "1. Current agent connections:"
netstat -an | grep :2042 | grep ESTABLISHED || echo "No active connections"

# 2. Create simple test file
echo ""
echo "2. Creating test file..."
cat > /tmp/cape_test.py << 'EOF'
#!/usr/bin/env python3
import os
import time

print("CAPE Test Script Starting...")

# Create a test file
with open("cape_test_output.txt", "w") as f:
    f.write("CAPE behavior analysis test")

print("Test file created")
time.sleep(10)
print("CAPE Test Script Completed")
EOF

chmod +x /tmp/cape_test.py

# 3. Submit analysis
echo ""
echo "3. Submitting test analysis..."
cd $CAPE_DIR
python3 utils/submit.py --timeout 60 /tmp/cape_test.py

# 4. Monitor for a bit
echo ""
echo "4. Monitoring for 30 seconds..."
for i in {1..6}; do
    echo "[$i/6] Checking connections..."
    netstat -an | grep :2042 | grep ESTABLISHED || echo "  No connections"
    sleep 5
done

echo ""
echo "5. Check results at: http://localhost:8000"
echo "   Look for latest analysis and check Behavior section"
```

## BƯỚC 3: VM Setup Instructions

### 3.1. Attach ISO to VMs
```bash
#!/bin/bash
# File: attach_agent_iso.sh

echo "Attaching agent ISO to VMs..."

# Attach to cape1
echo "Attaching to cape1..."
sudo virsh attach-disk cape1 /var/lib/libvirt/images/iso/cape-agent-complete.iso hdc --type cdrom --mode readonly --config

# Attach to cuckoo1
echo "Attaching to cuckoo1..."
sudo virsh attach-disk cuckoo1 /var/lib/libvirt/images/iso/cape-agent-complete.iso hdc --type cdrom --mode readonly --config

# Verify attachments
echo ""
echo "Verifying attachments:"
echo "cape1 disks:"
sudo virsh domblklist cape1

echo ""
echo "cuckoo1 disks:"
sudo virsh domblklist cuckoo1

echo ""
echo "✅ ISO attached to both VMs"
echo ""
echo "Next steps IN EACH VM:"
echo "1. Open File Explorer, go to CD drive"
echo "2. Run install_agent.bat as Administrator"
echo "3. Follow the installation prompts"
echo "4. Test with start_agent_now.bat"
```

### 3.2. Manual steps trong VMs

#### Trong cape1 (Windows 10):
```
1. Mở File Explorer
2. Vào CD drive (thường là D: hoặc E:)
3. Right-click install_agent.bat → "Run as administrator"
4. Follow prompts:
   - Should show "✅ Python found"
   - Should show "✅ Files copied"
   - Should test connection successfully
5. Test manual: Double-click start_agent_now.bat
6. Should see: "Starting agent..." và agent chạy
```

#### Trong cuckoo1 (Windows 7):
```
Làm tương tự như cape1
```

## BƯỚC 4: Complete setup script

```bash
#!/bin/bash
# File: complete_agent_setup.sh

echo "=== COMPLETE AGENT SETUP ==="

# 1. Create agent ISO
echo "Step 1: Creating agent setup ISO..."
./create_complete_agent_setup.sh

# 2. Attach to VMs
echo ""
echo "Step 2: Attaching ISO to VMs..."
./attach_agent_iso.sh

# 3. Start monitoring
echo ""
echo "Step 3: Starting connection monitor..."
echo "Monitor will run in background. Check agent_monitor.log for output."
./monitor_agent_realtime.sh > agent_monitor.log 2>&1 &
MONITOR_PID=$!

echo "Monitor PID: $MONITOR_PID"
echo "To stop monitor: kill $MONITOR_PID"

echo ""
echo "=== SETUP COMPLETED ==="
echo ""
echo "MANUAL STEPS REQUIRED:"
echo ""
echo "IN EACH VM (cape1 and cuckoo1):"
echo "1. Open File Explorer, go to CD drive"
echo "2. Run install_agent.bat as Administrator"
echo "3. Test with start_agent_now.bat"
echo "4. Verify agent connects successfully"
echo ""
echo "THEN TEST:"
echo "./test_analysis_with_monitoring.sh"
echo ""
echo "Monitor log: tail -f agent_monitor.log"
```

## CÁCH SỬ DỤNG:

```bash
# 1. Setup hoàn chỉnh
chmod +x *.sh
./complete_agent_setup.sh

# 2. Trong VMs: Install agent theo hướng dẫn

# 3. Test analysis
./test_analysis_with_monitoring.sh

# 4. Monitor connections
tail -f agent_monitor.log
```

## KẾT QUẢ MONG ĐỢI:

✅ **Trong VMs sẽ thấy:**
```
✅ SUCCESS: Can connect to result server!
Starting CAPE Agent...
Agent connected to *************:2042
```

✅ **Monitor sẽ thấy:**
```
✅ Active agent connections:
tcp 0 0 *************:2042 *************01:xxxxx ESTABLISHED
tcp 0 0 *************:2042 *************02:xxxxx ESTABLISHED
```

✅ **Behavior Analysis sẽ có data!**

Result server đã sẵn sàng, chỉ cần setup agent trong VMs!
