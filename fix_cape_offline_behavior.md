# FIX CAPEv2 - Block DNS/VT và Enable Behavior Analysis

## Vấn đề hiện tại:
1. **Vẫn leak DNS/VT** - CAPEv2 vẫn cố gắng query DNS và VirusTotal
2. **Behavior analysis không chạy** - Agent không hoạt động trong VMs

## GIẢI PHÁP 1: BLOCK HOÀN TOÀN DNS/VT

### 1.1. Script block tất cả external connections
```bash
#!/bin/bash
# File: block_all_external.sh

echo "BLOCKING ALL EXTERNAL CONNECTIONS..."

# Flush existing OUTPUT rules
iptables -F OUTPUT

# Allow loopback
iptables -A OUTPUT -o lo -j ACCEPT

# Allow local networks only
iptables -A OUTPUT -d *********/8 -j ACCEPT
iptables -A OUTPUT -d *************/24 -j ACCEPT
iptables -A OUTPUT -d ***********/24 -j ACCEPT
iptables -A OUTPUT -d 10.0.0.0/8 -j ACCEPT

# BLOCK DNS completely (port 53)
iptables -A OUTPUT -p udp --dport 53 -j REJECT --reject-with icmp-port-unreachable
iptables -A OUTPUT -p tcp --dport 53 -j REJECT --reject-with tcp-reset

# BLOCK HTTP/HTTPS completely
iptables -A OUTPUT -p tcp --dport 80 -j REJECT --reject-with tcp-reset
iptables -A OUTPUT -p tcp --dport 443 -j REJECT --reject-with tcp-reset

# BLOCK all other external traffic
iptables -A OUTPUT -j REJECT --reject-with icmp-host-unreachable

# Save rules
iptables-save > /etc/iptables/rules.v4

echo "✓ ALL external connections BLOCKED!"
echo "✓ Only local networks allowed"
```

### 1.2. Block VT domains trong /etc/hosts
```bash
#!/bin/bash
# File: block_vt_domains.sh

echo "Blocking VirusTotal domains in /etc/hosts..."

# Backup hosts file
cp /etc/hosts /etc/hosts.backup

# Add VT blocks
cat >> /etc/hosts << 'EOF'

# BLOCK VirusTotal and threat intel services
127.0.0.1 www.virustotal.com
127.0.0.1 virustotal.com
127.0.0.1 api.virustotal.com
127.0.0.1 vtapi.virustotal.com
127.0.0.1 vt.virustotal.com
127.0.0.1 malwr.com
127.0.0.1 hybrid-analysis.com
127.0.0.1 joesandbox.com
127.0.0.1 threatminer.org
127.0.0.1 otx.alienvault.com
127.0.0.1 urlvoid.com
127.0.0.1 ipvoid.com
127.0.0.1 abuse.ch
127.0.0.1 malwaredomainlist.com
EOF

echo "✓ VT domains blocked in /etc/hosts"
```

### 1.3. Disable DNS trong CAPEv2 code
```bash
#!/bin/bash
# File: disable_dns_in_code.sh

CAPE_DIR="/opt/CAPEv2"

echo "Disabling DNS resolution in CAPEv2 code..."

# Disable DNS trong processing modules
find $CAPE_DIR/modules/processing -name "*.py" -exec sed -i 's/socket\.gethostbyaddr/# socket.gethostbyaddr/g' {} \;
find $CAPE_DIR/modules/processing -name "*.py" -exec sed -i 's/socket\.gethostbyname/# socket.gethostbyname/g' {} \;
find $CAPE_DIR/modules/processing -name "*.py" -exec sed -i 's/dns\.resolver/# dns.resolver/g' {} \;

# Disable VT API calls
find $CAPE_DIR/modules -name "*.py" -exec sed -i 's/requests\.get.*virustotal/# requests.get # DISABLED VT/g' {} \;
find $CAPE_DIR/modules -name "*.py" -exec sed -i 's/requests\.post.*virustotal/# requests.post # DISABLED VT/g' {} \;

# Disable network requests trong lib
find $CAPE_DIR/lib -name "*.py" -exec sed -i 's/urllib\.request/# urllib.request # DISABLED/g' {} \;

echo "✓ DNS and VT disabled in code"
```

## GIẢI PHÁP 2: FIX BEHAVIOR ANALYSIS

### 2.1. Kiểm tra agent status
```bash
#!/bin/bash
# File: check_behavior_status.sh

CAPE_DIR="/opt/CAPEv2"

echo "=== CHECKING BEHAVIOR ANALYSIS STATUS ==="

# 1. Check VMs connectivity
echo "1. VM Connectivity:"
for vm_ip in *************** ***************; do
    if ping -c 1 -W 2 $vm_ip >/dev/null 2>&1; then
        echo "✓ $vm_ip: Reachable"
    else
        echo "✗ $vm_ip: NOT reachable"
    fi
done

# 2. Check result server
echo ""
echo "2. Result Server:"
if netstat -tlnp | grep :2042 >/dev/null; then
    echo "✓ Result server listening on port 2042"
    netstat -tlnp | grep :2042
else
    echo "✗ Result server NOT listening on port 2042"
fi

# 3. Check behavior module
echo ""
echo "3. Behavior Module:"
if [ -f "$CAPE_DIR/modules/processing/behavior.py" ]; then
    echo "✓ Behavior module exists"
else
    echo "✗ Behavior module missing"
fi

# 4. Check recent analyses
echo ""
echo "4. Recent Analyses:"
if [ -f "$CAPE_DIR/log/cuckoo.log" ]; then
    echo "Last 10 log entries:"
    tail -10 $CAPE_DIR/log/cuckoo.log
else
    echo "✗ No cuckoo.log found"
fi

# 5. Check database
echo ""
echo "5. Database Check:"
cd $CAPE_DIR
python3 -c "
try:
    from lib.cuckoo.core.database import Database
    db = Database()
    tasks = db.list_tasks(limit=3)
    print(f'Found {len(tasks)} recent tasks')
    for task in tasks:
        print(f'  Task {task.id}: {task.status} - {task.target}')
except Exception as e:
    print(f'Database error: {e}')
"
```

### 2.2. Tạo agent ISO mới
```bash
#!/bin/bash
# File: create_agent_iso.sh

CAPE_DIR="/opt/CAPEv2"

echo "Creating new agent ISO..."

# Tạo thư mục agent
rm -rf /tmp/cape-agent-new
mkdir -p /tmp/cape-agent-new

# Copy agent files
cp $CAPE_DIR/agent/agent.py /tmp/cape-agent-new/
cp -r $CAPE_DIR/agent/*.py /tmp/cape-agent-new/ 2>/dev/null || true

# Tạo Windows batch files
cat > /tmp/cape-agent-new/install_agent.bat << 'EOF'
@echo off
echo Installing CAPE Agent...

REM Create directory
if not exist "C:\cape-agent" mkdir C:\cape-agent

REM Copy files
copy *.py C:\cape-agent\

REM Create startup script
echo @echo off > C:\cape-agent\start_agent.bat
echo cd C:\cape-agent >> C:\cape-agent\start_agent.bat
echo python agent.py ************* 2042 >> C:\cape-agent\start_agent.bat

REM Add to startup
copy C:\cape-agent\start_agent.bat "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\"

echo Agent installed successfully!
echo Agent will start automatically on next boot.
pause
EOF

cat > /tmp/cape-agent-new/test_agent.bat << 'EOF'
@echo off
echo Testing CAPE Agent...
cd C:\cape-agent
python agent.py ************* 2042
EOF

cat > /tmp/cape-agent-new/README.txt << 'EOF'
CAPE Agent Installation:

1. Run install_agent.bat as Administrator
2. Restart Windows
3. Agent should start automatically
4. To test manually, run test_agent.bat

Agent connects to: *************:2042
EOF

# Tạo ISO
genisoimage -o /var/lib/libvirt/images/iso/cape-agent-new.iso -V "CAPE_AGENT" -r -J /tmp/cape-agent-new/

echo "✓ Agent ISO created: /var/lib/libvirt/images/iso/cape-agent-new.iso"
echo ""
echo "Next steps:"
echo "1. Attach ISO to VMs"
echo "2. Install agent in each VM"
echo "3. Test behavior analysis"
```

### 2.3. Script test behavior analysis
```bash
#!/bin/bash
# File: test_behavior_analysis.sh

CAPE_DIR="/opt/CAPEv2"

echo "Testing behavior analysis..."

# Tạo test executable
cat > /tmp/test_behavior.py << 'EOF'
#!/usr/bin/env python3
import os
import time
import sys

print("CAPE Behavior Test Starting...")

# Test file operations
try:
    with open("/tmp/cape_test_file.txt", "w") as f:
        f.write("CAPE behavior analysis test")
    print("✓ File operation test")
except Exception as e:
    print(f"✗ File operation failed: {e}")

# Test process operations
try:
    import subprocess
    result = subprocess.run(["whoami"], capture_output=True, text=True, timeout=5)
    print(f"✓ Process test: {result.stdout.strip()}")
except Exception as e:
    print(f"✗ Process test failed: {e}")

# Sleep to allow monitoring
print("Sleeping for 30 seconds for monitoring...")
time.sleep(30)

print("CAPE Behavior Test Completed")
EOF

chmod +x /tmp/test_behavior.py

echo "✓ Test script created: /tmp/test_behavior.py"
echo ""
echo "To test behavior analysis:"
echo "1. Make sure VMs are running with agents"
echo "2. Submit test: cd $CAPE_DIR && python3 utils/submit.py --timeout 120 /tmp/test_behavior.py"
echo "3. Check results in web interface: http://localhost:8000"
```

## GIẢI PHÁP 3: COMPLETE FIX SCRIPT

### Script tổng hợp fix tất cả
```bash
#!/bin/bash
# File: complete_cape_fix.sh

CAPE_DIR="/opt/CAPEv2"
CONF_DIR="$CAPE_DIR/conf"

echo "=== COMPLETE CAPE OFFLINE + BEHAVIOR FIX ==="

# 1. BLOCK ALL EXTERNAL CONNECTIONS
echo "Step 1: Blocking external connections..."
iptables -F OUTPUT
iptables -A OUTPUT -o lo -j ACCEPT
iptables -A OUTPUT -d *********/8 -j ACCEPT
iptables -A OUTPUT -d *************/24 -j ACCEPT
iptables -A OUTPUT -d ***********/24 -j ACCEPT
iptables -A OUTPUT -p udp --dport 53 -j REJECT
iptables -A OUTPUT -p tcp --dport 53 -j REJECT
iptables -A OUTPUT -p tcp --dport 80 -j REJECT
iptables -A OUTPUT -p tcp --dport 443 -j REJECT
iptables -A OUTPUT -j REJECT
iptables-save > /etc/iptables/rules.v4

# 2. BLOCK VT DOMAINS
echo "Step 2: Blocking VT domains..."
cat >> /etc/hosts << 'EOF'
127.0.0.1 www.virustotal.com
127.0.0.1 virustotal.com
127.0.0.1 api.virustotal.com
EOF

# 3. UPDATE CONFIGS
echo "Step 3: Updating configs..."
sed -i 's/resolve_dns = on/resolve_dns = off/g' $CONF_DIR/cuckoo.conf
sed -i 's/route = internet/route = none/g' $CONF_DIR/routing.conf
sed -i 's/enabled = yes/enabled = no/g' $CONF_DIR/processing.conf
sed -i 's/\[behavior\]/[behavior]/g; /\[behavior\]/,/^\[/ s/enabled = no/enabled = yes/' $CONF_DIR/processing.conf

# 4. RESTART SERVICES
echo "Step 4: Restarting services..."
systemctl restart cape 2>/dev/null || true

echo ""
echo "✓ COMPLETE FIX APPLIED!"
echo ""
echo "NEXT MANUAL STEPS:"
echo "1. Install agent in VMs using new ISO"
echo "2. Test with: python3 utils/submit.py /tmp/test_behavior.py"
echo "3. Check logs: tail -f log/cuckoo.log"
echo "4. Verify NO external connections in logs"
```

## CÁCH SỬ DỤNG:

```bash
# 1. Apply complete fix
chmod +x complete_cape_fix.sh
sudo ./complete_cape_fix.sh

# 2. Check status
chmod +x check_behavior_status.sh
./check_behavior_status.sh

# 3. Create new agent ISO
chmod +x create_agent_iso.sh
sudo ./create_agent_iso.sh

# 4. Attach ISO to VMs
sudo virsh attach-disk cape1 /var/lib/libvirt/images/iso/cape-agent-new.iso hdc --type cdrom --mode readonly
sudo virsh attach-disk cuckoo1 /var/lib/libvirt/images/iso/cape-agent-new.iso hdc --type cdrom --mode readonly

# 5. Install agent trong VMs (manual)
# - Vào VMs, chạy install_agent.bat as Administrator
# - Restart VMs

# 6. Test behavior analysis
chmod +x test_behavior_analysis.sh
./test_behavior_analysis.sh

# 7. Submit test sample
cd /opt/CAPEv2
python3 utils/submit.py --timeout 120 /tmp/test_behavior.py
```

## KẾT QUẢ MONG ĐỢI:

✅ **KHÔNG CÒN:**
- DNS queries
- VirusTotal requests  
- External HTTP/HTTPS connections
- Network leakage

✅ **CÓ BEHAVIOR ANALYSIS:**
- API calls monitoring
- File operations
- Process creation
- Registry changes
- Memory analysis
- Screenshots

Với fix này sẽ **HOÀN TOÀN OFFLINE** và **BEHAVIOR ANALYSIS HOẠT ĐỘNG**!
