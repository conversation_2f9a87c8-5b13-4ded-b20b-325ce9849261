# Disable DNS Monitoring trong CAPEv2 - HOÀN TOÀN

## Vấn đề
Trong report Overview, phần DNS vẫn hiển thị các DNS queries của malware, c<PERSON> thể leak thông tin ra VirusTotal hoặc external services.

## GIẢI PHÁP: D<PERSON><PERSON>LE HOÀN TOÀN DNS MONITORING

### BƯỚC 1: Block DNS trong VMs (không cho malware query DNS)

#### 1.1. Block DNS trong VM network
```bash
#!/bin/bash
# File: block_dns_in_vms.sh

echo "Blocking DNS in VM network..."

# Block DNS traffic từ VMs
iptables -I FORWARD -s *************/24 -p udp --dport 53 -j DROP
iptables -I FORWARD -s *************/24 -p tcp --dport 53 -j DROP

# Block DNS traffic tới VMs
iptables -I FORWARD -d *************/24 -p udp --sport 53 -j DROP
iptables -I FORWARD -d *************/24 -p tcp --sport 53 -j DROP

# Save rules
iptables-save > /etc/iptables/rules.v4

echo "✓ DNS blocked for VMs"
```

#### 1.2. Disable DNS trong VM network config
```bash
#!/bin/bash
# File: disable_vm_dns.sh

# Update cape-hostonly network để không có DNS
cat > /tmp/cape-no-dns.xml << 'EOF'
<network>
  <name>cape-hostonly</name>
  <bridge name='virbr1' stp='on' delay='0'/>
  <ip address='*************' netmask='*************'>
    <dhcp>
      <range start='**************' end='***************'/>
    </dhcp>
  </ip>
</network>
EOF

# Stop VMs
sudo virsh shutdown cape1 cuckoo1
sleep 15

# Update network
sudo virsh net-destroy cape-hostonly
sudo virsh net-undefine cape-hostonly
sudo virsh net-define /tmp/cape-no-dns.xml
sudo virsh net-start cape-hostonly
sudo virsh net-autostart cape-hostonly

# Start VMs
sudo virsh start cape1 cuckoo1

echo "✓ VM network updated without DNS"
```

### BƯỚC 2: Disable DNS Processing trong CAPEv2

#### 2.1. Disable DNS processing module
```bash
#!/bin/bash
# File: disable_dns_processing.sh

CAPE_DIR="/opt/CAPEv2"

echo "Disabling DNS processing in CAPEv2..."

# 1. Disable trong processing.conf
sed -i '/\[network\]/,/^\[/ s/enabled = yes/enabled = no/' $CAPE_DIR/conf/processing.conf

# 2. Disable DNS resolution trong cuckoo.conf
sed -i 's/resolve_dns = on/resolve_dns = off/g' $CAPE_DIR/conf/cuckoo.conf

# 3. Rename DNS processing module để disable hoàn toàn
if [ -f "$CAPE_DIR/modules/processing/network.py" ]; then
    mv "$CAPE_DIR/modules/processing/network.py" "$CAPE_DIR/modules/processing/network.py.disabled"
    echo "✓ Network processing module disabled"
fi

# 4. Create dummy network module (không process DNS)
cat > $CAPE_DIR/modules/processing/network.py << 'EOF'
# Dummy network module - DNS processing disabled
from lib.cuckoo.common.abstracts import Processing

class Network(Processing):
    """Dummy network analysis - DNS disabled for security."""
    
    def run(self):
        """Return empty network analysis."""
        return {
            "dns": [],
            "domains": [],
            "hosts": [],
            "http": [],
            "https": [],
            "tcp": [],
            "udp": [],
            "icmp": []
        }
EOF

echo "✓ DNS processing disabled in CAPEv2"
```

#### 2.2. Disable DNS trong behavior analysis
```bash
#!/bin/bash
# File: disable_dns_behavior.sh

CAPE_DIR="/opt/CAPEv2"

echo "Disabling DNS in behavior analysis..."

# Find và disable DNS-related behavior monitoring
find $CAPE_DIR/modules/processing -name "*.py" -exec sed -i 's/dns_query/# dns_query # DISABLED/g' {} \;
find $CAPE_DIR/modules/processing -name "*.py" -exec sed -i 's/resolve_host/# resolve_host # DISABLED/g' {} \;
find $CAPE_DIR/modules/processing -name "*.py" -exec sed -i 's/gethostbyname/# gethostbyname # DISABLED/g' {} \;

# Disable trong behavior.py
if [ -f "$CAPE_DIR/modules/processing/behavior.py" ]; then
    sed -i 's/self\.parse_dns/# self.parse_dns # DISABLED/g' $CAPE_DIR/modules/processing/behavior.py
    sed -i 's/dns_requests/# dns_requests # DISABLED/g' $CAPE_DIR/modules/processing/behavior.py
fi

echo "✓ DNS disabled in behavior analysis"
```

### BƯỚC 3: Disable DNS trong Web Interface

#### 3.1. Hide DNS section trong web reports
```bash
#!/bin/bash
# File: hide_dns_web.sh

CAPE_DIR="/opt/CAPEv2"
WEB_DIR="$CAPE_DIR/web"

echo "Hiding DNS section in web interface..."

# Find và comment out DNS sections trong templates
find $WEB_DIR -name "*.html" -exec sed -i 's/<.*dns.*>/<\!-- DNS DISABLED -->/g' {} \;
find $WEB_DIR -name "*.html" -exec sed -i 's/{{.*dns.*}}/<!-- DNS DISABLED -->/g' {} \;

# Disable DNS trong views
find $WEB_DIR -name "*.py" -exec sed -i 's/dns_requests/# dns_requests # DISABLED/g' {} \;
find $WEB_DIR -name "*.py" -exec sed -i 's/\.dns/# .dns # DISABLED/g' {} \;

echo "✓ DNS hidden in web interface"
```

#### 3.2. Custom template để hide DNS
```bash
#!/bin/bash
# File: custom_dns_template.sh

CAPE_DIR="/opt/CAPEv2"

# Backup original template
cp $CAPE_DIR/web/templates/analysis/overview.html $CAPE_DIR/web/templates/analysis/overview.html.backup

# Create custom template without DNS
cat > /tmp/overview_no_dns.html << 'EOF'
<!-- Custom overview template with DNS section removed -->
{% extends "base.html" %}
{% load static %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>Analysis Overview</h2>
        
        <!-- File Info Section -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">File Information</h3>
            </div>
            <div class="panel-body">
                <!-- File info content -->
            </div>
        </div>
        
        <!-- Behavior Section -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">Behavior Analysis</h3>
            </div>
            <div class="panel-body">
                <!-- Behavior content -->
            </div>
        </div>
        
        <!-- DNS Section REMOVED for security -->
        <!-- 
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">DNS Requests - DISABLED</h3>
            </div>
            <div class="panel-body">
                <p>DNS monitoring disabled for security reasons.</p>
            </div>
        </div>
        -->
        
    </div>
</div>
{% endblock %}
EOF

# Apply custom template
cp /tmp/overview_no_dns.html $CAPE_DIR/web/templates/analysis/overview.html

echo "✓ Custom template applied - DNS section removed"
```

### BƯỚC 4: Block DNS trong Windows VMs

#### 4.1. Script để block DNS trong Windows
```bash
#!/bin/bash
# File: create_dns_block_script.sh

# Tạo script để chạy trong Windows VMs
cat > /tmp/block_dns_windows.bat << 'EOF'
@echo off
echo Blocking DNS in Windows...

REM Block DNS using Windows Firewall
netsh advfirewall firewall add rule name="Block DNS Out UDP" dir=out action=block protocol=UDP remoteport=53
netsh advfirewall firewall add rule name="Block DNS Out TCP" dir=out action=block protocol=TCP remoteport=53
netsh advfirewall firewall add rule name="Block DNS In UDP" dir=in action=block protocol=UDP localport=53
netsh advfirewall firewall add rule name="Block DNS In TCP" dir=in action=block protocol=TCP localport=53

REM Disable DNS Client service
sc config Dnscache start= disabled
sc stop Dnscache

REM Set invalid DNS servers
netsh interface ip set dns "Ethernet" static 127.0.0.1
netsh interface ip set dns "Local Area Connection" static 127.0.0.1

echo DNS blocked in Windows!
pause
EOF

# Tạo ISO chứa script
mkdir -p /tmp/dns-block
cp /tmp/block_dns_windows.bat /tmp/dns-block/

genisoimage -o /var/lib/libvirt/images/iso/dns-block.iso -V "DNS_BLOCK" -r -J /tmp/dns-block/

echo "✓ DNS block script created: /var/lib/libvirt/images/iso/dns-block.iso"
echo "Attach to VMs and run block_dns_windows.bat as Administrator"
```

### BƯỚC 5: Complete DNS Disable Script

#### Script tổng hợp disable tất cả DNS
```bash
#!/bin/bash
# File: complete_dns_disable.sh

CAPE_DIR="/opt/CAPEv2"

echo "=== COMPLETE DNS DISABLE FOR CAPEV2 ==="

# 1. Block DNS in VM network
echo "Step 1: Blocking DNS in VM network..."
iptables -I FORWARD -s *************/24 -p udp --dport 53 -j DROP
iptables -I FORWARD -s *************/24 -p tcp --dport 53 -j DROP
iptables -I FORWARD -d *************/24 -p udp --sport 53 -j DROP
iptables -I FORWARD -d *************/24 -p tcp --sport 53 -j DROP

# 2. Disable DNS processing
echo "Step 2: Disabling DNS processing..."
sed -i '/\[network\]/,/^\[/ s/enabled = yes/enabled = no/' $CAPE_DIR/conf/processing.conf
sed -i 's/resolve_dns = on/resolve_dns = off/g' $CAPE_DIR/conf/cuckoo.conf

# 3. Disable network module
echo "Step 3: Disabling network module..."
if [ -f "$CAPE_DIR/modules/processing/network.py" ]; then
    mv "$CAPE_DIR/modules/processing/network.py" "$CAPE_DIR/modules/processing/network.py.disabled"
fi

# 4. Create dummy network module
cat > $CAPE_DIR/modules/processing/network.py << 'EOF'
from lib.cuckoo.common.abstracts import Processing

class Network(Processing):
    def run(self):
        return {"dns": [], "domains": [], "hosts": []}
EOF

# 5. Disable DNS in behavior
echo "Step 4: Disabling DNS in behavior..."
find $CAPE_DIR/modules/processing -name "*.py" -exec sed -i 's/dns_query/# dns_query/g' {} \;

# 6. Hide DNS in web interface
echo "Step 5: Hiding DNS in web interface..."
find $CAPE_DIR/web -name "*.html" -exec sed -i 's/dns_requests/<!-- dns_requests -->/g' {} \;

# 7. Save iptables
iptables-save > /etc/iptables/rules.v4

# 8. Restart services
echo "Step 6: Restarting services..."
systemctl restart cape 2>/dev/null || true

echo ""
echo "✓ COMPLETE DNS DISABLE APPLIED!"
echo ""
echo "DNS is now completely disabled:"
echo "- No DNS queries from VMs"
echo "- No DNS processing in CAPEv2"
echo "- No DNS display in web interface"
echo "- No DNS data in reports"
echo ""
echo "Next: Run block_dns_windows.bat in VMs to disable DNS in Windows"
```

### BƯỚC 6: Verification Script

#### Script kiểm tra DNS đã disable
```bash
#!/bin/bash
# File: verify_dns_disabled.sh

CAPE_DIR="/opt/CAPEv2"

echo "=== VERIFYING DNS IS DISABLED ==="

# 1. Check iptables rules
echo "1. Checking iptables DNS blocks:"
if iptables -L FORWARD | grep -q "dpt:53"; then
    echo "✓ DNS blocked in iptables"
else
    echo "✗ DNS not blocked in iptables"
fi

# 2. Check processing config
echo ""
echo "2. Checking processing config:"
if grep -q "resolve_dns = off" $CAPE_DIR/conf/cuckoo.conf; then
    echo "✓ DNS resolution disabled in cuckoo.conf"
else
    echo "✗ DNS resolution still enabled"
fi

# 3. Check network module
echo ""
echo "3. Checking network module:"
if [ -f "$CAPE_DIR/modules/processing/network.py.disabled" ]; then
    echo "✓ Original network module disabled"
else
    echo "✗ Original network module still active"
fi

# 4. Test DNS from host (should fail)
echo ""
echo "4. Testing DNS resolution:"
if timeout 3 nslookup google.com >/dev/null 2>&1; then
    echo "⚠ Host can still resolve DNS (expected for host)"
else
    echo "✓ DNS resolution blocked"
fi

# 5. Check recent analysis for DNS data
echo ""
echo "5. Checking recent analysis for DNS data:"
if [ -f "$CAPE_DIR/log/cuckoo.log" ]; then
    if grep -q "dns" $CAPE_DIR/log/cuckoo.log | tail -10; then
        echo "⚠ DNS activity found in logs"
    else
        echo "✓ No DNS activity in recent logs"
    fi
fi

echo ""
echo "Verification completed!"
```

## CÁCH SỬ DỤNG:

```bash
# 1. Apply complete DNS disable
chmod +x complete_dns_disable.sh
sudo ./complete_dns_disable.sh

# 2. Create DNS block script for Windows VMs
chmod +x create_dns_block_script.sh
./create_dns_block_script.sh

# 3. Attach DNS block ISO to VMs
sudo virsh attach-disk cape1 /var/lib/libvirt/images/iso/dns-block.iso hdc --type cdrom --mode readonly
sudo virsh attach-disk cuckoo1 /var/lib/libvirt/images/iso/dns-block.iso hdc --type cdrom --mode readonly

# 4. In VMs: Run block_dns_windows.bat as Administrator

# 5. Verify DNS is disabled
chmod +x verify_dns_disabled.sh
./verify_dns_disabled.sh

# 6. Test analysis (should have NO DNS data)
cd /opt/CAPEv2
python3 utils/submit.py --timeout 120 /tmp/test_sample
```

## KẾT QUẢ:

✅ **DNS HOÀN TOÀN DISABLED:**
- Không có DNS queries từ VMs
- Không có DNS data trong reports
- Không có DNS section trong web interface
- Không có DNS processing trong CAPEv2

✅ **REPORTS SẼ HIỂN thị:**
- Overview: Không có phần DNS
- Behavior: Chỉ có file/registry/process operations
- Static: PE analysis, strings, YARA matches

Với cấu hình này, **HOÀN TOÀN KHÔNG CÓ DNS DATA** trong reports!
