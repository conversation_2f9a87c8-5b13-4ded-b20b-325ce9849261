# Hướng dẫn Setup 2 máy KVM (cape1 và cuckoo1) cho CAPEv2 - CHI TIẾT

## Tổng quan
Tài liệu này hướng dẫn từng bước cụ thể để setup 2 máy ảo KVM (cape1 và cuckoo1) cho CAPEv2 trên host có card mạng enp4s0 (IP: ************).

## BƯỚC 1: Chuẩn bị môi trường

### 1.1. Kiểm tra KVM và libvirt
```bash
# Kiểm tra KVM có hoạt động không
sudo kvm-ok

# Kiểm tra libvirt service
sudo systemctl status libvirtd

# Nếu chưa chạy, start libvirt
sudo systemctl start libvirtd
sudo systemctl enable libvirtd

# Thêm user vào group libvirt
sudo usermod -aG libvirt $USER
sudo usermod -aG kvm $USER

# Logout và login lại để áp dụng group
```

### 1.2. Cài đặt các package cần thiết
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y qemu-kvm libvirt-daemon-system libvirt-clients bridge-utils virt-manager virtinst

# CentOS/RHEL
sudo yum install -y qemu-kvm libvirt virt-install virt-manager bridge-utils
```

## BƯỚC 2: Tạo KVM Network

### 2.1. Kiểm tra network hiện tại
```bash
# Xem các network đang có
sudo virsh net-list --all

# Xem chi tiết network default
sudo virsh net-dumpxml default

# Kiểm tra bridge interfaces
ip link show type bridge
```

### 2.2. Tạo network cho CAPE
```bash
# Tạo file network XML
cat > /tmp/cape-network.xml << 'EOF'
<network>
  <name>cape-network</name>
  <forward mode='nat'>
    <nat>
      <port start='1024' end='65535'/>
    </nat>
  </forward>
  <bridge name='virbr1' stp='on' delay='0'/>
  <ip address='*************' netmask='*************'>
    <dhcp>
      <range start='**************' end='***************'/>
    </dhcp>
  </ip>
</network>
EOF

# Define network
sudo virsh net-define /tmp/cape-network.xml

# Start network
sudo virsh net-start cape-network

# Auto-start network khi boot
sudo virsh net-autostart cape-network

# Kiểm tra network đã tạo
sudo virsh net-list --all
```

## BƯỚC 3: Cấu hình IP forwarding và iptables

### 3.1. Enable IP forwarding
```bash
# Kiểm tra IP forwarding hiện tại
cat /proc/sys/net/ipv4/ip_forward

# Enable IP forwarding tạm thời
sudo sysctl net.ipv4.ip_forward=1

# Enable vĩnh viễn
echo 'net.ipv4.ip_forward=1' | sudo tee -a /etc/sysctl.conf

# Áp dụng ngay
sudo sysctl -p
```

### 3.2. Cấu hình iptables
```bash
# Tạo script iptables
cat > /tmp/setup_cape_iptables.sh << 'EOF'
#!/bin/bash

# Clear existing rules
iptables -F
iptables -t nat -F
iptables -X

# Set default policies
iptables -P INPUT ACCEPT
iptables -P FORWARD ACCEPT
iptables -P OUTPUT ACCEPT

# Allow loopback
iptables -A INPUT -i lo -j ACCEPT

# Allow established connections
iptables -A INPUT -m state --state RELATED,ESTABLISHED -j ACCEPT
iptables -A FORWARD -m state --state RELATED,ESTABLISHED -j ACCEPT

# NAT rule cho enp4s0 (internet access)
iptables -t nat -A POSTROUTING -o enp4s0 -j MASQUERADE

# Allow VM network (*************/24) ra internet
iptables -A FORWARD -s *************/24 -o enp4s0 -j ACCEPT
iptables -A FORWARD -d *************/24 -i enp4s0 -m state --state RELATED,ESTABLISHED -j ACCEPT

# Allow traffic between VMs
iptables -A FORWARD -s *************/24 -d *************/24 -j ACCEPT

# Allow result server port 2042
iptables -A INPUT -p tcp --dport 2042 -j ACCEPT

# Allow SSH (optional)
iptables -A INPUT -p tcp --dport 22 -j ACCEPT

echo "iptables rules applied successfully"
EOF

# Chạy script
sudo bash /tmp/setup_cape_iptables.sh

# Lưu rules (Ubuntu/Debian)
sudo apt install -y iptables-persistent
sudo iptables-save | sudo tee /etc/iptables/rules.v4

# Hoặc lưu rules (CentOS/RHEL)
# sudo service iptables save
```

## BƯỚC 4: Tạo thư mục và download ISO

### 4.1. Tạo thư mục cho VMs
```bash
# Tạo thư mục cho VM images
sudo mkdir -p /var/lib/libvirt/images/cape

# Tạo thư mục cho ISO files
sudo mkdir -p /var/lib/libvirt/images/iso

# Set permissions
sudo chown -R libvirt-qemu:libvirt-qemu /var/lib/libvirt/images/cape
sudo chmod 755 /var/lib/libvirt/images/cape
sudo chmod 755 /var/lib/libvirt/images/iso
```

### 4.2. Download Windows ISO (ví dụ)
```bash
# Download Windows 10 ISO (thay URL bằng link thực tế)
# cd /var/lib/libvirt/images/iso
# sudo wget -O windows10.iso "YOUR_WINDOWS_10_ISO_URL"

# Download Windows 7 ISO (thay URL bằng link thực tế)
# sudo wget -O windows7.iso "YOUR_WINDOWS_7_ISO_URL"

# Hoặc copy từ USB/DVD
# sudo cp /media/usb/windows10.iso /var/lib/libvirt/images/iso/
# sudo cp /media/usb/windows7.iso /var/lib/libvirt/images/iso/
```

## BƯỚC 5: Tạo VM cape1 (Windows 10 x64)

### 5.1. Tạo disk image cho cape1
```bash
# Tạo disk 50GB cho cape1
sudo qemu-img create -f qcow2 /var/lib/libvirt/images/cape/cape1.qcow2 50G

# Kiểm tra disk đã tạo
sudo qemu-img info /var/lib/libvirt/images/cape/cape1.qcow2
```

### 5.2. Tạo VM cape1 với virt-install
```bash
# Tạo VM cape1
sudo virt-install \
  --name cape1 \
  --ram 4096 \
  --disk path=/var/lib/libvirt/images/cape/cape1.qcow2,format=qcow2,bus=virtio \
  --vcpus 2 \
  --os-type windows \
  --os-variant win10 \
  --network network=cape-network,model=virtio \
  --graphics vnc,listen=0.0.0.0,port=5901 \
  --console pty,target_type=serial \
  --cdrom /var/lib/libvirt/images/iso/windows10.iso \
  --boot cdrom,hd \
  --noautoconsole

# Kiểm tra VM đã tạo
sudo virsh list --all
```

### 5.3. Kết nối và cài đặt Windows 10
```bash
# Kết nối VNC để cài Windows (từ máy khác)
# vncviewer YOUR_HOST_IP:5901

# Hoặc sử dụng virt-manager
sudo virt-manager

# Hoặc console trực tiếp
sudo virsh console cape1
```

## BƯỚC 6: Tạo VM cuckoo1 (Windows 7 x86)

### 6.1. Tạo disk image cho cuckoo1
```bash
# Tạo disk 40GB cho cuckoo1
sudo qemu-img create -f qcow2 /var/lib/libvirt/images/cape/cuckoo1.qcow2 40G

# Kiểm tra disk đã tạo
sudo qemu-img info /var/lib/libvirt/images/cape/cuckoo1.qcow2
```

### 6.2. Tạo VM cuckoo1 với virt-install
```bash
# Tạo VM cuckoo1
sudo virt-install \
  --name cuckoo1 \
  --ram 2048 \
  --disk path=/var/lib/libvirt/images/cape/cuckoo1.qcow2,format=qcow2,bus=virtio \
  --vcpus 2 \
  --os-type windows \
  --os-variant win7 \
  --network network=cape-network,model=virtio \
  --graphics vnc,listen=0.0.0.0,port=5902 \
  --console pty,target_type=serial \
  --cdrom /var/lib/libvirt/images/iso/windows7.iso \
  --boot cdrom,hd \
  --noautoconsole

# Kiểm tra VM đã tạo
sudo virsh list --all
```

### 6.3. Kết nối và cài đặt Windows 7
```bash
# Kết nối VNC để cài Windows (từ máy khác)
# vncviewer YOUR_HOST_IP:5902

# Hoặc sử dụng virt-manager
sudo virt-manager

# Hoặc console trực tiếp
sudo virsh console cuckoo1
```

## BƯỚC 7: Cấu hình IP tĩnh trong VMs

### 7.1. Cấu hình IP cho cape1 (*************01)
```bash
# Sau khi cài xong Windows 10, trong VM cape1:
# Mở Command Prompt as Administrator và chạy:

# Set IP tĩnh
netsh interface ip set address name="Ethernet" static *************01 ************* *************

# Set DNS
netsh interface ip set dns name="Ethernet" static *******
netsh interface ip add dns name="Ethernet" ******* index=2

# Kiểm tra cấu hình
ipconfig /all

# Test connectivity
ping *************
ping *******
```

### 7.2. Cấu hình IP cho cuckoo1 (*************02)
```bash
# Sau khi cài xong Windows 7, trong VM cuckoo1:
# Mở Command Prompt as Administrator và chạy:

# Set IP tĩnh
netsh interface ip set address name="Local Area Connection" static *************02 ************* *************

# Set DNS
netsh interface ip set dns name="Local Area Connection" static *******
netsh interface ip add dns name="Local Area Connection" ******* index=2

# Kiểm tra cấu hình
ipconfig /all

# Test connectivity
ping *************
ping *******
```

## BƯỚC 8: Cài đặt CAPE Agent trong VMs

### 8.1. Tạo ISO chứa agent
```bash
# Tạo thư mục tạm cho agent files
mkdir -p /tmp/cape-agent

# Copy agent files từ CAPEv2
cp /opt/CAPEv2/agent/agent.py /tmp/cape-agent/
cp /opt/CAPEv2/agent/*.py /tmp/cape-agent/

# Tạo ISO file
sudo genisoimage -o /var/lib/libvirt/images/iso/cape-agent.iso -V "CAPE_AGENT" -r -J /tmp/cape-agent/

# Kiểm tra ISO đã tạo
ls -la /var/lib/libvirt/images/iso/cape-agent.iso
```

### 8.2. Mount agent ISO vào VMs
```bash
# Attach agent ISO vào cape1
sudo virsh attach-disk cape1 /var/lib/libvirt/images/iso/cape-agent.iso hdc --type cdrom --mode readonly --config

# Attach agent ISO vào cuckoo1
sudo virsh attach-disk cuckoo1 /var/lib/libvirt/images/iso/cape-agent.iso hdc --type cdrom --mode readonly --config

# Kiểm tra disk đã attach
sudo virsh domblklist cape1
sudo virsh domblklist cuckoo1
```

### 8.3. Cài đặt Python trong VMs (nếu cần)
```bash
# Download Python installer
# Trong VM Windows, download và cài Python 3.8+ từ python.org
# Hoặc sử dụng chocolatey:
# Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))
# choco install python3
```

### 8.4. Setup agent trong cape1
```bash
# Trong VM cape1 (Windows 10):
# 1. Mở File Explorer, vào CD drive
# 2. Copy agent.py vào C:\cape-agent\
# 3. Tạo startup script C:\cape-agent\start_agent.bat:

@echo off
cd C:\cape-agent
python agent.py ************* 2042

# 4. Thêm vào Windows startup:
# Win+R -> shell:startup
# Copy start_agent.bat vào thư mục startup
```

### 8.5. Setup agent trong cuckoo1
```bash
# Trong VM cuckoo1 (Windows 7):
# 1. Mở File Explorer, vào CD drive
# 2. Copy agent.py vào C:\cape-agent\
# 3. Tạo startup script C:\cape-agent\start_agent.bat:

@echo off
cd C:\cape-agent
python agent.py ************* 2042

# 4. Thêm vào Windows startup:
# Start -> All Programs -> Startup
# Copy start_agent.bat vào thư mục Startup
```

## BƯỚC 9: Disable bảo mật trong VMs

### 9.1. Disable Windows Defender (cape1 - Win10)
```bash
# Trong VM cape1, mở PowerShell as Administrator:
Set-MpPreference -DisableRealtimeMonitoring $true
Set-MpPreference -DisableBehaviorMonitoring $true
Set-MpPreference -DisableBlockAtFirstSeen $true
Set-MpPreference -DisableIOAVProtection $true
Set-MpPreference -DisablePrivacyMode $true
Set-MpPreference -SignatureDisableUpdateOnStartupWithoutEngine $true
Set-MpPreference -DisableArchiveScanning $true
Set-MpPreference -DisableIntrusionPreventionSystem $true
Set-MpPreference -DisableScriptScanning $true
Set-MpPreference -SubmitSamplesConsent 2
```

### 9.2. Disable Windows Firewall (cả 2 VMs)
```bash
# Trong cả 2 VMs, mở Command Prompt as Administrator:
netsh advfirewall set allprofiles state off

# Hoặc qua GUI:
# Control Panel -> System and Security -> Windows Firewall -> Turn Windows Firewall on or off
```

### 9.3. Disable UAC (cả 2 VMs)
```bash
# Trong cả 2 VMs, mở Command Prompt as Administrator:
reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v EnableLUA /t REG_DWORD /d 0 /f

# Restart VMs sau khi thay đổi
```

## BƯỚC 10: Tạo Snapshots

### 10.1. Shutdown VMs
```bash
# Shutdown cape1
sudo virsh shutdown cape1

# Đợi VM shutdown hoàn toàn
sudo virsh list --all

# Shutdown cuckoo1
sudo virsh shutdown cuckoo1

# Đợi VM shutdown hoàn toàn
sudo virsh list --all
```

### 10.2. Tạo clean snapshots
```bash
# Tạo snapshot cho cape1
sudo virsh snapshot-create-as cape1 clean "Clean snapshot for malware analysis"

# Tạo snapshot cho cuckoo1
sudo virsh snapshot-create-as cuckoo1 clean "Clean snapshot for malware analysis"

# Kiểm tra snapshots đã tạo
sudo virsh snapshot-list cape1
sudo virsh snapshot-list cuckoo1
```

### 10.3. Test restore snapshots
```bash
# Start VMs
sudo virsh start cape1
sudo virsh start cuckoo1

# Test restore cape1
sudo virsh snapshot-revert cape1 clean

# Test restore cuckoo1
sudo virsh snapshot-revert cuckoo1 clean

# Kiểm tra VMs đã restore
sudo virsh list --all
```

## BƯỚC 11: Cấu hình CAPEv2

### 11.1. Tạo file kvm.conf
```bash
# Backup file gốc
sudo cp /opt/CAPEv2/conf/kvm.conf.default /opt/CAPEv2/conf/kvm.conf.backup

# Tạo file kvm.conf mới
cat > /opt/CAPEv2/conf/kvm.conf << 'EOF'
[kvm]
# Danh sách 2 máy VM
machines = cape1,cuckoo1

# Interface cho packet capture
interface = virbr1

# Connection string cho libvirt
dsn = qemu:///system

[cape1]
# Label name trong libvirt
label = cape1

# Platform OS
platform = windows

# IP tĩnh cho cape1
ip = *************01

# Architecture
arch = x64

# Tags cho Windows version (bắt buộc)
tags = win10

# Snapshot name
snapshot = clean

# Result server IP như VM nhìn thấy
resultserver_ip = *************

# Result server port
resultserver_port = 2042

# Interface override cho VM này
interface = virbr1

# Reserved machine (không)
reserved = no

[cuckoo1]
# Label name trong libvirt
label = cuckoo1

# Platform OS
platform = windows

# IP tĩnh cho cuckoo1
ip = *************02

# Architecture
arch = x86

# Tags cho Windows version
tags = win7

# Snapshot name
snapshot = clean

# Result server IP như VM nhìn thấy
resultserver_ip = *************

# Result server port
resultserver_port = 2042

# Interface override cho VM này
interface = virbr1

# Reserved machine (không)
reserved = no
EOF
```

### 11.2. Cấu hình cuckoo.conf
```bash
# Backup file gốc
sudo cp /opt/CAPEv2/conf/cuckoo.conf.default /opt/CAPEv2/conf/cuckoo.conf.backup

# Chỉnh sửa result server trong cuckoo.conf
sudo sed -i 's/ip = ***********/ip = 0.0.0.0/' /opt/CAPEv2/conf/cuckoo.conf

# Hoặc edit thủ công
sudo nano /opt/CAPEv2/conf/cuckoo.conf
# Tìm section [resultserver] và đổi:
# ip = 0.0.0.0
# port = 2042
```

### 11.3. Cấu hình auxiliary.conf
```bash
# Backup file gốc
sudo cp /opt/CAPEv2/conf/auxiliary.conf.default /opt/CAPEv2/conf/auxiliary.conf.backup

# Chỉnh sửa sniffer interface
sudo sed -i 's/interface = virbr1/interface = virbr1/' /opt/CAPEv2/conf/auxiliary.conf

# Enable sniffer
sudo sed -i 's/enabled = yes/enabled = yes/' /opt/CAPEv2/conf/auxiliary.conf
```

## BƯỚC 12: Test và Kiểm tra

### 12.1. Kiểm tra VMs connectivity
```bash
# Start cả 2 VMs
sudo virsh start cape1
sudo virsh start cuckoo1

# Đợi VMs boot xong (khoảng 2-3 phút)
sleep 180

# Ping VMs từ host
ping -c 3 *************01  # cape1
ping -c 3 *************02  # cuckoo1

# Kiểm tra VMs có thể ra internet
# (Trong VMs, mở Command Prompt và ping *******)
```

### 12.2. Test Result Server connection
```bash
# Kiểm tra port 2042 có mở không
sudo netstat -tlnp | grep 2042

# Test kết nối từ host tới VMs
telnet *************01 2042
telnet 192.168.************

# Nếu telnet không có, cài đặt:
sudo apt install telnet
```

### 12.3. Test CAPEv2
```bash
# Chuyển tới thư mục CAPEv2
cd /opt/CAPEv2

# Kiểm tra cấu hình
python3 utils/community.py -waf

# Start CAPEv2 (terminal 1)
python3 cuckoo.py

# Trong terminal khác, submit test sample
python3 utils/submit.py --machine cape1 /bin/ls

# Hoặc submit qua web interface
python3 utils/web.py
# Truy cập http://localhost:8000
```

### 12.4. Kiểm tra logs
```bash
# Xem CAPEv2 logs
tail -f /opt/CAPEv2/log/cuckoo.log

# Xem VM logs
sudo tail -f /var/log/libvirt/qemu/cape1.log
sudo tail -f /var/log/libvirt/qemu/cuckoo1.log

# Xem network logs
sudo tcpdump -i virbr1 -n
```

## BƯỚC 13: Troubleshooting

### 13.1. VM không start
```bash
# Kiểm tra libvirt service
sudo systemctl status libvirtd

# Restart libvirt nếu cần
sudo systemctl restart libvirtd

# Kiểm tra VM definition
sudo virsh dumpxml cape1
sudo virsh dumpxml cuckoo1

# Force start VM
sudo virsh start cape1 --force-boot
```

### 13.2. Không ping được VMs
```bash
# Kiểm tra network
sudo virsh net-list --all
sudo virsh net-info cape-network

# Kiểm tra bridge
ip link show virbr1
brctl show virbr1

# Restart network
sudo virsh net-destroy cape-network
sudo virsh net-start cape-network
```

### 13.3. Agent không kết nối
```bash
# Kiểm tra firewall
sudo iptables -L -n | grep 2042

# Kiểm tra result server
sudo netstat -tlnp | grep 2042

# Test từ VM (trong VM chạy):
# telnet ************* 2042

# Restart agent trong VM
# Ctrl+C để stop agent.py rồi chạy lại
```

### 13.4. Snapshot không hoạt động
```bash
# Kiểm tra snapshots
sudo virsh snapshot-list cape1
sudo virsh snapshot-list cuckoo1

# Xóa snapshot lỗi và tạo lại
sudo virsh snapshot-delete cape1 clean
sudo virsh snapshot-create-as cape1 clean "Clean snapshot"

# Kiểm tra qemu-guest-agent
sudo virsh qemu-agent-command cape1 '{"execute":"guest-ping"}'
```

## BƯỚC 14: Maintenance Scripts

### 14.1. Script start/stop VMs
```bash
# Tạo script quản lý VMs
mkdir -p /opt/CAPEv2/scripts

cat > /opt/CAPEv2/scripts/manage_vms.sh << 'EOF'
#!/bin/bash

case "$1" in
    start)
        echo "Starting CAPE VMs..."
        sudo virsh start cape1
        sudo virsh start cuckoo1
        echo "VMs started"
        ;;
    stop)
        echo "Stopping CAPE VMs..."
        sudo virsh shutdown cape1
        sudo virsh shutdown cuckoo1
        echo "VMs stopped"
        ;;
    restart)
        echo "Restarting CAPE VMs..."
        sudo virsh shutdown cape1
        sudo virsh shutdown cuckoo1
        sleep 10
        sudo virsh start cape1
        sudo virsh start cuckoo1
        echo "VMs restarted"
        ;;
    status)
        echo "VM Status:"
        sudo virsh list --all
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status}"
        exit 1
        ;;
esac
EOF

chmod +x /opt/CAPEv2/scripts/manage_vms.sh
```

### 14.2. Script reset snapshots
```bash
# Tạo script reset snapshots
cat > /opt/CAPEv2/scripts/reset_snapshots.sh << 'EOF'
#!/bin/bash

echo "Resetting VMs to clean snapshots..."

# Stop VMs
sudo virsh shutdown cape1
sudo virsh shutdown cuckoo1

# Wait for shutdown
sleep 15

# Revert to clean snapshots
sudo virsh snapshot-revert cape1 clean
sudo virsh snapshot-revert cuckoo1 clean

# Start VMs
sudo virsh start cape1
sudo virsh start cuckoo1

echo "VMs reset to clean snapshots"
EOF

chmod +x /opt/CAPEv2/scripts/reset_snapshots.sh
```

## Tóm tắt các lệnh quan trọng

### Quản lý VMs:
```bash
# Start VMs
sudo virsh start cape1 cuckoo1

# Stop VMs
sudo virsh shutdown cape1 cuckoo1

# Reset về snapshot clean
sudo virsh snapshot-revert cape1 clean
sudo virsh snapshot-revert cuckoo1 clean

# Xem status VMs
sudo virsh list --all
```

### Chạy CAPEv2:
```bash
cd /opt/CAPEv2
python3 cuckoo.py                    # Start CAPE
python3 utils/web.py                 # Start web interface
python3 utils/submit.py sample.exe   # Submit sample
```

### Kiểm tra logs:
```bash
tail -f /opt/CAPEv2/log/cuckoo.log   # CAPE logs
sudo tcpdump -i virbr1 -n            # Network traffic
```
