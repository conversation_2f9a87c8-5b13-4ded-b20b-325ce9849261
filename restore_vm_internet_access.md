# Restore Internet Access cho VMs (cape1, cuckoo1)

## Tình trạng hiện tại:
- ✅ VMs ping được host (*************)
- ✅ VMs ping được nhau
- ❌ VMs không thể ra internet (*******, google.com)

## BƯỚC 1: Kiểm tra cấu hình hiện tại

### 1.1. Script kiểm tra network hiện tại
```bash
#!/bin/bash
# File: check_current_network.sh

echo "=== CHECKING CURRENT NETWORK CONFIGURATION ==="

# 1. Check VM network
echo "1. VM Network Configuration:"
sudo virsh net-list --all
echo ""
sudo virsh net-dumpxml cape-hostonly 2>/dev/null || sudo virsh net-dumpxml cape-network 2>/dev/null

# 2. Check host interfaces
echo ""
echo "2. Host Network Interfaces:"
ip addr show virbr1
echo ""
ip route show | grep 192.168.100

# 3. Check iptables rules
echo ""
echo "3. Current iptables rules:"
echo "NAT rules:"
sudo iptables -t nat -L -n | grep 192.168.100
echo ""
echo "FORWARD rules:"
sudo iptables -L FORWARD -n | grep 192.168.100

# 4. Test connectivity from host
echo ""
echo "4. Testing connectivity from host:"
for vm_ip in *************01 *************02 *************03; do
    if ping -c 1 -W 2 $vm_ip >/dev/null 2>&1; then
        echo "✅ $vm_ip: Reachable"
    else
        echo "❌ $vm_ip: Not reachable"
    fi
done

# 5. Check DNS resolution
echo ""
echo "5. Host DNS resolution:"
if nslookup google.com >/dev/null 2>&1; then
    echo "✅ Host can resolve DNS"
else
    echo "❌ Host cannot resolve DNS"
fi

echo ""
echo "=== CURRENT NETWORK CHECK COMPLETED ==="
```

### 1.2. Test internet từ VMs
```bash
#!/bin/bash
# File: create_vm_internet_test.sh

echo "Creating VM internet test script..."

# Tạo script test cho VMs
cat > /tmp/test_internet_from_vm.py << 'EOF'
#!/usr/bin/env python3
"""
Script to test internet connectivity from inside VM
"""
import subprocess
import socket
import sys

def test_ping(host, description):
    """Test ping to host"""
    try:
        result = subprocess.run(['ping', '-n', '1', host], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ {description}: OK")
            return True
        else:
            print(f"❌ {description}: FAIL")
            return False
    except Exception as e:
        print(f"❌ {description}: ERROR - {e}")
        return False

def test_dns(hostname):
    """Test DNS resolution"""
    try:
        ip = socket.gethostbyname(hostname)
        print(f"✅ DNS resolution {hostname}: {ip}")
        return True
    except Exception as e:
        print(f"❌ DNS resolution {hostname}: FAIL - {e}")
        return False

def test_http(url):
    """Test HTTP connection"""
    try:
        import urllib.request
        response = urllib.request.urlopen(url, timeout=10)
        print(f"✅ HTTP {url}: OK (status: {response.getcode()})")
        return True
    except Exception as e:
        print(f"❌ HTTP {url}: FAIL - {e}")
        return False

def main():
    print("=== VM INTERNET CONNECTIVITY TEST ===")
    
    # Test local connectivity
    print("\n1. Local Network:")
    test_ping("*************", "Host (gateway)")
    
    # Test internet IPs
    print("\n2. Internet IPs:")
    test_ping("*******", "Google DNS")
    test_ping("*******", "Cloudflare DNS")
    
    # Test DNS resolution
    print("\n3. DNS Resolution:")
    test_dns("google.com")
    test_dns("github.com")
    
    # Test HTTP
    print("\n4. HTTP Connectivity:")
    test_http("http://google.com")
    
    print("\n=== TEST COMPLETED ===")
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
EOF

# Tạo Windows batch file
cat > /tmp/test_internet_from_vm.bat << 'EOF'
@echo off
echo === VM Internet Test ===

echo 1. Testing local network...
ping -n 1 *************

echo.
echo 2. Testing internet IPs...
ping -n 1 *******
ping -n 1 *******

echo.
echo 3. Testing DNS resolution...
nslookup google.com
nslookup github.com

echo.
echo 4. Testing HTTP...
curl -I http://google.com

echo.
echo Test completed!
pause
EOF

# Tạo ISO
mkdir -p /tmp/vm-internet-test
cp /tmp/test_internet_from_vm.py /tmp/vm-internet-test/
cp /tmp/test_internet_from_vm.bat /tmp/vm-internet-test/

genisoimage -o /var/lib/libvirt/images/iso/vm-internet-test.iso -V "INTERNET_TEST" -r -J /tmp/vm-internet-test/

echo "✅ VM internet test ISO created: /var/lib/libvirt/images/iso/vm-internet-test.iso"
echo ""
echo "Usage:"
echo "1. Attach ISO to VMs"
echo "2. Run test_internet_from_vm.bat in VMs"
echo "3. Check which tests fail"
```

## BƯỚC 2: Restore NAT network

### 2.1. Tạo NAT network mới
```bash
#!/bin/bash
# File: create_nat_network.sh

echo "Creating NAT network for internet access..."

# 1. Stop existing network
echo "1. Stopping existing network..."
sudo virsh net-destroy cape-hostonly 2>/dev/null || true
sudo virsh net-destroy cape-network 2>/dev/null || true

# 2. Create NAT network XML
cat > /tmp/cape-nat-network.xml << 'EOF'
<network>
  <name>cape-nat</name>
  <forward mode='nat'>
    <nat>
      <port start='1024' end='65535'/>
    </nat>
  </forward>
  <bridge name='virbr1' stp='on' delay='0'/>
  <ip address='*************' netmask='*************'>
    <dhcp>
      <range start='**************' end='***************'/>
    </dhcp>
  </ip>
</network>
EOF

# 3. Define và start NAT network
echo "2. Creating NAT network..."
sudo virsh net-define /tmp/cape-nat-network.xml
sudo virsh net-start cape-nat
sudo virsh net-autostart cape-nat

# 4. Verify network
echo "3. Verifying network..."
sudo virsh net-list --all
sudo virsh net-dumpxml cape-nat

echo "✅ NAT network created successfully"
```

### 2.2. Update VM network interfaces
```bash
#!/bin/bash
# File: update_vm_network.sh

echo "Updating VM network interfaces..."

# 1. Shutdown VMs
echo "1. Shutting down VMs..."
sudo virsh shutdown cape1 2>/dev/null || true
sudo virsh shutdown cuckoo1 2>/dev/null || true
sleep 15

# 2. Update network for each VM
for vm in cape1 cuckoo1; do
    echo "2. Updating network for $vm..."
    
    # Detach old network interface
    sudo virsh detach-interface $vm network --config 2>/dev/null || true
    
    # Attach new NAT network interface
    sudo virsh attach-interface $vm network cape-nat --model virtio --config
    
    echo "✅ $vm network updated"
done

# 3. Start VMs
echo "3. Starting VMs..."
sudo virsh start cape1
sudo virsh start cuckoo1

# 4. Wait for VMs to boot
echo "4. Waiting for VMs to boot..."
sleep 60

# 5. Verify VMs are running
echo "5. Verifying VMs..."
sudo virsh list | grep -E "(cape1|cuckoo1)"

echo "✅ VM network interfaces updated"
```

## BƯỚC 3: Configure iptables for NAT

### 3.1. Setup NAT rules
```bash
#!/bin/bash
# File: setup_nat_rules.sh

echo "Setting up NAT rules for internet access..."

# 1. Enable IP forwarding
echo "1. Enabling IP forwarding..."
echo 1 | sudo tee /proc/sys/net/ipv4/ip_forward
echo 'net.ipv4.ip_forward=1' | sudo tee -a /etc/sysctl.conf

# 2. Clear existing rules for VM subnet
echo "2. Clearing existing rules..."
sudo iptables -t nat -D POSTROUTING -s *************/24 -o enp4s0 -j MASQUERADE 2>/dev/null || true
sudo iptables -D FORWARD -s *************/24 -o enp4s0 -j ACCEPT 2>/dev/null || true
sudo iptables -D FORWARD -d *************/24 -i enp4s0 -m state --state RELATED,ESTABLISHED -j ACCEPT 2>/dev/null || true

# 3. Add NAT rules
echo "3. Adding NAT rules..."
sudo iptables -t nat -A POSTROUTING -s *************/24 -o enp4s0 -j MASQUERADE
sudo iptables -A FORWARD -s *************/24 -o enp4s0 -j ACCEPT
sudo iptables -A FORWARD -d *************/24 -i enp4s0 -m state --state RELATED,ESTABLISHED -j ACCEPT

# 4. Allow VM communication
echo "4. Allowing VM communication..."
sudo iptables -A INPUT -s *************/24 -j ACCEPT
sudo iptables -A OUTPUT -d *************/24 -j ACCEPT

# 5. Save rules
echo "5. Saving iptables rules..."
sudo iptables-save | sudo tee /etc/iptables/rules.v4

# 6. Verify rules
echo "6. Verifying NAT rules..."
echo "NAT rules:"
sudo iptables -t nat -L -n | grep 192.168.100
echo ""
echo "FORWARD rules:"
sudo iptables -L FORWARD -n | grep 192.168.100

echo "✅ NAT rules configured successfully"
```

### 3.2. Remove blocking rules
```bash
#!/bin/bash
# File: remove_blocking_rules.sh

echo "Removing rules that block internet access..."

# 1. Remove DNS blocking rules
echo "1. Removing DNS blocking rules..."
sudo iptables -D OUTPUT -p udp --dport 53 -j DROP 2>/dev/null || true
sudo iptables -D OUTPUT -p tcp --dport 53 -j DROP 2>/dev/null || true
sudo iptables -D FORWARD -s *************/24 -p udp --dport 53 -j DROP 2>/dev/null || true
sudo iptables -D FORWARD -s *************/24 -p tcp --dport 53 -j DROP 2>/dev/null || true

# 2. Remove HTTP/HTTPS blocking rules
echo "2. Removing HTTP/HTTPS blocking rules..."
sudo iptables -D OUTPUT -p tcp --dport 80 -j DROP 2>/dev/null || true
sudo iptables -D OUTPUT -p tcp --dport 443 -j DROP 2>/dev/null || true
sudo iptables -D FORWARD -s *************/24 -p tcp --dport 80 -j DROP 2>/dev/null || true
sudo iptables -D FORWARD -s *************/24 -p tcp --dport 443 -j DROP 2>/dev/null || true

# 3. Remove general blocking rules
echo "3. Removing general blocking rules..."
sudo iptables -D OUTPUT -j DROP 2>/dev/null || true
sudo iptables -D OUTPUT -j REJECT 2>/dev/null || true

# 4. Allow DNS
echo "4. Allowing DNS..."
sudo iptables -A OUTPUT -p udp --dport 53 -j ACCEPT
sudo iptables -A OUTPUT -p tcp --dport 53 -j ACCEPT

# 5. Allow HTTP/HTTPS
echo "5. Allowing HTTP/HTTPS..."
sudo iptables -A OUTPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A OUTPUT -p tcp --dport 443 -j ACCEPT

# 6. Save rules
echo "6. Saving updated rules..."
sudo iptables-save | sudo tee /etc/iptables/rules.v4

echo "✅ Blocking rules removed successfully"
```

## BƯỚC 4: Configure DNS trong VMs

### 4.1. Tạo script cấu hình DNS cho VMs
```bash
#!/bin/bash
# File: create_dns_config_script.sh

echo "Creating DNS configuration script for VMs..."

# Tạo script cho Windows VMs
cat > /tmp/configure_dns.bat << 'EOF'
@echo off
echo === Configuring DNS for Internet Access ===

REM Check Administrator privileges
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Please run as Administrator!
    pause
    exit /b 1
)

echo Current IP configuration:
ipconfig /all

echo.
echo Configuring DNS servers...

REM Set DNS servers for Ethernet interface
netsh interface ip set dns name="Ethernet" static *******
netsh interface ip add dns name="Ethernet" ******* index=2

REM Also try for "Local Area Connection" (Windows 7)
netsh interface ip set dns name="Local Area Connection" static ******* 2>nul
netsh interface ip add dns name="Local Area Connection" ******* index=2 2>nul

echo.
echo Flushing DNS cache...
ipconfig /flushdns

echo.
echo Testing DNS resolution...
nslookup google.com
nslookup github.com

echo.
echo Testing internet connectivity...
ping -n 1 *******
ping -n 1 google.com

echo.
echo DNS configuration completed!
pause
EOF

# Tạo script enable DHCP (alternative)
cat > /tmp/enable_dhcp.bat << 'EOF'
@echo off
echo === Enabling DHCP for automatic configuration ===

REM Check Administrator privileges
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Please run as Administrator!
    pause
    exit /b 1
)

echo Enabling DHCP for Ethernet...
netsh interface ip set address name="Ethernet" dhcp
netsh interface ip set dns name="Ethernet" dhcp

REM Also try for "Local Area Connection" (Windows 7)
netsh interface ip set address name="Local Area Connection" dhcp 2>nul
netsh interface ip set dns name="Local Area Connection" dhcp 2>nul

echo.
echo Waiting for DHCP configuration...
timeout /t 10

echo.
echo Current configuration:
ipconfig /all

echo.
echo Testing connectivity...
ping -n 1 *************
ping -n 1 *******
ping -n 1 google.com

echo.
echo DHCP configuration completed!
pause
EOF

# Tạo ISO
mkdir -p /tmp/dns-config
cp /tmp/configure_dns.bat /tmp/dns-config/
cp /tmp/enable_dhcp.bat /tmp/dns-config/

genisoimage -o /var/lib/libvirt/images/iso/dns-config.iso -V "DNS_CONFIG" -r -J /tmp/dns-config/

echo "✅ DNS configuration ISO created: /var/lib/libvirt/images/iso/dns-config.iso"
echo ""
echo "Usage:"
echo "1. Attach ISO to VMs"
echo "2. Try enable_dhcp.bat first (automatic)"
echo "3. If DHCP fails, use configure_dns.bat (manual)"
```

## BƯỚC 5: Complete restore script

### 5.1. Script restore internet hoàn chỉnh
```bash
#!/bin/bash
# File: complete_internet_restore.sh

echo "=== COMPLETE INTERNET RESTORE FOR VMs ==="

# 1. Check current network
echo "Step 1: Checking current network configuration..."
./check_current_network.sh > network_check.log
echo "Network check saved to: network_check.log"

# 2. Create NAT network
echo ""
echo "Step 2: Creating NAT network..."
./create_nat_network.sh

# 3. Update VM network interfaces
echo ""
echo "Step 3: Updating VM network interfaces..."
./update_vm_network.sh

# 4. Setup NAT rules
echo ""
echo "Step 4: Setting up NAT rules..."
./setup_nat_rules.sh

# 5. Remove blocking rules
echo ""
echo "Step 5: Removing blocking rules..."
./remove_blocking_rules.sh

# 6. Create DNS config tools
echo ""
echo "Step 6: Creating DNS configuration tools..."
./create_dns_config_script.sh

# 7. Create internet test tools
echo ""
echo "Step 7: Creating internet test tools..."
./create_vm_internet_test.sh

# 8. Test from host
echo ""
echo "Step 8: Testing from host..."
echo "Host internet test:"
if ping -c 1 ******* >/dev/null 2>&1; then
    echo "✅ Host has internet access"
else
    echo "❌ Host does not have internet access"
fi

echo ""
echo "=== INTERNET RESTORE COMPLETED ==="
echo ""
echo "MANUAL STEPS IN VMs:"
echo ""
echo "1. Attach DNS config ISO:"
echo "   sudo virsh attach-disk cape1 /var/lib/libvirt/images/iso/dns-config.iso hdc --type cdrom --mode readonly"
echo "   sudo virsh attach-disk cuckoo1 /var/lib/libvirt/images/iso/dns-config.iso hdc --type cdrom --mode readonly"
echo ""
echo "2. In each VM:"
echo "   - Run enable_dhcp.bat as Administrator (try this first)"
echo "   - If DHCP fails, run configure_dns.bat as Administrator"
echo ""
echo "3. Test internet in VMs:"
echo "   - Attach vm-internet-test.iso"
echo "   - Run test_internet_from_vm.bat"
echo ""
echo "4. Expected results:"
echo "   ✅ ping ******* should work"
echo "   ✅ nslookup google.com should work"
echo "   ✅ HTTP access should work"
```

## CÁCH SỬ DỤNG:

```bash
# 1. Restore internet access
chmod +x *.sh
sudo ./complete_internet_restore.sh

# 2. Configure DNS trong VMs
sudo virsh attach-disk cape1 /var/lib/libvirt/images/iso/dns-config.iso hdc --type cdrom --mode readonly
sudo virsh attach-disk cuckoo1 /var/lib/libvirt/images/iso/dns-config.iso hdc --type cdrom --mode readonly

# 3. Trong VMs: Run enable_dhcp.bat as Administrator

# 4. Test internet
sudo virsh attach-disk cape1 /var/lib/libvirt/images/iso/vm-internet-test.iso hdc --type cdrom --mode readonly
# Trong VMs: Run test_internet_from_vm.bat
```

## KẾT QUẢ MONG ĐỢI:

✅ **Sau khi restore:**
- VMs ping được *******
- VMs resolve được DNS (google.com)
- VMs truy cập được HTTP/HTTPS
- VMs có thể download/update

❌ **Trước khi restore:**
- VMs chỉ ping được local network
- Không resolve DNS
- Không truy cập internet

**VMs sẽ có internet access trở lại!**
