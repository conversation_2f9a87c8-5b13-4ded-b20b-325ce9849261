#!/bin/bash

# Script tìm tất cả file cấu hình CAPEv2 cần sửa khi đổi vị trí
# Sử dụng: ./find_cape_configs.sh

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

function print_section() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

function print_found() {
    echo -e "${GREEN}[FOUND]${NC} $1"
}

function print_command() {
    echo -e "${YELLOW}[EDIT]${NC} $1"
}

OLD_CAPE_PATH="/opt/CAPEv2"

echo "🔍 TÌM TẤT CẢ FILE CẤU HÌNH CAPEv2 CẦN SỬA"
echo "Tìm kiếm references đến: $OLD_CAPE_PATH"
echo

print_section "1. SYSTEMD SERVICES"
echo "Vị trí: /lib/systemd/system/ và /etc/systemd/system/"

SYSTEMD_FILES=(
    "/lib/systemd/system/cape.service"
    "/lib/systemd/system/cape-processor.service"
    "/lib/systemd/system/cape-web.service"
    "/lib/systemd/system/cape-rooter.service"
    "/lib/systemd/system/cape-dist.service"
    "/lib/systemd/system/suricata.service"
    "/lib/systemd/system/guacd.service"
    "/lib/systemd/system/guac-web.service"
)

for file in "${SYSTEMD_FILES[@]}"; do
    if [ -f "$file" ] && grep -q "$OLD_CAPE_PATH" "$file" 2>/dev/null; then
        print_found "$file"
        print_command "sudo nano $file"
        echo "  Cần sửa: WorkingDirectory, ExecStart, ExecStartPre"
        echo
    fi
done

print_section "2. CRONTAB"
echo "Kiểm tra crontab của user hiện tại:"
if crontab -l 2>/dev/null | grep -q "$OLD_CAPE_PATH"; then
    print_found "User crontab có references"
    print_command "crontab -e"
    echo "  Các dòng cần sửa:"
    crontab -l 2>/dev/null | grep -n "$OLD_CAPE_PATH" | sed 's/^/    /'
    echo
fi

echo "Kiểm tra root crontab:"
if sudo crontab -l 2>/dev/null | grep -q "$OLD_CAPE_PATH"; then
    print_found "Root crontab có references"
    print_command "sudo crontab -e"
    echo "  Các dòng cần sửa:"
    sudo crontab -l 2>/dev/null | grep -n "$OLD_CAPE_PATH" | sed 's/^/    /'
    echo
fi

echo "Kiểm tra system cron files:"
if [ -d "/etc/cron.d" ]; then
    find /etc/cron.d -type f -exec grep -l "$OLD_CAPE_PATH" {} \; 2>/dev/null | while read -r file; do
        print_found "$file"
        print_command "sudo nano $file"
        echo "  Các dòng cần sửa:"
        grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/    /'
        echo
    done
fi

print_section "3. UWSGI CONFIGURATION"
UWSGI_LOCATIONS=(
    "/etc/uwsgi/apps-available"
    "/etc/uwsgi/apps-enabled"
)

for dir in "${UWSGI_LOCATIONS[@]}"; do
    if [ -d "$dir" ]; then
        echo "Kiểm tra: $dir"
        find "$dir" -name "*cape*" -o -name "*.ini" | while read -r file; do
            if [ -f "$file" ] && grep -q "$OLD_CAPE_PATH" "$file" 2>/dev/null; then
                print_found "$file"
                print_command "sudo nano $file"
                echo "  Các dòng cần sửa:"
                grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/    /'
                echo
            fi
        done
    fi
done

print_section "4. NGINX CONFIGURATION"
NGINX_LOCATIONS=(
    "/etc/nginx/sites-available"
    "/etc/nginx/sites-enabled"
    "/etc/nginx/conf.d"
)

for dir in "${NGINX_LOCATIONS[@]}"; do
    if [ -d "$dir" ]; then
        echo "Kiểm tra: $dir"
        find "$dir" -type f \( -name "*cape*" -o -name "*.conf" \) | while read -r file; do
            if [ -f "$file" ] && grep -q "$OLD_CAPE_PATH" "$file" 2>/dev/null; then
                print_found "$file"
                print_command "sudo nano $file"
                echo "  Các dòng cần sửa:"
                grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/    /'
                echo
            fi
        done
    fi
done

print_section "5. APPARMOR PROFILES"
if [ -d "/etc/apparmor.d" ]; then
    echo "Kiểm tra: /etc/apparmor.d"
    find /etc/apparmor.d -type f | while read -r file; do
        if [ -f "$file" ] && grep -q "$OLD_CAPE_PATH" "$file" 2>/dev/null; then
            print_found "$file"
            print_command "sudo nano $file"
            echo "  Các dòng cần sửa:"
            grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/    /'
            echo
        fi
    done
fi

print_section "6. LOGROTATE CONFIGURATION"
if [ -d "/etc/logrotate.d" ]; then
    echo "Kiểm tra: /etc/logrotate.d"
    find /etc/logrotate.d -type f | while read -r file; do
        if [ -f "$file" ] && grep -q "$OLD_CAPE_PATH" "$file" 2>/dev/null; then
            print_found "$file"
            print_command "sudo nano $file"
            echo "  Các dòng cần sửa:"
            grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/    /'
            echo
        fi
    done
fi

print_section "7. SUDOERS FILES"
SUDOERS_FILES=(
    "/etc/sudoers.d/cape"
    "/etc/sudoers.d/ip_netns"
)

for file in "${SUDOERS_FILES[@]}"; do
    if [ -f "$file" ] && grep -q "$OLD_CAPE_PATH" "$file" 2>/dev/null; then
        print_found "$file"
        print_command "sudo visudo -f $file"
        echo "  Các dòng cần sửa:"
        grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/    /'
        echo
    fi
done

if grep -q "$OLD_CAPE_PATH" /etc/sudoers 2>/dev/null; then
    print_found "/etc/sudoers"
    print_command "sudo visudo"
    echo "  Các dòng cần sửa:"
    grep -n "$OLD_CAPE_PATH" /etc/sudoers | sed 's/^/    /'
    echo
fi

print_section "8. ENVIRONMENT FILES"
ENV_FILES=(
    "/etc/environment"
    "/etc/profile"
    "/etc/bash.bashrc"
)

for file in "${ENV_FILES[@]}"; do
    if [ -f "$file" ] && grep -q "$OLD_CAPE_PATH" "$file" 2>/dev/null; then
        print_found "$file"
        print_command "sudo nano $file"
        echo "  Các dòng cần sửa:"
        grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/    /'
        echo
    fi
done

print_section "9. SYMBOLIC LINKS"
echo "Tìm symbolic links trỏ đến $OLD_CAPE_PATH:"
find /usr /etc /opt /var -type l -lname "*$OLD_CAPE_PATH*" 2>/dev/null | while read -r link; do
    target=$(readlink "$link")
    print_found "Link: $link -> $target"
    print_command "sudo rm $link && sudo ln -sf /new/path $link"
    echo
done

print_section "HƯỚNG DẪN SỬA NHANH"
echo
echo "🔧 CÁC LỆNH SỬA NHANH:"
echo
echo "1. Sửa systemd services:"
echo "   sudo nano /lib/systemd/system/cape.service"
echo "   sudo nano /lib/systemd/system/cape-processor.service"
echo "   sudo nano /lib/systemd/system/cape-web.service"
echo "   sudo nano /lib/systemd/system/cape-rooter.service"
echo "   sudo systemctl daemon-reload"
echo
echo "2. Sửa crontab:"
echo "   crontab -e"
echo "   sudo crontab -e"
echo
echo "3. Restart services:"
echo "   sudo systemctl restart cape cape-processor cape-web cape-rooter"
echo
echo "4. Kiểm tra status:"
echo "   systemctl status cape cape-processor cape-web cape-rooter"
echo
echo "📝 HOẶC SỬ DỤNG SCRIPT TỰ ĐỘNG:"
echo "   sudo ./update_cape_services.sh /mnt/CAPEv2"
echo
