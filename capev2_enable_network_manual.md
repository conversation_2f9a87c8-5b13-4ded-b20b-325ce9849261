# Hướng dẫn Manual: Bật lại Network Analysis trong CAPEv2

## 📋 **Tổng quan**
Hướng dẫn chi tiết cách chỉnh sửa từng file `.conf` để bật lại Network Analysis sau khi đã tắt.

---

## 📁 **File cần chỉnh sửa**

### 1. **conf/processing.conf** - File chính
### 2. **conf/auxiliary.conf** - Packet capture
### 3. **conf/cuckoo.conf** - Network processing
### 4. **conf/kvm.conf** - VM network (tùy chọn)

---

## 🔧 **1. File: conf/processing.conf**

### **Vị trí:** `/opt/CAPEv2/conf/processing.conf`

```bash
# Mở file để edit
nano /opt/CAPEv2/conf/processing.conf
```

### **A. Section [network] - BẬT LẠI Network Analysis**
```ini
[network]
# THAY ĐỔI: enabled = no → enabled = yes
enabled = yes

# THAY ĐỔI: sort_pcap = no → sort_pcap = yes  
sort_pcap = yes

# Các setting khác GIỮ NGUYÊN hoặc BẬT
dnswhitelist = yes
dnswhitelist_file = extra/whitelist_domains.txt
ipwhitelist = yes
ipwhitelist_file = extra/whitelist_ips.txt
network_passlist = no
network_passlist_file = extra/whitelist_network.txt

# BẬT country lookup (cần GeoIP database)
country_lookup = yes
```

### **B. Section [suricata] - BẬT LẠI Suricata IDS**
```ini
[suricata]
# THAY ĐỔI: enabled = no → enabled = yes
enabled = yes

# Các setting khác GIỮ NGUYÊN
bin = /usr/bin/suricata
conf = /etc/suricata/suricata.yaml
socket_file = /tmp/suricata-command.socket
runmode = socket
```

### **C. Section [detections] - BẬT LẠI Detection**
```ini
[detections]
enabled = yes
behavior = yes
yara = yes

# THAY ĐỔI: suricata = no → suricata = yes
suricata = yes

virustotal = yes
clamav = no
```

---

## 📡 **2. File: conf/auxiliary.conf**

### **Vị trí:** `/opt/CAPEv2/conf/auxiliary.conf`

```bash
# Mở file để edit
nano /opt/CAPEv2/conf/auxiliary.conf
```

### **A. Section [sniffer] - BẬT LẠI Packet Capture**
```ini
[sniffer]
# THAY ĐỔI: enabled = no → enabled = yes
enabled = yes

# Các setting khác GIỮ NGUYÊN
remote = no
tcpdump = /usr/bin/tcpdump

# KIỂM TRA interface có đúng không
interface = virbr1

# Berkeley packet filter
bpf = not arp
```

### **B. Section [AzSniffer] - GIỮ TẮT**
```ini
[AzSniffer]
# GIỮ NGUYÊN: enabled = no (chỉ dùng cho Azure)
enabled = no
```

---

## ⚙️ **3. File: conf/cuckoo.conf**

### **Vị trí:** `/opt/CAPEv2/conf/cuckoo.conf`

```bash
# Mở file để edit
nano /opt/CAPEv2/conf/cuckoo.conf
```

### **A. Section [processing] - BẬT LẠI Network Processing**
```ini
[processing]
# THAY ĐỔI: sort_pcap = no → sort_pcap = yes
sort_pcap = yes

# THAY ĐỔI: resolve_dns = no → resolve_dns = yes
resolve_dns = yes

# Các setting khác GIỮ NGUYÊN
analysis_timeout = 300
critical_timeout = 120
```

### **B. Section [resultserver] - TĂNG Upload Size**
```ini
[resultserver]
ip = 0.0.0.0
port = 2042
force_port = no

# TĂNG upload size cho network captures
upload_max_size = 256
```

---

## 🌐 **4. File: conf/kvm.conf (Tùy chọn)**

### **Vị trí:** `/opt/CAPEv2/conf/kvm.conf`

```bash
# Mở file để edit (nếu dùng KVM)
nano /opt/CAPEv2/conf/kvm.conf
```

### **A. Section [kvm] - Network Interface**
```ini
[kvm]
# KIỂM TRA interface có đúng không
interface = virbr1
```

### **B. Section [cuckoo1] - VM Network**
```ini
[cuckoo1]
label = cuckoo1
platform = windows
ip = *************01

# KIỂM TRA interface có đúng không
interface = virbr1
resultserver_ip = *************
```

---

## ✅ **5. Checklist thay đổi**

### **File: processing.conf**
- [ ] `[network] enabled = yes`
- [ ] `[network] sort_pcap = yes`
- [ ] `[suricata] enabled = yes`
- [ ] `[detections] suricata = yes`

### **File: auxiliary.conf**
- [ ] `[sniffer] enabled = yes`
- [ ] `[sniffer] interface = virbr1` (hoặc interface đúng)

### **File: cuckoo.conf**
- [ ] `[processing] sort_pcap = yes`
- [ ] `[processing] resolve_dns = yes`
- [ ] `[resultserver] upload_max_size = 256`

---

## 🔍 **6. Kiểm tra sau khi chỉnh sửa**

### **A. Kiểm tra syntax config**
```bash
cd /opt/CAPEv2

# Kiểm tra processing.conf
python3 -c "
import configparser
config = configparser.ConfigParser()
config.read('conf/processing.conf')
print('✅ processing.conf syntax OK')
"

# Kiểm tra auxiliary.conf
python3 -c "
import configparser
config = configparser.ConfigParser()
config.read('conf/auxiliary.conf')
print('✅ auxiliary.conf syntax OK')
"
```

### **B. Kiểm tra network settings**
```bash
# Kiểm tra network enabled
grep -A 5 "\[network\]" conf/processing.conf | grep "enabled"

# Kiểm tra sniffer enabled
grep -A 5 "\[sniffer\]" conf/auxiliary.conf | grep "enabled"

# Kiểm tra suricata enabled
grep -A 5 "\[suricata\]" conf/processing.conf | grep "enabled"
```

### **C. Kiểm tra network interface**
```bash
# Kiểm tra interface có tồn tại không
ip addr show virbr1

# Nếu không có virbr1, kiểm tra virbr0
ip addr show virbr0

# List tất cả interfaces
ip link show
```

---

## 🚀 **7. Restart services**

```bash
# Restart tất cả CAPE services
sudo systemctl restart cape cape-processor cape-web cape-rooter

# Kiểm tra status
sudo systemctl status cape cape-processor cape-web
```

---

## 🔧 **8. Troubleshooting**

### **A. Nếu interface không tồn tại**
```bash
# Start KVM default network
sudo virsh net-start default
sudo virsh net-autostart default

# Kiểm tra lại
ip addr show virbr0
```

### **B. Nếu tcpdump không có quyền**
```bash
# Cấp quyền cho tcpdump
sudo chmod +x /usr/bin/tcpdump
sudo setcap cap_net_raw,cap_net_admin=eip /usr/bin/tcpdump
```

### **C. Nếu Suricata chưa cài**
```bash
# Cài đặt Suricata
sudo apt update
sudo apt install suricata suricata-update -y

# Update rules
sudo suricata-update
```

---

## 📊 **9. Kết quả mong đợi**

### **Sau khi hoàn thành, bạn sẽ thấy:**
- ✅ **Network tab** xuất hiện trong analysis results
- ✅ **PCAP files** được tạo trong `/opt/CAPEv2/storage/analyses/[task_id]/`
- ✅ **DNS queries** được log và hiển thị
- ✅ **HTTP requests** được capture và phân tích
- ✅ **Network connections** được track
- ✅ **Suricata alerts** (nếu có malicious activity)
- ✅ **Geolocation** information cho IPs

### **Cấu trúc file network trong results:**
```
/opt/CAPEv2/storage/analyses/[task_id]/
├── dump.pcap          # Raw network traffic
├── network.json       # Network analysis results
├── suricata/          # Suricata alerts
│   ├── eve.json
│   └── fast.log
└── logs/
    └── network.log
```

---

## 🎯 **10. Test Network Analysis**

### **A. Submit sample để test**
```bash
# Via web interface
# Hoặc via command line
python3 utils/submit.py /path/to/sample.exe
```

### **B. Kiểm tra results**
```bash
# Kiểm tra network data trong database
python3 utils/db.py list

# Xem analysis results
ls -la storage/analyses/[task_id]/
```

---

**🎉 Hoàn thành! Network Analysis đã được bật lại và sẵn sàng phân tích network behavior của malware.**

---

## 📝 **11. Quick Reference - Các thay đổi chính**

### **processing.conf:**
```bash
# Tìm và thay đổi các dòng sau:
[network]
enabled = yes          # Thay từ 'no' thành 'yes'
sort_pcap = yes        # Thay từ 'no' thành 'yes'

[suricata]
enabled = yes          # Thay từ 'no' thành 'yes'

[detections]
suricata = yes         # Thay từ 'no' thành 'yes'
```

### **auxiliary.conf:**
```bash
# Tìm và thay đổi dòng sau:
[sniffer]
enabled = yes          # Thay từ 'no' thành 'yes'
```

### **cuckoo.conf:**
```bash
# Tìm và thay đổi các dòng sau:
[processing]
sort_pcap = yes        # Thay từ 'no' thành 'yes'
resolve_dns = yes      # Thay từ 'no' thành 'yes'
```

---

## ⚡ **12. One-liner Commands (Nhanh nhất)**

```bash
cd /opt/CAPEv2

# Backup configs
mkdir -p conf/backup_$(date +%Y%m%d_%H%M%S)
cp conf/*.conf conf/backup_$(date +%Y%m%d_%H%M%S)/

# Enable network trong processing.conf
sed -i '/^\[network\]/,/^\[/ s/enabled = no/enabled = yes/' conf/processing.conf
sed -i '/^\[network\]/,/^\[/ s/sort_pcap = no/sort_pcap = yes/' conf/processing.conf
sed -i '/^\[suricata\]/,/^\[/ s/enabled = no/enabled = yes/' conf/processing.conf
sed -i '/^\[detections\]/,/^\[/ s/suricata = no/suricata = yes/' conf/processing.conf

# Enable sniffer trong auxiliary.conf
sed -i '/^\[sniffer\]/,/^\[/ s/enabled = no/enabled = yes/' conf/auxiliary.conf

# Enable network processing trong cuckoo.conf
sed -i '/^\[processing\]/,/^\[/ s/sort_pcap = no/sort_pcap = yes/' conf/cuckoo.conf
sed -i '/^\[processing\]/,/^\[/ s/resolve_dns = no/resolve_dns = yes/' conf/cuckoo.conf

# Restart services
sudo systemctl restart cape cape-processor cape-web

echo "✅ Network Analysis đã được bật lại!"
```

---

## 🔍 **13. Verification Commands**

```bash
# Kiểm tra nhanh tất cả settings
echo "=== NETWORK SETTINGS CHECK ==="

echo "1. Network module:"
grep -A 2 "^\[network\]" conf/processing.conf | grep enabled

echo "2. Suricata module:"
grep -A 2 "^\[suricata\]" conf/processing.conf | grep enabled

echo "3. Sniffer module:"
grep -A 2 "^\[sniffer\]" conf/auxiliary.conf | grep enabled

echo "4. Network processing:"
grep -A 5 "^\[processing\]" conf/cuckoo.conf | grep -E "(sort_pcap|resolve_dns)"

echo "5. Services status:"
sudo systemctl is-active cape cape-processor cape-web
```

---

**💡 Tip: Sử dụng one-liner commands ở section 12 để enable nhanh nhất, sau đó dùng verification commands ở section 13 để kiểm tra!**
