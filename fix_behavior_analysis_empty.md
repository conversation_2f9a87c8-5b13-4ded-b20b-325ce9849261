# FIX Behavior Analysis Empty - Debug và Giải pháp

## Vấn đề: Behavior Analysis rỗng
Behavior Analysis không hiển thị gì trong reports, có thể do:
1. Agent không chạy trong VMs
2. Agent không kết nối được result server
3. Behavior processing module bị lỗi
4. VM không thể giao tiếp với host

## BƯỚC 1: Debug toàn diện

### 1.1. Script kiểm tra tổng thể
```bash
#!/bin/bash
# File: debug_behavior_comprehensive.sh

CAPE_DIR="/opt/CAPEv2"

echo "=== COMPREHENSIVE BEHAVIOR ANALYSIS DEBUG ==="

# 1. Check VMs status
echo "1. VM Status:"
sudo virsh list --all | grep -E "(cape1|cuckoo1)"

# 2. Check VM connectivity
echo ""
echo "2. VM Connectivity:"
for vm_ip in *************01 *************02; do
    if ping -c 1 -W 2 $vm_ip >/dev/null 2>&1; then
        echo "✓ $vm_ip: Reachable"
        
        # Test specific ports
        for port in 2042 8000; do
            if timeout 3 bash -c "</dev/tcp/$vm_ip/$port" 2>/dev/null; then
                echo "  ✓ Port $port: Open"
            else
                echo "  ✗ Port $port: Closed"
            fi
        done
    else
        echo "✗ $vm_ip: Not reachable"
    fi
done

# 3. Check result server
echo ""
echo "3. Result Server:"
if netstat -tlnp | grep :2042 >/dev/null; then
    echo "✓ Result server listening on port 2042"
    netstat -tlnp | grep :2042
else
    echo "✗ Result server NOT listening on port 2042"
fi

# 4. Check behavior module
echo ""
echo "4. Behavior Processing Module:"
if [ -f "$CAPE_DIR/modules/processing/behavior.py" ]; then
    echo "✓ Behavior module exists"
    
    # Check if enabled
    if grep -q "behavior.*=.*yes" $CAPE_DIR/conf/processing.conf; then
        echo "✓ Behavior module enabled in config"
    else
        echo "✗ Behavior module disabled in config"
    fi
else
    echo "✗ Behavior module missing!"
fi

# 5. Check recent analysis logs
echo ""
echo "5. Recent Analysis Logs:"
if [ -f "$CAPE_DIR/log/cuckoo.log" ]; then
    echo "Last 20 lines with behavior/agent/analysis:"
    tail -50 $CAPE_DIR/log/cuckoo.log | grep -i -E "(behavior|agent|analysis|task|error)" | tail -20
else
    echo "✗ No cuckoo.log found"
fi

# 6. Check database tasks
echo ""
echo "6. Database Tasks:"
cd $CAPE_DIR
python3 -c "
try:
    from lib.cuckoo.core.database import Database
    db = Database()
    tasks = db.list_tasks(limit=5)
    print(f'Found {len(tasks)} recent tasks:')
    for task in tasks:
        print(f'  Task {task.id}: Status={task.status}, Target={task.target}')
        if hasattr(task, 'guest_log'):
            print(f'    Guest log: {task.guest_log}')
except Exception as e:
    print(f'Database error: {e}')
"

# 7. Check analysis results directory
echo ""
echo "7. Analysis Results:"
if [ -d "$CAPE_DIR/storage/analyses" ]; then
    echo "Recent analysis directories:"
    ls -la $CAPE_DIR/storage/analyses/ | tail -5
    
    # Check latest analysis
    latest_analysis=$(ls -1 $CAPE_DIR/storage/analyses/ | tail -1)
    if [ -n "$latest_analysis" ]; then
        echo ""
        echo "Latest analysis ($latest_analysis) contents:"
        ls -la $CAPE_DIR/storage/analyses/$latest_analysis/
        
        # Check for behavior data
        if [ -f "$CAPE_DIR/storage/analyses/$latest_analysis/reports/report.json" ]; then
            echo ""
            echo "Behavior data in report:"
            python3 -c "
import json
try:
    with open('$CAPE_DIR/storage/analyses/$latest_analysis/reports/report.json', 'r') as f:
        data = json.load(f)
    if 'behavior' in data:
        print(f'Behavior data found: {len(data[\"behavior\"].get(\"processes\", []))} processes')
        if data['behavior'].get('processes'):
            print('First process:', list(data['behavior']['processes'][0].keys())[:5])
        else:
            print('No processes in behavior data')
    else:
        print('No behavior key in report')
except Exception as e:
    print(f'Error reading report: {e}')
"
        fi
    fi
else
    echo "✗ No analyses directory found"
fi

echo ""
echo "=== DEBUG COMPLETED ==="
```

### 1.2. Test agent connection từ host
```bash
#!/bin/bash
# File: test_agent_connection.sh

echo "Testing agent connection from host..."

# Test connection to VMs on result server port
for vm_ip in *************01 *************02; do
    echo "Testing $vm_ip..."
    
    # Test TCP connection
    if timeout 5 bash -c "echo 'test' > /dev/tcp/$vm_ip/2042" 2>/dev/null; then
        echo "✓ $vm_ip: TCP connection successful"
    else
        echo "✗ $vm_ip: TCP connection failed"
    fi
    
    # Test with telnet if available
    if command -v telnet >/dev/null; then
        echo "Testing with telnet..."
        timeout 3 telnet $vm_ip 2042 2>/dev/null | head -3
    fi
    
    # Test with nc if available
    if command -v nc >/dev/null; then
        echo "Testing with netcat..."
        timeout 3 nc -zv $vm_ip 2042 2>&1
    fi
done
```

## BƯỚC 2: Fix Agent trong VMs

### 2.1. Tạo agent setup script mới
```bash
#!/bin/bash
# File: create_new_agent_setup.sh

CAPE_DIR="/opt/CAPEv2"

echo "Creating comprehensive agent setup..."

# Tạo thư mục agent
rm -rf /tmp/cape-agent-fix
mkdir -p /tmp/cape-agent-fix

# Copy tất cả agent files
cp -r $CAPE_DIR/agent/* /tmp/cape-agent-fix/ 2>/dev/null || true

# Tạo Python test script
cat > /tmp/cape-agent-fix/test_python.py << 'EOF'
#!/usr/bin/env python3
import sys
import socket
import time

print("Python version:", sys.version)
print("Testing socket connection...")

try:
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(5)
    result = sock.connect_ex(('*************', 2042))
    if result == 0:
        print("✓ Can connect to result server")
    else:
        print("✗ Cannot connect to result server")
    sock.close()
except Exception as e:
    print(f"✗ Socket error: {e}")

print("Test completed")
EOF

# Tạo Windows install script với debug
cat > /tmp/cape-agent-fix/install_agent_debug.bat << 'EOF'
@echo off
echo === CAPE Agent Installation with Debug ===

REM Check Python
echo Checking Python...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python not found!
    echo Please install Python 3.8+ from python.org
    pause
    exit /b 1
)

REM Create directory
echo Creating agent directory...
if not exist "C:\cape-agent" mkdir C:\cape-agent

REM Copy files
echo Copying agent files...
copy *.py C:\cape-agent\
copy *.bat C:\cape-agent\

REM Test Python script
echo Testing Python connectivity...
cd C:\cape-agent
python test_python.py

REM Create startup script with debug
echo Creating startup script...
echo @echo off > C:\cape-agent\start_agent_debug.bat
echo echo Starting CAPE Agent... >> C:\cape-agent\start_agent_debug.bat
echo cd C:\cape-agent >> C:\cape-agent\start_agent_debug.bat
echo echo Connecting to *************:2042... >> C:\cape-agent\start_agent_debug.bat
echo python agent.py ************* 2042 >> C:\cape-agent\start_agent_debug.bat
echo pause >> C:\cape-agent\start_agent_debug.bat

REM Create auto-start script (no pause)
echo @echo off > C:\cape-agent\start_agent.bat
echo cd C:\cape-agent >> C:\cape-agent\start_agent.bat
echo python agent.py ************* 2042 >> C:\cape-agent\start_agent.bat

REM Add to startup
echo Adding to startup...
copy C:\cape-agent\start_agent.bat "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\"

echo.
echo === Installation Complete ===
echo.
echo To test manually: run start_agent_debug.bat
echo Agent will auto-start on next boot
echo.
pause
EOF

# Tạo manual test script
cat > /tmp/cape-agent-fix/test_agent_manual.bat << 'EOF'
@echo off
echo === Manual Agent Test ===
cd C:\cape-agent

echo Testing network connectivity...
ping -n 1 *************

echo Testing port 2042...
telnet ************* 2042

echo Starting agent manually...
python agent.py ************* 2042
EOF

# Tạo ISO
genisoimage -o /var/lib/libvirt/images/iso/cape-agent-debug.iso -V "CAPE_AGENT_DEBUG" -r -J /tmp/cape-agent-fix/

echo "✓ Debug agent ISO created: /var/lib/libvirt/images/iso/cape-agent-debug.iso"
echo ""
echo "Next steps:"
echo "1. Attach ISO to VMs"
echo "2. Run install_agent_debug.bat as Administrator"
echo "3. Test with test_agent_manual.bat"
echo "4. Check connectivity and debug output"
```

### 2.2. Fix behavior processing module
```bash
#!/bin/bash
# File: fix_behavior_module.sh

CAPE_DIR="/opt/CAPEv2"

echo "Fixing behavior processing module..."

# Backup original
if [ -f "$CAPE_DIR/modules/processing/behavior.py" ]; then
    cp "$CAPE_DIR/modules/processing/behavior.py" "$CAPE_DIR/modules/processing/behavior.py.backup"
fi

# Ensure behavior is enabled in processing.conf
sed -i '/\[behavior\]/,/^\[/ s/enabled = no/enabled = yes/' $CAPE_DIR/conf/processing.conf

# Check if behavior module exists and is valid
if [ -f "$CAPE_DIR/modules/processing/behavior.py" ]; then
    echo "✓ Behavior module exists"
    
    # Test if module can be imported
    cd $CAPE_DIR
    python3 -c "
try:
    from modules.processing.behavior import Behavior
    print('✓ Behavior module can be imported')
except Exception as e:
    print(f'✗ Behavior module import error: {e}')
"
else
    echo "✗ Behavior module missing - this is a problem!"
fi

# Ensure processing.conf has correct behavior config
echo ""
echo "Checking processing.conf behavior section:"
grep -A 5 -B 1 "\[behavior\]" $CAPE_DIR/conf/processing.conf

echo ""
echo "Behavior module fix completed"
```

## BƯỚC 3: Manual VM Setup

### 3.1. Hướng dẫn setup agent trong VMs

#### Trong cape1 (Windows 10):
```cmd
1. Attach ISO: sudo virsh attach-disk cape1 /var/lib/libvirt/images/iso/cape-agent-debug.iso hdc --type cdrom --mode readonly

2. Trong VM:
   - Mở File Explorer, vào CD drive
   - Chạy install_agent_debug.bat as Administrator
   - Kiểm tra output có lỗi gì không

3. Test manual:
   - Chạy test_agent_manual.bat
   - Xem có kết nối được không

4. Check network:
   - ping *************
   - telnet ************* 2042

5. Start agent manual:
   - cd C:\cape-agent
   - python agent.py ************* 2042
   - Để chạy và xem có lỗi gì
```

#### Trong cuckoo1 (Windows 7):
```cmd
Làm tương tự như cape1
```

### 3.2. Test từ host khi agent chạy
```bash
#!/bin/bash
# File: test_agent_running.sh

echo "Testing when agent is running in VMs..."

# Check result server logs
echo "1. Result server connections:"
netstat -an | grep :2042

# Check CAPEv2 logs for agent connections
echo ""
echo "2. Recent agent connections in logs:"
tail -50 /opt/CAPEv2/log/cuckoo.log | grep -i -E "(agent|connect|guest)"

# Submit test analysis
echo ""
echo "3. Submitting test analysis..."
cd /opt/CAPEv2

# Create simple test file
echo "print('Hello from test')" > /tmp/test_simple.py

# Submit with debug
python3 utils/submit.py --timeout 60 --debug /tmp/test_simple.py

echo ""
echo "Check web interface at http://localhost:8000 for results"
```

## BƯỚC 4: Complete Fix Script

```bash
#!/bin/bash
# File: complete_behavior_fix.sh

echo "=== COMPLETE BEHAVIOR ANALYSIS FIX ==="

# 1. Debug current state
echo "Step 1: Running comprehensive debug..."
./debug_behavior_comprehensive.sh > debug_output.txt
echo "Debug output saved to debug_output.txt"

# 2. Fix behavior module
echo ""
echo "Step 2: Fixing behavior module..."
./fix_behavior_module.sh

# 3. Create new agent setup
echo ""
echo "Step 3: Creating new agent setup..."
./create_new_agent_setup.sh

# 4. Restart CAPEv2
echo ""
echo "Step 4: Restarting CAPEv2..."
sudo systemctl restart cape

echo ""
echo "=== FIX COMPLETED ==="
echo ""
echo "MANUAL STEPS REQUIRED:"
echo "1. Attach debug ISO to VMs:"
echo "   sudo virsh attach-disk cape1 /var/lib/libvirt/images/iso/cape-agent-debug.iso hdc --type cdrom --mode readonly"
echo "   sudo virsh attach-disk cuckoo1 /var/lib/libvirt/images/iso/cape-agent-debug.iso hdc --type cdrom --mode readonly"
echo ""
echo "2. In each VM:"
echo "   - Run install_agent_debug.bat as Administrator"
echo "   - Test with test_agent_manual.bat"
echo "   - Ensure agent connects to *************:2042"
echo ""
echo "3. Test analysis:"
echo "   cd /opt/CAPEv2"
echo "   python3 utils/submit.py --timeout 60 /tmp/test_simple.py"
echo ""
echo "4. Check results at http://localhost:8000"
```

## CÁCH SỬ DỤNG:

```bash
# 1. Run complete fix
chmod +x *.sh
sudo ./complete_behavior_fix.sh

# 2. Follow manual steps in output

# 3. Test agent connection
./test_agent_running.sh

# 4. Submit test analysis and check results
```

Vấn đề Behavior Analysis rỗng thường do agent không kết nối được. Script này sẽ debug toàn diện và fix vấn đề!
