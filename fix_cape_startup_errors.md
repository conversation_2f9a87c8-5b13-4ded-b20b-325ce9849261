# Fix CAPEv2 Startup Errors

## Lỗi từ log:
```
1. Group pcap does not exist.
2. Unable to import libvirt
3. tcpdump permission problems
4. pam_unix(sudo:auth): conversation failed
```

## LỖI 1: Group pcap không tồn tại (🖥️ CHẠY TRÊN HOST)

### Fix pcap group và tcpdump permissions:
```bash
# 1. Tạo pcap group
sudo groupadd pcap

# 2. Add user hiện tại vào pcap group
sudo usermod -a -G pcap $(whoami)

# 3. Set group ownership cho tcpdump
sudo chgrp pcap /usr/bin/tcpdump

# 4. Set capabilities cho tcpdump
sudo setcap cap_net_raw,cap_net_admin=eip /usr/bin/tcpdump

# 5. Verify tcpdump permissions
ls -la /usr/bin/tcpdump
getcap /usr/bin/tcpdump

# 6. Test tcpdump (should work without sudo)
timeout 2 tcpdump -i any -c 1 2>/dev/null && echo "✅ tcpdump works" || echo "❌ tcpdump failed"
```

## LỖI 2: Libvirt import error (🖥️ CHẠY TRÊN HOST)

### Install libvirt Python bindings:
```bash
# 1. Install libvirt development packages
sudo apt update
sudo apt install -y libvirt-dev pkg-config

# 2. Install Python libvirt bindings
pip3 install libvirt-python

# 3. Install additional dependencies từ log
cd /opt/CAPEv2/
pip3 install certvalidator asn1crypto mscerts

# 4. Test libvirt import
python3 -c "import libvirt; print('✅ libvirt import successful')" || echo "❌ libvirt import failed"

# 5. Check libvirt service
sudo systemctl status libvirtd
sudo systemctl enable libvirtd
sudo systemctl start libvirtd
```

## LỖI 3: Sudo password required (🖥️ CHẠY TRÊN HOST)

### Setup passwordless sudo cho tcpdump:
```bash
# 1. Tạo sudoers file cho tcpdump
sudo tee /etc/sudoers.d/tcpdump << EOF
$(whoami) ALL=NOPASSWD: /usr/bin/tcpdump
EOF

# 2. Set correct permissions
sudo chmod 440 /etc/sudoers.d/tcpdump

# 3. Verify sudoers syntax
sudo visudo -c

# 4. Test sudo tcpdump (should not ask password)
sudo tcpdump --version && echo "✅ sudo tcpdump works" || echo "❌ sudo tcpdump failed"
```

## LỖI 4: User permissions (🖥️ CHẠY TRÊN HOST)

### Add user vào các groups cần thiết:
```bash
# 1. Add user vào libvirt group
sudo usermod -a -G libvirt $(whoami)

# 2. Add user vào kvm group
sudo usermod -a -G kvm $(whoami)

# 3. Add user vào pcap group (đã làm ở trên)
sudo usermod -a -G pcap $(whoami)

# 4. Verify groups
groups $(whoami)

# 5. Apply group changes (logout/login hoặc newgrp)
newgrp libvirt
newgrp kvm
newgrp pcap
```

## SCRIPT FIX HOÀN CHỈNH (🖥️ CHẠY TRÊN HOST)

```bash
#!/bin/bash
# Complete fix for CAPEv2 startup errors

echo "=== FIXING CAPEV2 STARTUP ERRORS ==="

CURRENT_USER=$(whoami)
echo "Current user: $CURRENT_USER"

# 1. Fix pcap group and tcpdump
echo "1. Fixing pcap group and tcpdump permissions..."
sudo groupadd pcap 2>/dev/null || echo "pcap group already exists"
sudo usermod -a -G pcap $CURRENT_USER
sudo chgrp pcap /usr/bin/tcpdump
sudo setcap cap_net_raw,cap_net_admin=eip /usr/bin/tcpdump

# 2. Fix libvirt dependencies
echo "2. Installing libvirt dependencies..."
sudo apt update
sudo apt install -y libvirt-dev pkg-config python3-libvirt

# Install Python packages
cd /opt/CAPEv2/
pip3 install libvirt-python certvalidator asn1crypto mscerts

# 3. Fix sudo permissions
echo "3. Setting up passwordless sudo for tcpdump..."
sudo tee /etc/sudoers.d/tcpdump << EOF
$CURRENT_USER ALL=NOPASSWD: /usr/bin/tcpdump
EOF
sudo chmod 440 /etc/sudoers.d/tcpdump

# 4. Add user to required groups
echo "4. Adding user to required groups..."
sudo usermod -a -G libvirt $CURRENT_USER
sudo usermod -a -G kvm $CURRENT_USER
sudo usermod -a -G pcap $CURRENT_USER

# 5. Start required services
echo "5. Starting required services..."
sudo systemctl enable libvirtd
sudo systemctl start libvirtd

# 6. Test fixes
echo "6. Testing fixes..."
echo "Testing tcpdump:"
timeout 2 tcpdump -i any -c 1 2>/dev/null && echo "✅ tcpdump works" || echo "❌ tcpdump failed"

echo "Testing libvirt:"
python3 -c "import libvirt; print('✅ libvirt import successful')" 2>/dev/null || echo "❌ libvirt import failed"

echo "Testing sudo tcpdump:"
sudo tcpdump --version >/dev/null 2>&1 && echo "✅ sudo tcpdump works" || echo "❌ sudo tcpdump failed"

echo "User groups:"
groups $CURRENT_USER

echo "✅ All fixes applied!"
echo ""
echo "IMPORTANT: You may need to logout and login again for group changes to take effect"
echo "Or run: newgrp libvirt && newgrp kvm && newgrp pcap"
```

## RESTART CAPEV2 (🖥️ CHẠY TRÊN HOST)

### Sau khi fix, restart CAPEv2:
```bash
# 1. Apply group changes
newgrp libvirt
newgrp kvm  
newgrp pcap

# 2. Restart CAPEv2
sudo systemctl restart cape

# 3. Check service status
sudo systemctl status cape

# 4. Check logs
sudo journalctl -u cape -f --no-pager

# 5. Verify no more errors
sudo journalctl -u cape --since "1 minute ago" | grep -E "(ERROR|CRITICAL)"
```

## VERIFY FIX (🖥️ CHẠY TRÊN HOST)

### Test từng component:
```bash
# 1. Test tcpdump
echo "Testing tcpdump:"
timeout 2 tcpdump -i any -c 1 2>/dev/null && echo "✅ tcpdump OK" || echo "❌ tcpdump FAIL"

# 2. Test libvirt
echo "Testing libvirt:"
python3 -c "import libvirt; print('✅ libvirt OK')" 2>/dev/null || echo "❌ libvirt FAIL"

# 3. Test sudo tcpdump
echo "Testing sudo tcpdump:"
sudo tcpdump --version >/dev/null 2>&1 && echo "✅ sudo tcpdump OK" || echo "❌ sudo tcpdump FAIL"

# 4. Test groups
echo "User groups:"
groups $(whoami) | grep -E "(libvirt|kvm|pcap)" && echo "✅ Groups OK" || echo "❌ Groups missing"

# 5. Test CAPEv2 service
echo "CAPEv2 service:"
sudo systemctl is-active cape && echo "✅ CAPEv2 running" || echo "❌ CAPEv2 not running"
```

## TROUBLESHOOTING

### Nếu vẫn có lỗi libvirt:
```bash
# Install thêm packages
sudo apt install -y python3-dev libxml2-dev libxslt-dev
pip3 install --upgrade libvirt-python

# Check libvirt connection
python3 -c "import libvirt; conn = libvirt.open('qemu:///system'); print('✅ libvirt connection OK')"
```

### Nếu vẫn có lỗi tcpdump:
```bash
# Alternative: Use sudo approach
sudo tee /etc/sudoers.d/cape-tcpdump << EOF
$(whoami) ALL=NOPASSWD: /usr/bin/tcpdump
EOF

# Test
sudo tcpdump --version
```

### Nếu vẫn có lỗi groups:
```bash
# Force logout/login
echo "Please logout and login again, then run:"
echo "sudo systemctl restart cape"
```

## LỆNH NHANH FIX TẤT CẢ:

```bash
# Fix all errors in one go
sudo groupadd pcap 2>/dev/null || true
sudo usermod -a -G pcap,libvirt,kvm $(whoami)
sudo chgrp pcap /usr/bin/tcpdump
sudo setcap cap_net_raw,cap_net_admin=eip /usr/bin/tcpdump
echo "$(whoami) ALL=NOPASSWD: /usr/bin/tcpdump" | sudo tee /etc/sudoers.d/tcpdump
sudo chmod 440 /etc/sudoers.d/tcpdump
sudo apt install -y libvirt-dev pkg-config python3-libvirt
pip3 install libvirt-python certvalidator asn1crypto mscerts
sudo systemctl enable libvirtd && sudo systemctl start libvirtd
newgrp libvirt && newgrp kvm && newgrp pcap
sudo systemctl restart cape
```

## KẾT QUẢ MONG ĐỢI:

✅ **Sau khi fix:**
- Group pcap tồn tại
- tcpdump hoạt động không cần sudo
- libvirt import thành công
- CAPEv2 start không lỗi
- Không còn permission errors

❌ **Trước khi fix:**
- Group pcap does not exist
- tcpdump permission denied
- libvirt import failed
- CAPEv2 startup failed

**CAPEv2 sẽ start thành công!** 🚀
