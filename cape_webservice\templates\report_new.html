{% extends "base.html" %}

{% block title %}Analysis Report - CAPE Web Service{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h3 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>Analysis Report
                </h3>
            </div>
            <div class="card-body">
                <!-- File Info Header -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="file-info">
                            <h5><i class="fas fa-file me-2"></i>File Information</h5>
                            <p><strong>Filename:</strong> {{ analysis.filename }}</p>
                            <p><strong>Analysis ID:</strong> <code>{{ analysis.id }}</code></p>
                            <p><strong>CAPE Task ID:</strong> <code>{{ analysis.cape_task_id }}</code></p>
                            {% if analysis.file_hash %}
                            <p><strong>File Hash:</strong> <code class="hash-value">{{ analysis.file_hash }}</code></p>
                            {% endif %}
                            {% if analysis.was_cached %}
                            <p><span class="badge bg-success"><i class="fas fa-database me-1"></i>Cached Result</span></p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="file-info">
                            <h5><i class="fas fa-clock me-2"></i>Analysis Timeline</h5>
                            {% if report and report.info %}
                            <p><strong>Started:</strong> {{ report.info.started if report.info.started else analysis.started_at }}</p>
                            <p><strong>Ended:</strong> {{ report.info.ended if report.info.ended else analysis.completed_at }}</p>
                            <p><strong>Duration:</strong> {{ "%.1f"|format(report.info.duration) if report.info.duration else 'N/A' }} seconds</p>
                            {% else %}
                            <p><strong>Created:</strong> {{ analysis.created_at }}</p>
                            <p><strong>Started:</strong> {{ analysis.started_at }}</p>
                            <p><strong>Completed:</strong> {{ analysis.completed_at }}</p>
                            {% endif %}
                            <p><strong>Mode:</strong> <span class="badge bg-primary">{{ analysis.analysis_mode.title() }}</span></p>
                        </div>
                    </div>
                </div>

                {% if report %}
                <!-- Navigation Tabs -->
                <ul class="nav nav-tabs" id="reportTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                            <i class="fas fa-chart-pie me-1"></i>Quick Overview
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="target-tab" data-bs-toggle="tab" data-bs-target="#target" type="button" role="tab">
                            <i class="fas fa-file me-1"></i>Target File
                        </button>
                    </li>
                    {% if report.behavior %}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="behavior-tab" data-bs-toggle="tab" data-bs-target="#behavior" type="button" role="tab">
                            <i class="fas fa-microscope me-1"></i>Behavior
                            <span class="badge bg-info ms-1">{{ report.behavior.processes|length if report.behavior.processes else 0 }}</span>
                        </button>
                    </li>
                    {% endif %}
                    {% if report.signatures %}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="signatures-tab" data-bs-toggle="tab" data-bs-target="#signatures" type="button" role="tab">
                            <i class="fas fa-exclamation-triangle me-1"></i>Signatures
                            <span class="badge bg-danger ms-1">{{ report.signatures|length }}</span>
                        </button>
                    </li>
                    {% endif %}
                    {% if report.network %}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="network-tab" data-bs-toggle="tab" data-bs-target="#network" type="button" role="tab">
                            <i class="fas fa-network-wired me-1"></i>Network
                            <span class="badge bg-warning ms-1">{{ report.network.hosts|length if report.network.hosts else 0 }}</span>
                        </button>
                    </li>
                    {% endif %}
                    {% if report.dropped or (report.CAPE and report.CAPE.payloads) %}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="files-tab" data-bs-toggle="tab" data-bs-target="#files" type="button" role="tab">
                            <i class="fas fa-file-code me-1"></i>Files & Payloads
                        </button>
                    </li>
                    {% endif %}
                    {% if report.static %}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="static-tab" data-bs-toggle="tab" data-bs-target="#static" type="button" role="tab">
                            <i class="fas fa-search-plus me-1"></i>Static Analysis
                        </button>
                    </li>
                    {% endif %}
                    {% if report.capa_summary %}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="capa-tab" data-bs-toggle="tab" data-bs-target="#capa" type="button" role="tab">
                            <i class="fas fa-shield-alt me-1"></i>CAPA
                        </button>
                    </li>
                    {% endif %}
                    {% if report.statistics %}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="statistics-tab" data-bs-toggle="tab" data-bs-target="#statistics" type="button" role="tab">
                            <i class="fas fa-chart-bar me-1"></i>Statistics
                        </button>
                    </li>
                    {% endif %}
                    {% if report.strings %}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="strings-tab" data-bs-toggle="tab" data-bs-target="#strings" type="button" role="tab">
                            <i class="fas fa-font me-1"></i>Strings
                            <span class="badge bg-secondary ms-1">{{ report.strings|length }}</span>
                        </button>
                    </li>
                    {% endif %}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="raw-tab" data-bs-toggle="tab" data-bs-target="#raw" type="button" role="tab">
                            <i class="fas fa-code me-1"></i>Raw Data
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content mt-4" id="reportTabsContent">
                    
                    <!-- Quick Overview Tab -->
                    <div class="tab-pane fade show active" id="overview" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-chart-pie me-2"></i>Analysis Summary</h4>
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-shield-virus fa-2x text-danger mb-2"></i>
                                            <h6>Malware Score</h6>
                                            <h4 class="text-danger">{{ report.info.score if report.info and report.info.score else 'N/A' }}</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                                            <h6>Signatures</h6>
                                            <h4 class="text-warning">{{ report.signatures|length if report.signatures else 0 }}</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-network-wired fa-2x text-info mb-2"></i>
                                            <h6>Network Hosts</h6>
                                            <h4 class="text-info">{{ report.network.hosts|length if report.network and report.network.hosts else 0 }}</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-file-code fa-2x text-success mb-2"></i>
                                            <h6>Dropped Files</h6>
                                            <h4 class="text-success">{{ report.dropped|length if report.dropped else 0 }}</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-cogs fa-2x text-primary mb-2"></i>
                                            <h6>Processes</h6>
                                            <h4 class="text-primary">{{ report.behavior.processes|length if report.behavior and report.behavior.processes else 0 }}</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-clock fa-2x text-secondary mb-2"></i>
                                            <h6>Duration</h6>
                                            <h4 class="text-secondary">{{ "%.1f"|format(report.info.duration) if report.info and report.info.duration else 'N/A' }}s</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Key Findings -->
                        <div class="report-section">
                            <h4><i class="fas fa-key me-2"></i>Key Findings</h4>
                            <div class="row">
                                <!-- High Severity Signatures -->
                                {% if report.signatures %}
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-danger text-white">
                                            <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Critical Detections</h6>
                                        </div>
                                        <div class="card-body">
                                            {% set high_sigs = report.signatures | selectattr('severity', 'equalto', 3) | list %}
                                            {% if high_sigs %}
                                            {% for sig in high_sigs[:5] %}
                                            <div class="mb-2">
                                                <span class="badge bg-danger me-2">HIGH</span>
                                                <strong>{{ sig.description }}</strong>
                                            </div>
                                            {% endfor %}
                                            {% if high_sigs|length > 5 %}
                                            <small class="text-muted">... and {{ high_sigs|length - 5 }} more high severity detections</small>
                                            {% endif %}
                                            {% else %}
                                            <p class="text-muted mb-0">No high severity signatures detected</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                <!-- Network Activity Summary -->
                                {% if report.network %}
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0"><i class="fas fa-network-wired me-2"></i>Network Activity</h6>
                                        </div>
                                        <div class="card-body">
                                            {% if report.network.hosts %}
                                            <p><strong>{{ report.network.hosts|length }}</strong> unique hosts contacted</p>
                                            {% for host in report.network.hosts[:3] %}
                                            <div class="mb-1">
                                                <code class="hash-value">{{ host.ip }}</code>
                                                {% if host.country_name %}
                                                <span class="badge bg-secondary">{{ host.country_name }}</span>
                                                {% endif %}
                                            </div>
                                            {% endfor %}
                                            {% if report.network.hosts|length > 3 %}
                                            <small class="text-muted">... and {{ report.network.hosts|length - 3 }} more hosts</small>
                                            {% endif %}
                                            {% else %}
                                            <p class="text-muted mb-0">No network activity detected</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Target File Tab -->
                    <div class="tab-pane fade" id="target" role="tabpanel">
                        {% if report.target and report.target.file %}
                        <div class="report-section">
                            <h4><i class="fas fa-file me-2"></i>Target File Information</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <tr><th>File Name</th><td><strong>{{ report.target.file.name }}</strong></td></tr>
                                            <tr><th>File Size</th><td>{{ "{:,}".format(report.target.file.size) }} bytes ({{ "%.2f"|format(report.target.file.size / 1024 / 1024) }} MB)</td></tr>
                                            <tr><th>File Type</th><td>{{ report.target.file.type }}</td></tr>
                                            <tr><th>MIME Type</th><td>{{ report.target.file.mime if report.target.file.mime else 'N/A' }}</td></tr>
                                            <tr><th>CRC32</th><td><code class="hash-value">{{ report.target.file.crc32 if report.target.file.crc32 else 'N/A' }}</code></td></tr>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <tr><th>MD5</th><td><code class="hash-value">{{ report.target.file.md5 }}</code></td></tr>
                                            <tr><th>SHA1</th><td><code class="hash-value">{{ report.target.file.sha1 }}</code></td></tr>
                                            <tr><th>SHA256</th><td><code class="hash-value">{{ report.target.file.sha256 }}</code></td></tr>
                                            <tr><th>SHA512</th><td><code class="hash-value">{{ report.target.file.sha512 if report.target.file.sha512 else 'N/A' }}</code></td></tr>
                                            <tr><th>SSDEEP</th><td><code class="hash-value">{{ report.target.file.ssdeep if report.target.file.ssdeep else 'N/A' }}</code></td></tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        {% if report.info %}
                        <div class="report-section">
                            <h4><i class="fas fa-info-circle me-2"></i>Analysis Information</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <tr><th>Analysis ID</th><td>{{ report.info.id if report.info.id else 'N/A' }}</td></tr>
                                            <tr><th>Category</th><td>{{ report.info.category if report.info.category else 'N/A' }}</td></tr>
                                            <tr><th>Package</th><td>{{ report.info.package if report.info.package else 'N/A' }}</td></tr>
                                            <tr><th>Timeout</th><td>{{ report.info.timeout if report.info.timeout else 'N/A' }}</td></tr>
                                            <tr><th>Version</th><td>{{ report.info.version if report.info.version else 'N/A' }}</td></tr>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <tr><th>Machine</th><td>{{ report.info.machine.name if report.info.machine and report.info.machine.name else 'N/A' }}</td></tr>
                                            <tr><th>Platform</th><td>{{ report.info.machine.platform if report.info.machine and report.info.machine.platform else 'N/A' }}</td></tr>
                                            <tr><th>Manager</th><td>{{ report.info.machine.manager if report.info.machine and report.info.machine.manager else 'N/A' }}</td></tr>
                                            <tr><th>Route</th><td>{{ report.info.route if report.info.route else 'Default' }}</td></tr>
                                            <tr><th>User ID</th><td>{{ report.info.user_id if report.info.user_id else 'N/A' }}</td></tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Include other tabs -->
                    {% include 'report_tabs.html' %}

                    <!-- Files & Payloads Tab -->
                    {% if report.dropped or (report.CAPE and report.CAPE.payloads) %}
                    <div class="tab-pane fade" id="files" role="tabpanel">
                        <!-- CAPE Payloads -->
                        {% if report.CAPE and report.CAPE.payloads %}
                        <div class="report-section">
                            <h4><i class="fas fa-bug me-2"></i>CAPE Extracted Payloads ({{ report.CAPE.payloads|length }})</h4>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Type</th>
                                            <th>Module</th>
                                            <th>Size</th>
                                            <th>SHA256</th>
                                            <th>Yara</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for payload in report.CAPE.payloads %}
                                        <tr>
                                            <td><span class="badge bg-danger">{{ payload.type if payload.type else 'Unknown' }}</span></td>
                                            <td><small>{{ payload.module_path if payload.module_path else 'N/A' }}</small></td>
                                            <td>{{ "{:,}".format(payload.size) if payload.size else 'N/A' }} bytes</td>
                                            <td><code class="hash-value">{{ payload.sha256 if payload.sha256 else 'N/A' }}</code></td>
                                            <td>
                                                {% if payload.cape_yara %}
                                                {% for yara in payload.cape_yara %}
                                                <span class="badge bg-warning me-1">{{ yara.name }}</span>
                                                {% endfor %}
                                                {% else %}
                                                N/A
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Dropped Files -->
                        {% if report.dropped %}
                        <div class="report-section">
                            <h4><i class="fas fa-file-code me-2"></i>Dropped Files ({{ report.dropped|length }})</h4>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>File Name</th>
                                            <th>Path</th>
                                            <th>Size</th>
                                            <th>Type</th>
                                            <th>MD5</th>
                                            <th>Yara</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for file in report.dropped %}
                                        <tr>
                                            <td><strong>{{ file.name }}</strong></td>
                                            <td><small>{{ file.path if file.path else 'N/A' }}</small></td>
                                            <td>{{ "{:,}".format(file.size) if file.size else 'N/A' }} bytes</td>
                                            <td>{{ file.type if file.type else 'Unknown' }}</td>
                                            <td><code class="hash-value">{{ file.md5 }}</code></td>
                                            <td>
                                                {% if file.yara %}
                                                {% for yara in file.yara %}
                                                <span class="badge bg-info me-1">{{ yara.name }}</span>
                                                {% endfor %}
                                                {% else %}
                                                N/A
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- Static Analysis Tab -->
                    {% if report.static %}
                    <div class="tab-pane fade" id="static" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-search-plus me-2"></i>Static Analysis</h4>

                            <!-- PE Information -->
                            {% if report.static.pe %}
                            <div class="mb-4">
                                <h5><i class="fas fa-file-alt me-2"></i>PE Information</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <tr><th>Machine</th><td>{{ report.static.pe.machine if report.static.pe.machine else 'N/A' }}</td></tr>
                                                <tr><th>Timestamp</th><td>{{ report.static.pe.timestamp if report.static.pe.timestamp else 'N/A' }}</td></tr>
                                                <tr><th>Entry Point</th><td><code>{{ report.static.pe.entrypoint if report.static.pe.entrypoint else 'N/A' }}</code></td></tr>
                                                <tr><th>Image Base</th><td><code>{{ report.static.pe.imagebase if report.static.pe.imagebase else 'N/A' }}</code></td></tr>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <tr><th>Sections</th><td>{{ report.static.pe.sections|length if report.static.pe.sections else 0 }}</td></tr>
                                                <tr><th>Imports</th><td>{{ report.static.pe.imports|length if report.static.pe.imports else 0 }}</td></tr>
                                                <tr><th>Exports</th><td>{{ report.static.pe.exports|length if report.static.pe.exports else 0 }}</td></tr>
                                                <tr><th>Resources</th><td>{{ report.static.pe.resources|length if report.static.pe.resources else 0 }}</td></tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}

                            <!-- Imports -->
                            {% if report.static.pe and report.static.pe.imports %}
                            <div class="mb-4">
                                <h5><i class="fas fa-download me-2"></i>Imported Functions</h5>
                                <div class="row">
                                    {% for dll, functions in report.static.pe.imports.items() %}
                                    <div class="col-md-6 mb-3">
                                        <h6>{{ dll }}</h6>
                                        <div class="bg-light p-2 rounded">
                                            {% for func in functions %}
                                            <span class="badge bg-secondary me-1 mb-1">{{ func }}</span>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- CAPA Tab -->
                    {% if report.capa_summary %}
                    <div class="tab-pane fade" id="capa" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-shield-alt me-2"></i>CAPA Analysis</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <tr><th>MD5</th><td><code class="hash-value">{{ report.capa_summary.md5 }}</code></td></tr>
                                            <tr><th>SHA1</th><td><code class="hash-value">{{ report.capa_summary.sha1 }}</code></td></tr>
                                            <tr><th>SHA256</th><td><code class="hash-value">{{ report.capa_summary.sha256 }}</code></td></tr>
                                            <tr><th>Path</th><td><small>{{ report.capa_summary.path }}</small></td></tr>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <tr><th>ATT&CK Techniques</th><td>{{ report.capa_summary.ATTCK|length if report.capa_summary.ATTCK else 0 }}</td></tr>
                                            <tr><th>MBC Behaviors</th><td>{{ report.capa_summary.MBC|length if report.capa_summary.MBC else 0 }}</td></tr>
                                            <tr><th>Capabilities</th><td>{{ report.capa_summary.CAPABILITY|length if report.capa_summary.CAPABILITY else 0 }}</td></tr>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- CAPA Details -->
                            {% if report.capa_summary.CAPABILITY %}
                            <div class="mb-4">
                                <h5><i class="fas fa-list me-2"></i>Detected Capabilities</h5>
                                <div class="row">
                                    {% for capability, details in report.capa_summary.CAPABILITY.items() %}
                                    <div class="col-md-6 mb-2">
                                        <span class="badge bg-info me-1">{{ capability }}</span>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Statistics Tab -->
                    {% if report.statistics %}
                    <div class="tab-pane fade" id="statistics" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-chart-bar me-2"></i>Processing Statistics</h4>

                            <!-- Processing Times -->
                            {% if report.statistics.processing %}
                            <div class="mb-4">
                                <h5><i class="fas fa-clock me-2"></i>Processing Times</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Module</th>
                                                <th>Time (seconds)</th>
                                                <th>Percentage</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% set total_time = report.statistics.processing | sum(attribute='time') %}
                                            {% for module in report.statistics.processing %}
                                            <tr>
                                                <td><strong>{{ module.name }}</strong></td>
                                                <td>{{ "%.3f"|format(module.time) }}</td>
                                                <td>
                                                    {% if total_time > 0 %}
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar" role="progressbar" style="--progress-width: {{ (module.time / total_time * 100)|round(1) }}%; width: var(--progress-width)">
                                                            {{ (module.time / total_time * 100)|round(1) }}%
                                                        </div>
                                                    </div>
                                                    {% else %}
                                                    0%
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}

                            <!-- Signature Times -->
                            {% if report.statistics.signatures %}
                            <div class="mb-4">
                                <h5><i class="fas fa-stopwatch me-2"></i>Signature Processing Times</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Signature</th>
                                                <th>Time (seconds)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for sig in report.statistics.signatures %}
                                            <tr>
                                                <td>{{ sig.name }}</td>
                                                <td>{{ "%.6f"|format(sig.time) }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Strings Tab -->
                    {% if report.strings %}
                    <div class="tab-pane fade" id="strings" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-font me-2"></i>Extracted Strings ({{ report.strings|length }})</h4>
                            <div class="mb-3">
                                <input type="text" class="form-control" id="stringSearch" placeholder="Search strings...">
                            </div>
                            <div class="bg-light p-3 rounded" style="max-height: 600px; overflow-y: auto;" id="stringsContainer">
                                {% for string in report.strings %}
                                <div class="mb-1 string-item">
                                    <code>{{ string }}</code>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Raw Data Tab -->
                    <div class="tab-pane fade" id="raw" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-code me-2"></i>Raw Report Data</h4>
                            <div class="mb-3">
                                <button class="btn btn-outline-primary" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#rawJsonCollapse" aria-expanded="false">
                                    <i class="fas fa-eye me-2"></i>Show/Hide Raw JSON
                                </button>
                                <button class="btn btn-outline-secondary ms-2" onclick="downloadJson()">
                                    <i class="fas fa-download me-2"></i>Download JSON
                                </button>
                            </div>
                            <div class="collapse" id="rawJsonCollapse">
                                <div class="json-viewer">
                                    <pre id="rawJsonData">{{ report | tojson(indent=2) }}</pre>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                {% endif %}

                <!-- Actions -->
                <div class="text-center mt-4">
                    {% if analysis.status == 'completed' and analysis.id != 'json-upload' %}
                    <a href="{{ url_for('analysis_status', analysis_id=analysis.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line me-2"></i>Back to Status
                    </a>
                    {% endif %}
                    <a href="{{ url_for('list_analyses') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-2"></i>All Analyses
                    </a>
                    <a href="{{ url_for('index') }}" class="btn btn-outline-success">
                        <i class="fas fa-upload me-2"></i>Upload Another File
                    </a>
                    <a href="{{ url_for('json_viewer') }}" class="btn btn-outline-info">
                        <i class="fas fa-file-code me-2"></i>JSON Viewer
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Function to download JSON report
function downloadJson() {
    const jsonData = document.getElementById('rawJsonData').textContent;
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '{{ analysis.cape_task_id }}_report.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Function to copy text to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showToast('Copied to clipboard!', 'success');
    });
}

// Show toast notification
function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.style.position = 'fixed';
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-check me-2"></i>${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    toast.addEventListener('hidden.bs.toast', function() {
        document.body.removeChild(toast);
    });
}

// Initialize page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers to copy hash values
    const hashElements = document.querySelectorAll('.hash-value');
    hashElements.forEach(function(element) {
        element.style.cursor = 'pointer';
        element.title = 'Click to copy';
        element.addEventListener('click', function() {
            copyToClipboard(element.textContent);
        });
    });

    // String search functionality
    const stringSearch = document.getElementById('stringSearch');
    if (stringSearch) {
        stringSearch.addEventListener('keyup', function() {
            const filter = this.value.toLowerCase();
            const strings = document.querySelectorAll('.string-item');
            strings.forEach(function(item) {
                const text = item.textContent.toLowerCase();
                item.style.display = text.includes(filter) ? '' : 'none';
            });
        });
    }

    // Add search functionality to large tables
    const tables = document.querySelectorAll('.table');
    tables.forEach(function(table, index) {
        if (table.rows.length > 10) {
            const searchInput = document.createElement('input');
            searchInput.type = 'text';
            searchInput.className = 'form-control mb-2';
            searchInput.placeholder = 'Search in table...';
            searchInput.addEventListener('keyup', function() {
                const filter = this.value.toLowerCase();
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(function(row) {
                    const text = row.textContent.toLowerCase();
                    row.style.display = text.includes(filter) ? '' : 'none';
                });
            });
            table.parentNode.insertBefore(searchInput, table);
        }
    });
});

// Auto-refresh functionality for pending analyses
{% if analysis.status in ['pending', 'running'] %}
setTimeout(function() {
    location.reload();
}, 30000);
{% endif %}
</script>
{% endblock %}
