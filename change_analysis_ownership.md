# Change Analysis Files Ownership từ cape sang user hiện tại

## Vấn đề:
- Analysis files thuộc về user `cape`
- User hiện tại không thể access/modify files
- Cần chuyển ownership sang user hiện tại

## GIẢI PHÁP 1: Chuyển ownership toàn bộ (🖥️ CHẠY TRÊN HOST)

### Bước 1: Kiểm tra ownership hiện tại
```bash
# Check user hiện tại
whoami

# Check ownership của CAPEv2 directory
ls -la /opt/CAPEv2/

# Check ownership của storage directory
ls -la /opt/CAPEv2/storage/

# Check ownership của analyses directory
ls -la /opt/CAPEv2/storage/analyses/

# Check ownership của analysis files cụ thể
ls -la /opt/CAPEv2/storage/analyses/*/
```

### Bước 2: Stop CAPEv2 services
```bash
# Stop CAPEv2 để tránh conflict
sudo systemctl stop cape
sudo systemctl stop cape-web 2>/dev/null || true

# Verify services stopped
sudo systemctl status cape
```

### Bước 3: Chuyển ownership sang user hiện tại
```bash
# Get current user
CURRENT_USER=$(whoami)
echo "Current user: $CURRENT_USER"

# Change ownership của toàn bộ CAPEv2
sudo chown -R $CURRENT_USER:$CURRENT_USER /opt/CAPEv2/

# Verify ownership changed
ls -la /opt/CAPEv2/ | head -10
ls -la /opt/CAPEv2/storage/analyses/ | head -10
```

### Bước 4: Update systemd service
```bash
# Update systemd service để chạy với user hiện tại
CURRENT_USER=$(whoami)

sudo tee /etc/systemd/system/cape.service << EOF
[Unit]
Description=CAPE Sandbox
After=network.target postgresql.service

[Service]
Type=simple
User=$CURRENT_USER
Group=$CURRENT_USER
WorkingDirectory=/opt/CAPEv2
ExecStart=/usr/bin/python3 /opt/CAPEv2/cuckoo.py
Restart=always
RestartSec=10
Environment=PYTHONPATH=/opt/CAPEv2

[Install]
WantedBy=multi-user.target
EOF

# Update web service nếu có
if [ -f "/etc/systemd/system/cape-web.service" ]; then
    sudo tee /etc/systemd/system/cape-web.service << EOF
[Unit]
Description=CAPE Web Interface
After=network.target cape.service

[Service]
Type=simple
User=$CURRENT_USER
Group=$CURRENT_USER
WorkingDirectory=/opt/CAPEv2
ExecStart=/usr/bin/python3 /opt/CAPEv2/utils/web.py --host 0.0.0.0 --port 8000
Restart=always
RestartSec=10
Environment=PYTHONPATH=/opt/CAPEv2

[Install]
WantedBy=multi-user.target
EOF
fi

# Reload systemd
sudo systemctl daemon-reload
```

### Bước 5: Restart services
```bash
# Start CAPEv2 với user mới
sudo systemctl start cape

# Check service status
sudo systemctl status cape

# Start web service nếu có
sudo systemctl start cape-web 2>/dev/null || true

# Verify services running với user đúng
ps aux | grep -E "(cuckoo|cape)" | grep -v grep
```

## GIẢI PHÁP 2: Chỉ chuyển ownership analysis files (🖥️ CHẠY TRÊN HOST)

### Nếu chỉ muốn chuyển analysis files, không chuyển toàn bộ:
```bash
# Get current user
CURRENT_USER=$(whoami)

# Chỉ chuyển ownership storage directory
sudo chown -R $CURRENT_USER:$CURRENT_USER /opt/CAPEv2/storage/

# Chỉ chuyển ownership log directory
sudo chown -R $CURRENT_USER:$CURRENT_USER /opt/CAPEv2/log/

# Keep CAPEv2 core files với cape user, chỉ đổi data files
sudo chown -R $CURRENT_USER:$CURRENT_USER /opt/CAPEv2/storage/analyses/
sudo chown -R $CURRENT_USER:$CURRENT_USER /opt/CAPEv2/storage/binaries/
sudo chown -R $CURRENT_USER:$CURRENT_USER /opt/CAPEv2/storage/baseline/

# Verify
ls -la /opt/CAPEv2/storage/analyses/ | head -5
```

## GIẢI PHÁP 3: Tạo shared group (🖥️ CHẠY TRÊN HOST)

### Tạo group chung để cả cape và user hiện tại đều access được:
```bash
# Get current user
CURRENT_USER=$(whoami)

# Tạo group chung
sudo groupadd capev2-users 2>/dev/null || true

# Add users vào group
sudo usermod -a -G capev2-users $CURRENT_USER
sudo usermod -a -G capev2-users cape 2>/dev/null || true

# Change group ownership
sudo chgrp -R capev2-users /opt/CAPEv2/storage/
sudo chgrp -R capev2-users /opt/CAPEv2/log/

# Set group permissions
sudo chmod -R g+rw /opt/CAPEv2/storage/
sudo chmod -R g+rw /opt/CAPEv2/log/

# Set sticky bit để new files inherit group
sudo chmod -R g+s /opt/CAPEv2/storage/
sudo chmod -R g+s /opt/CAPEv2/log/

# Verify permissions
ls -la /opt/CAPEv2/storage/ | head -5
```

## BƯỚC KIỂM TRA: Test access (🖥️ CHẠY TRÊN HOST)

### Test read access:
```bash
# Test đọc analysis files
ls -la /opt/CAPEv2/storage/analyses/

# Test đọc analysis reports
cat /opt/CAPEv2/storage/analyses/*/reports/report.json | head -20 2>/dev/null || echo "No reports found"

# Test đọc logs
ls -la /opt/CAPEv2/log/
```

### Test write access:
```bash
# Test tạo file trong storage
touch /opt/CAPEv2/storage/test_write.txt
if [ $? -eq 0 ]; then
    echo "✅ Can write to storage directory"
    rm /opt/CAPEv2/storage/test_write.txt
else
    echo "❌ Cannot write to storage directory"
fi

# Test tạo analysis directory
mkdir -p /opt/CAPEv2/storage/analyses/test_analysis
if [ $? -eq 0 ]; then
    echo "✅ Can create analysis directories"
    rmdir /opt/CAPEv2/storage/analyses/test_analysis
else
    echo "❌ Cannot create analysis directories"
fi
```

## SCRIPT HOÀN CHỈNH: Change ownership

```bash
#!/bin/bash
# Complete ownership change script

echo "=== CHANGING CAPEV2 OWNERSHIP ==="

# Get current user
CURRENT_USER=$(whoami)
echo "Current user: $CURRENT_USER"

# Check if cape user exists
if id cape >/dev/null 2>&1; then
    echo "Cape user exists"
    CAPE_EXISTS=true
else
    echo "Cape user does not exist"
    CAPE_EXISTS=false
fi

# Stop services
echo "1. Stopping CAPEv2 services..."
sudo systemctl stop cape 2>/dev/null || true
sudo systemctl stop cape-web 2>/dev/null || true

# Change ownership
echo "2. Changing ownership to $CURRENT_USER..."
sudo chown -R $CURRENT_USER:$CURRENT_USER /opt/CAPEv2/

# Update systemd service
echo "3. Updating systemd service..."
sudo tee /etc/systemd/system/cape.service << EOF
[Unit]
Description=CAPE Sandbox
After=network.target postgresql.service

[Service]
Type=simple
User=$CURRENT_USER
Group=$CURRENT_USER
WorkingDirectory=/opt/CAPEv2
ExecStart=/usr/bin/python3 /opt/CAPEv2/cuckoo.py
Restart=always
RestartSec=10
Environment=PYTHONPATH=/opt/CAPEv2

[Install]
WantedBy=multi-user.target
EOF

# Reload and restart
echo "4. Reloading systemd and restarting services..."
sudo systemctl daemon-reload
sudo systemctl start cape

# Verify
echo "5. Verifying ownership and services..."
echo "Ownership:"
ls -la /opt/CAPEv2/ | head -3
ls -la /opt/CAPEv2/storage/ | head -3

echo "Service status:"
sudo systemctl status cape --no-pager -l

echo "Process user:"
ps aux | grep -E "(cuckoo|cape)" | grep -v grep | head -3

echo "✅ Ownership change completed!"
```

## TROUBLESHOOTING

### Nếu services không start:
```bash
# Check service logs
sudo journalctl -u cape -f

# Check permissions
ls -la /opt/CAPEv2/cuckoo.py

# Fix execute permissions
chmod +x /opt/CAPEv2/cuckoo.py
chmod +x /opt/CAPEv2/utils/*.py
```

### Nếu vẫn không access được files:
```bash
# Check SELinux
getenforce 2>/dev/null || echo "SELinux not installed"

# Reset permissions
sudo chmod -R 755 /opt/CAPEv2/
sudo chmod -R 644 /opt/CAPEv2/conf/*.conf
sudo chmod -R 755 /opt/CAPEv2/storage/
```

### Nếu muốn revert về cape user:
```bash
# Stop services
sudo systemctl stop cape

# Change back to cape
sudo chown -R cape:cape /opt/CAPEv2/

# Update service
sudo sed -i "s/User=$USER/User=cape/" /etc/systemd/system/cape.service
sudo sed -i "s/Group=$USER/Group=cape/" /etc/systemd/system/cape.service

# Restart
sudo systemctl daemon-reload
sudo systemctl start cape
```

## LỆNH NHANH:

```bash
# Chuyển ownership sang user hiện tại
CURRENT_USER=$(whoami)
sudo systemctl stop cape
sudo chown -R $CURRENT_USER:$CURRENT_USER /opt/CAPEv2/
sudo sed -i "s/User=cape/User=$CURRENT_USER/" /etc/systemd/system/cape.service
sudo sed -i "s/Group=cape/Group=$CURRENT_USER/" /etc/systemd/system/cape.service
sudo systemctl daemon-reload
sudo systemctl start cape
```

## KẾT QUẢ MONG ĐỢI:

✅ **Sau khi change ownership:**
- User hiện tại có full access đến analysis files
- Có thể đọc/ghi/modify analysis data
- CAPEv2 chạy với user hiện tại
- Không còn permission errors

❌ **Trước khi change:**
- Analysis files thuộc về cape user
- User hiện tại không access được
- Permission denied errors

**Analysis files sẽ thuộc về user hiện tại!** 🚀
