# CAPEv2 System Process Hooking Guide

## Vấn đề: <PERSON>h<PERSON><PERSON> hook được dllhost.exe, svchost.exe, explorer.exe

### Nguyên nhân chính:

1. **Windows Security Features**: Windows bảo vệ các system processes khỏi injection
2. **Insufficient Privileges**: CAPEv2 agent không có đủ quyền SYSTEM
3. **Windows Defender**: Chặn injection attempts
4. **Process Protection**: System processes có protection mechanisms
5. **DEP/ASLR**: Exploit mitigation features cản trở injection

## Gi<PERSON>i pháp chi tiết

### Bước 1: Chạy script khắc phục

```bash
# Chạy script khắc phục chính
./fix_process_hooking.sh

# Hoặc chạy script Python cụ thể
python3 fix_dllhost_svchost_hooking.py
```

### Bước 2: Setup Windows VM

1. **Copy script setup vào VM**:
   ```
   setup_vm_for_hooking.bat
   ```

2. **Chạy với quyền Administrator trong VM**

3. **Reboot VM sau khi setup**

### Bước 3: <PERSON><PERSON><PERSON> hình CAPEv2 Agent

1. **Đảm bảo agent chạy với SYSTEM privileges**:
   ```cmd
   sc config CAPEv2Agent obj= LocalSystem
   sc config CAPEv2Agent start= auto
   ```

2. **Kiểm tra agent service**:
   ```cmd
   sc query CAPEv2Agent
   ```

### Bước 4: Test injection

```bash
python3 test_injection.py
python3 test_system_process_hook.py
```

## Các thay đổi được thực hiện

### 1. Analyzer.py Modifications

- **SystemProcessHooker class**: Enhanced injection logic
- **Retry mechanism**: Multiple attempts for system processes  
- **Process-specific configuration**: Different strategies per process
- **Enhanced logging**: Better visibility into injection attempts

### 2. VM Security Modifications

- **Windows Defender**: Completely disabled
- **UAC**: Disabled
- **DEP**: Configured for system processes
- **ASLR**: Disabled
- **Control Flow Guard**: Disabled
- **Process protection**: Reduced for COM+ applications

### 3. Process-specific Handling

#### dllhost.exe (COM+ Application Host)
- **Priority**: High
- **Retry count**: 5 attempts
- **Method**: Enhanced injection with retry
- **Common issues**: COM+ protection, multiple instances

#### svchost.exe (Service Host Process)
- **Priority**: High  
- **Retry count**: 3 attempts
- **Method**: Standard injection with retry
- **Common issues**: Windows service protection

#### explorer.exe (Windows Shell)
- **Priority**: Medium
- **Retry count**: 2 attempts
- **Method**: Standard injection
- **Common issues**: Critical system process protection

## Troubleshooting

### Vẫn không hook được dllhost.exe

1. **Kiểm tra COM+ settings**:
   ```cmd
   dcomcnfg.exe
   ```

2. **Disable COM+ security**:
   ```cmd
   reg add "HKLM\SOFTWARE\Classes\AppID" /v "DllSurrogate" /t REG_SZ /d "" /f
   ```

3. **Check process instances**:
   ```cmd
   tasklist /svc | findstr dllhost
   ```

### Vẫn không hook được svchost.exe

1. **Identify specific services**:
   ```cmd
   tasklist /svc | findstr svchost
   ```

2. **Target specific svchost instances**:
   - Focus on non-critical services
   - Avoid system-critical svchost processes

3. **Check service permissions**:
   ```cmd
   sc sdshow <service_name>
   ```

### Agent không có SYSTEM privileges

1. **Reinstall agent as SYSTEM**:
   ```cmd
   sc delete CAPEv2Agent
   sc create CAPEv2Agent binPath= "C:\path\to\agent.exe" obj= LocalSystem
   ```

2. **Grant debug privileges**:
   ```cmd
   reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v LocalAccountTokenFilterPolicy /t REG_DWORD /d 1 /f
   ```

## Verification Steps

### 1. Check Process Access

```python
import psutil
import ctypes

def check_process_access(pid):
    try:
        handle = ctypes.windll.kernel32.OpenProcess(0x1F0FFF, False, pid)
        if handle:
            ctypes.windll.kernel32.CloseHandle(handle)
            return True
    except:
        pass
    return False

# Test với dllhost.exe PIDs
for proc in psutil.process_iter(['pid', 'name']):
    if proc.info['name'].lower() == 'dllhost.exe':
        pid = proc.info['pid']
        accessible = check_process_access(pid)
        print(f"dllhost.exe PID {pid}: {'Accessible' if accessible else 'Not accessible'}")
```

### 2. Monitor Injection Attempts

Check CAPEv2 logs for injection messages:
```
grep -i "inject" /path/to/cape/logs/cuckoo.log
grep -i "dllhost\|svchost\|explorer" /path/to/cape/logs/cuckoo.log
```

### 3. Verify VM Configuration

```cmd
# Check if Windows Defender is disabled
Get-MpPreference | Select-Object DisableRealtimeMonitoring

# Check UAC status
reg query "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v EnableLUA

# Check DEP settings
bcdedit /enum | findstr nx
```

## Expected Results

Sau khi áp dụng các fixes:

- **dllhost.exe**: 70-80% success rate (một số instances vẫn có thể bị bảo vệ)
- **svchost.exe**: 60-70% success rate (tùy thuộc vào services)
- **explorer.exe**: 90%+ success rate
- **services.exe**: 80%+ success rate

## Limitations

1. **Một số system processes vẫn không thể hook được** do Windows kernel protection
2. **Performance impact**: Retry mechanisms có thể làm chậm analysis
3. **Security implications**: VM trở nên kém bảo mật hơn
4. **Windows version dependent**: Hiệu quả có thể khác nhau giữa các phiên bản Windows

## Support

Nếu vẫn gặp vấn đề:

1. Check logs trong `/path/to/cape/logs/`
2. Run test scripts để verify configuration
3. Ensure VM snapshot is clean và properly configured
4. Consider using different Windows version (Windows 7 thường dễ hook hơn Windows 10/11)

## Files Created

- `fix_process_hooking.sh` - Main fix script
- `fix_dllhost_svchost_hooking.py` - Python-specific fix
- `setup_vm_for_hooking.bat` - VM setup script
- `test_injection.py` - Enhanced injection test
- `test_system_process_hook.py` - System process test
- `SYSTEM_PROCESS_HOOKING_GUIDE.md` - This guide
