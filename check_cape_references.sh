#!/bin/bash

# Script kiểm tra tất cả references đến /opt/CAPEv2 trong hệ thống
# Sử dụng: ./check_cape_references.sh

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

function print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

function print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

function print_found() {
    echo -e "${RED}[FOUND]${NC} $1"
}

function print_section() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

OLD_CAPE_PATH="/opt/CAPEv2"

print_info "Kiểm tra tất cả references đến $OLD_CAPE_PATH trong hệ thống"
echo

print_section "1. SYSTEMD SERVICES"
SYSTEMD_DIRS=(
    "/lib/systemd/system"
    "/etc/systemd/system"
    "/usr/lib/systemd/system"
)

for dir in "${SYSTEMD_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "Checking: $dir"
        find "$dir" -name "*.service" -exec grep -l "$OLD_CAPE_PATH" {} \; 2>/dev/null | while read -r file; do
            print_found "Service file: $file"
            grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/  /'
        done
    fi
done
echo

print_section "2. CRONTAB"
echo "Checking system crontab..."
if crontab -l 2>/dev/null | grep -q "$OLD_CAPE_PATH"; then
    print_found "User crontab contains references:"
    crontab -l 2>/dev/null | grep -n "$OLD_CAPE_PATH" | sed 's/^/  /'
fi

echo "Checking root crontab..."
if sudo crontab -l 2>/dev/null | grep -q "$OLD_CAPE_PATH"; then
    print_found "Root crontab contains references:"
    sudo crontab -l 2>/dev/null | grep -n "$OLD_CAPE_PATH" | sed 's/^/  /'
fi

echo "Checking /etc/crontab..."
if [ -f "/etc/crontab" ] && grep -q "$OLD_CAPE_PATH" /etc/crontab; then
    print_found "/etc/crontab contains references:"
    grep -n "$OLD_CAPE_PATH" /etc/crontab | sed 's/^/  /'
fi

echo "Checking /etc/cron.d/..."
if [ -d "/etc/cron.d" ]; then
    find /etc/cron.d -type f -exec grep -l "$OLD_CAPE_PATH" {} \; 2>/dev/null | while read -r file; do
        print_found "Cron file: $file"
        grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/  /'
    done
fi
echo

print_section "3. NGINX CONFIGURATION"
NGINX_DIRS=(
    "/etc/nginx/sites-available"
    "/etc/nginx/sites-enabled"
    "/etc/nginx/conf.d"
    "/etc/nginx"
)

for dir in "${NGINX_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "Checking: $dir"
        find "$dir" -type f \( -name "*.conf" -o -name "*cape*" \) -exec grep -l "$OLD_CAPE_PATH" {} \; 2>/dev/null | while read -r file; do
            print_found "Nginx config: $file"
            grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/  /'
        done
    fi
done
echo

print_section "4. UWSGI CONFIGURATION"
UWSGI_DIRS=(
    "/etc/uwsgi/apps-available"
    "/etc/uwsgi/apps-enabled"
    "/etc/uwsgi"
)

for dir in "${UWSGI_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "Checking: $dir"
        find "$dir" -type f -name "*.ini" -exec grep -l "$OLD_CAPE_PATH" {} \; 2>/dev/null | while read -r file; do
            print_found "UWSGI config: $file"
            grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/  /'
        done
    fi
done
echo

print_section "5. APPARMOR PROFILES"
APPARMOR_DIRS=(
    "/etc/apparmor.d"
)

for dir in "${APPARMOR_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "Checking: $dir"
        find "$dir" -type f -exec grep -l "$OLD_CAPE_PATH" {} \; 2>/dev/null | while read -r file; do
            print_found "AppArmor profile: $file"
            grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/  /'
        done
    fi
done
echo

print_section "6. LOGROTATE CONFIGURATION"
LOGROTATE_DIRS=(
    "/etc/logrotate.d"
)

for dir in "${LOGROTATE_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "Checking: $dir"
        find "$dir" -type f -exec grep -l "$OLD_CAPE_PATH" {} \; 2>/dev/null | while read -r file; do
            print_found "Logrotate config: $file"
            grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/  /'
        done
    fi
done
echo

print_section "7. SUDOERS FILES"
SUDOERS_DIRS=(
    "/etc/sudoers.d"
)

for dir in "${SUDOERS_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "Checking: $dir"
        find "$dir" -type f -exec grep -l "$OLD_CAPE_PATH" {} \; 2>/dev/null | while read -r file; do
            print_found "Sudoers file: $file"
            grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/  /'
        done
    fi
done

if grep -q "$OLD_CAPE_PATH" /etc/sudoers 2>/dev/null; then
    print_found "/etc/sudoers contains references:"
    grep -n "$OLD_CAPE_PATH" /etc/sudoers | sed 's/^/  /'
fi
echo

print_section "8. ENVIRONMENT FILES"
ENV_FILES=(
    "/etc/environment"
    "/etc/profile"
    "/etc/bash.bashrc"
    "/root/.bashrc"
    "/home/<USER>/.bashrc"
)

for pattern in "${ENV_FILES[@]}"; do
    for file in $pattern; do
        if [ -f "$file" ] && grep -q "$OLD_CAPE_PATH" "$file" 2>/dev/null; then
            print_found "Environment file: $file"
            grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/  /'
        fi
    done
done
echo

print_section "9. PYTHON/POETRY CONFIGS"
PYTHON_CONFIGS=(
    "/etc/poetry"
    "/root/.config/pypoetry"
    "/home/<USER>/.config/pypoetry"
)

for pattern in "${PYTHON_CONFIGS[@]}"; do
    for dir in $pattern; do
        if [ -d "$dir" ]; then
            find "$dir" -type f -exec grep -l "$OLD_CAPE_PATH" {} \; 2>/dev/null | while read -r file; do
                print_found "Python config: $file"
                grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/  /'
            done
        fi
    done
done
echo

print_section "10. DATABASE CONFIGS"
DB_CONFIGS=(
    "/etc/postgresql"
    "/etc/mysql"
    "/etc/mongodb"
)

for dir in "${DB_CONFIGS[@]}"; do
    if [ -d "$dir" ]; then
        echo "Checking: $dir"
        find "$dir" -type f -exec grep -l "$OLD_CAPE_PATH" {} \; 2>/dev/null | while read -r file; do
            print_found "Database config: $file"
            grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/  /'
        done
    fi
done
echo

print_section "11. INIT SCRIPTS"
INIT_DIRS=(
    "/etc/init.d"
    "/etc/rc.local"
)

for item in "${INIT_DIRS[@]}"; do
    if [ -f "$item" ] && grep -q "$OLD_CAPE_PATH" "$item" 2>/dev/null; then
        print_found "Init file: $item"
        grep -n "$OLD_CAPE_PATH" "$item" | sed 's/^/  /'
    elif [ -d "$item" ]; then
        find "$item" -type f -exec grep -l "$OLD_CAPE_PATH" {} \; 2>/dev/null | while read -r file; do
            print_found "Init script: $file"
            grep -n "$OLD_CAPE_PATH" "$file" | sed 's/^/  /'
        done
    fi
done
echo

print_section "12. SYMBOLIC LINKS"
echo "Checking for symbolic links pointing to $OLD_CAPE_PATH..."
find /usr /etc /opt /var -type l -lname "*$OLD_CAPE_PATH*" 2>/dev/null | while read -r link; do
    target=$(readlink "$link")
    print_found "Symbolic link: $link -> $target"
done
echo

print_section "SUMMARY"
print_info "Kiểm tra hoàn tất!"
print_info ""
print_warning "Các file cần cập nhật khi đổi vị trí CAPEv2:"
print_warning "1. Systemd service files"
print_warning "2. Crontab entries"
print_warning "3. Nginx configurations"
print_warning "4. UWSGI configurations"
print_warning "5. AppArmor profiles"
print_warning "6. Logrotate configurations"
print_warning "7. Sudoers files"
print_warning "8. Environment variables"
print_warning "9. Symbolic links"
print_info ""
print_info "Sử dụng script update_cape_services.sh để cập nhật tự động"
