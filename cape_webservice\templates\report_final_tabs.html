                    <!-- MITRE ATT&CK Tab -->
                    {% if report.mitre_attck %}
                    <div class="tab-pane fade" id="mitre" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-crosshairs me-2"></i>MITRE ATT&CK Framework ({{ report.mitre_attck|length }} techniques)</h4>
                            <div class="row">
                                {% for technique in report.mitre_attck %}
                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <span class="badge bg-danger me-2">{{ technique.technique_id if technique.technique_id else 'N/A' }}</span>
                                                {{ technique.technique if technique.technique else 'Unknown Technique' }}
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>Tactic:</strong> {{ technique.tactic if technique.tactic else 'N/A' }}</p>
                                            <p><strong>Description:</strong> {{ technique.description if technique.description else 'No description available' }}</p>
                                            {% if technique.references %}
                                            <p><strong>References:</strong>
                                                {% for ref in technique.references %}
                                                <a href="{{ ref }}" target="_blank" class="badge bg-info me-1">{{ ref }}</a>
                                                {% endfor %}
                                            </p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- TTPs Tab -->
                    {% if report.ttps %}
                    <div class="tab-pane fade" id="ttps" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-tactics me-2"></i>Tactics, Techniques & Procedures ({{ report.ttps|length }})</h4>
                            <div class="accordion" id="ttpsAccordion">
                                {% for ttp in report.ttps %}
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="ttp{{ loop.index }}">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                data-bs-target="#ttpCollapse{{ loop.index }}">
                                            <span class="badge bg-warning me-2">{{ ttp.category if ttp.category else 'TTP' }}</span>
                                            {{ ttp.description if ttp.description else ttp.name if ttp.name else 'Unknown TTP' }}
                                        </button>
                                    </h2>
                                    <div id="ttpCollapse{{ loop.index }}" class="accordion-collapse collapse" 
                                         data-bs-parent="#ttpsAccordion">
                                        <div class="accordion-body">
                                            <pre class="bg-light p-2"><code>{{ ttp | tojson(indent=2) }}</code></pre>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Memory Tab -->
                    {% if report.memory %}
                    <div class="tab-pane fade" id="memory" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-memory me-2"></i>Memory Analysis</h4>
                            
                            {% if report.memory.pslist %}
                            <div class="mb-4">
                                <h5><i class="fas fa-list me-2"></i>Process List</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>PID</th>
                                                <th>PPID</th>
                                                <th>Process Name</th>
                                                <th>Threads</th>
                                                <th>Handles</th>
                                                <th>Start Time</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for process in report.memory.pslist %}
                                            <tr>
                                                <td><code>{{ process.pid }}</code></td>
                                                <td><code>{{ process.ppid }}</code></td>
                                                <td><strong>{{ process.name }}</strong></td>
                                                <td>{{ process.threads if process.threads else 'N/A' }}</td>
                                                <td>{{ process.handles if process.handles else 'N/A' }}</td>
                                                <td><small>{{ process.start_time if process.start_time else 'N/A' }}</small></td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}

                            {% if report.memory.malfind %}
                            <div class="mb-4">
                                <h5><i class="fas fa-search me-2"></i>Malicious Code Injection (Malfind)</h5>
                                <div class="accordion" id="malfindAccordion">
                                    {% for finding in report.memory.malfind %}
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="malfind{{ loop.index }}">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                    data-bs-target="#malfindCollapse{{ loop.index }}">
                                                PID {{ finding.pid }} - {{ finding.process }} - Address: {{ finding.address }}
                                            </button>
                                        </h2>
                                        <div id="malfindCollapse{{ loop.index }}" class="accordion-collapse collapse" 
                                             data-bs-parent="#malfindAccordion">
                                            <div class="accordion-body">
                                                <pre class="bg-light p-2"><code>{{ finding.hexdump if finding.hexdump else finding | tojson(indent=2) }}</code></pre>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}

                            {% if report.memory.yarascan %}
                            <div class="mb-4">
                                <h5><i class="fas fa-scanner me-2"></i>Yara Scan Results</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Rule</th>
                                                <th>PID</th>
                                                <th>Process</th>
                                                <th>Address</th>
                                                <th>Matches</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for scan in report.memory.yarascan %}
                                            <tr>
                                                <td><span class="badge bg-warning">{{ scan.rule }}</span></td>
                                                <td><code>{{ scan.pid }}</code></td>
                                                <td><strong>{{ scan.process }}</strong></td>
                                                <td><code>{{ scan.address }}</code></td>
                                                <td>{{ scan.matches if scan.matches else 'N/A' }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Process Monitor Tab -->
                    {% if report.procmon %}
                    <div class="tab-pane fade" id="procmon" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-tasks me-2"></i>Process Monitor ({{ report.procmon|length }} events)</h4>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Time</th>
                                            <th>Process</th>
                                            <th>PID</th>
                                            <th>Operation</th>
                                            <th>Path</th>
                                            <th>Result</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for event in report.procmon[:1000] %}
                                        <tr>
                                            <td><small>{{ event.time if event.time else 'N/A' }}</small></td>
                                            <td><strong>{{ event.process_name if event.process_name else 'N/A' }}</strong></td>
                                            <td><code>{{ event.pid if event.pid else 'N/A' }}</code></td>
                                            <td><span class="badge bg-info">{{ event.operation if event.operation else 'N/A' }}</span></td>
                                            <td><small>{{ event.path if event.path else 'N/A' }}</small></td>
                                            <td><span class="badge bg-{{ 'success' if event.result == 'SUCCESS' else 'warning' }}">{{ event.result if event.result else 'N/A' }}</span></td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% if report.procmon|length > 1000 %}
                            <p class="text-muted">Showing first 1000 events out of {{ report.procmon|length }} total events.</p>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Process Memory Tab -->
                    {% if report.procmemory %}
                    <div class="tab-pane fade" id="procmemory" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-microchip me-2"></i>Process Memory Dumps ({{ report.procmemory|length }})</h4>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>PID</th>
                                            <th>Process</th>
                                            <th>File</th>
                                            <th>Size</th>
                                            <th>Yara Matches</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for memory in report.procmemory %}
                                        <tr>
                                            <td><code>{{ memory.pid if memory.pid else 'N/A' }}</code></td>
                                            <td><strong>{{ memory.process if memory.process else 'N/A' }}</strong></td>
                                            <td><small>{{ memory.file if memory.file else 'N/A' }}</small></td>
                                            <td>{{ "{:,}".format(memory.size) if memory.size else 'N/A' }} bytes</td>
                                            <td>
                                                {% if memory.yara %}
                                                {% for yara in memory.yara %}
                                                <span class="badge bg-warning me-1">{{ yara.name }}</span>
                                                {% endfor %}
                                                {% else %}
                                                N/A
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- URL Analysis Tab -->
                    {% if report.url_analysis %}
                    <div class="tab-pane fade" id="url" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-link me-2"></i>URL Analysis</h4>
                            <div class="accordion" id="urlAccordion">
                                {% for url in report.url_analysis %}
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="url{{ loop.index }}">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                data-bs-target="#urlCollapse{{ loop.index }}">
                                            {{ url.url if url.url else 'URL Analysis' }}
                                        </button>
                                    </h2>
                                    <div id="urlCollapse{{ loop.index }}" class="accordion-collapse collapse" 
                                         data-bs-parent="#urlAccordion">
                                        <div class="accordion-body">
                                            <pre class="bg-light p-2"><code>{{ url | tojson(indent=2) }}</code></pre>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Screenshots Tab -->
                    {% if report.shots %}
                    <div class="tab-pane fade" id="shots" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-camera me-2"></i>Screenshots ({{ report.shots|length }})</h4>
                            <div class="row">
                                {% for shot in report.shots %}
                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">Screenshot {{ loop.index }}</h6>
                                        </div>
                                        <div class="card-body text-center">
                                            {% if shot.path %}
                                            <img src="{{ shot.path }}" class="img-fluid" alt="Screenshot {{ loop.index }}" style="max-height: 300px;">
                                            {% else %}
                                            <p class="text-muted">Screenshot path not available</p>
                                            {% endif %}
                                            {% if shot.timestamp %}
                                            <p class="text-muted mt-2"><small>Taken at: {{ shot.timestamp }}</small></p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Malware Score Tab -->
                    {% if report.malscore %}
                    <div class="tab-pane fade" id="malscore" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-calculator me-2"></i>Malware Score Analysis</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h2 class="text-danger">{{ report.malscore.score if report.malscore.score else 'N/A' }}</h2>
                                            <p>Overall Malware Score</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            {% for key, value in report.malscore.items() %}
                                            <tr>
                                                <th>{{ key.replace('_', ' ').title() }}</th>
                                                <td>{{ value }}</td>
                                            </tr>
                                            {% endfor %}
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Malware Status Tab -->
                    {% if report.malstatus %}
                    <div class="tab-pane fade" id="malstatus" role="tabpanel">
                        <div class="report-section">
                            <h4><i class="fas fa-flag me-2"></i>Malware Status</h4>
                            <div class="accordion" id="malstatusAccordion">
                                {% for status in report.malstatus %}
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="malstatus{{ loop.index }}">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                data-bs-target="#malstatusCollapse{{ loop.index }}">
                                            {{ status.name if status.name else 'Malware Status' }} - {{ status.status if status.status else 'Unknown' }}
                                        </button>
                                    </h2>
                                    <div id="malstatusCollapse{{ loop.index }}" class="accordion-collapse collapse" 
                                         data-bs-parent="#malstatusAccordion">
                                        <div class="accordion-body">
                                            <pre class="bg-light p-2"><code>{{ status | tojson(indent=2) }}</code></pre>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
